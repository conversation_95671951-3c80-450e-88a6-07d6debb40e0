package com.dyd.jdy.bean.four;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 生产图设计-技术电气
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ProductionElectrical {

    /**
     * 料号
     */
    private  String _id;
    /**
     * 生产图设计-技术电气
     */
   private  String _widget_1612859892361;

   private  String  _widget_1612859892362;

   private  int  _widget_1670826370892;

   private  String _widget_1612859892363;

   private  int _widget_1612859892364;

   private  String _widget_1629715286558;

    private  int _widget_1679964904426;

}
