package com.dyd.system.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;

public class CommentVO {

    @Schema(description = "自增编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "28969")
    private Long id;

    @Schema(description = "业务类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer bizType;

    @Schema(description = "业务唯一编码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String bizKey;

    @Schema(description = "父级业务编号", example = "3428")
    private Long parentId;

    @Schema(description = "评论内容")
    private String bizContent;

    @Schema(description = "评论人编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "31364")
    private Long userId;

    @Schema(description = "评论人名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "31364")
    private String userName;

    @Schema(description = "删除标记", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean delFlag;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

    @Schema(description = "创建人", requiredMode = Schema.RequiredMode.REQUIRED)
    private String createBy;

    @Schema(description = "修改人", requiredMode = Schema.RequiredMode.REQUIRED)
    private String updateBy;

}
