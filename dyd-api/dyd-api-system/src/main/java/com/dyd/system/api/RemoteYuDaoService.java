package com.dyd.system.api;

import com.dyd.common.core.constant.SecurityConstants;
import com.dyd.common.core.domain.R;
import com.dyd.system.api.vo.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@FeignClient(contextId = "remoteYuDaoService", url = "http://127.0.0.1:48080", value = "yuDao"
//        , fallbackFactory = RemoteDictDataFallbackFactory.class
)
public interface RemoteYuDaoService {

    @PostMapping("/bpm/inner/process-instance/create")
    R<String> createApproval(@RequestBody BpmProcessInstanceCreateReqVO createReqVO,
                             @RequestHeader(SecurityConstants.FROM_SOURCE)Long userId ,
                             @RequestHeader(value = "from-source-v") String username);

    @GetMapping("/bpm/inner/process-instance/list-by-process-instance-id")
    R<List<BpmTaskRespVO>> getTaskListByProcessInstanceId(@RequestParam("processInstanceId") String processInstanceId);

    @PostMapping("/bpm/inner/task/approve")
    R<Boolean> approveTask(@RequestBody BpmTaskApproveReqVO reqVO, @RequestHeader("from-source") Long userId, @RequestHeader(value = "from-source-v") String username);

    @PostMapping("/bpm/inner/task/reject")
    R<Boolean> rejectTask(@RequestBody BpmTaskRejectReqVO reqVO, @RequestHeader("from-source") Long userId, @RequestHeader(value = "from-source-v") String username);

    @PostMapping("/bpm/inner/process-instance/get-approval-detail")
    R<BpmApprovalDetailRespVO> getApprovalDetail(@RequestBody BpmApprovalDetailReqVO reqVO, @RequestHeader("from-source") Long userId, @RequestHeader(value = "from-source-v") String username);


}
