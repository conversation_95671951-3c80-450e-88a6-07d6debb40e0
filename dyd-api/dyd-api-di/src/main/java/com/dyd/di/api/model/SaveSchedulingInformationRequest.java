package com.dyd.di.api.model;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class SaveSchedulingInformationRequest {
    /**
     * 方案排期
     */
    private List<PreSaleSchedulingDTO> list = new ArrayList<>();
    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 系统工程师
     */
    private String systemsEngineer;

    /**
     * 是否统一排期
     */
    private Boolean unifiedScheduling = false;

    /**
     * 是否完成
     */
    private boolean finished = false;
}
