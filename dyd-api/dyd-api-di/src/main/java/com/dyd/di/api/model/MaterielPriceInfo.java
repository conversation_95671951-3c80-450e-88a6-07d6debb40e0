package com.dyd.di.api.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.dyd.common.core.annotation.Excel;
import com.dyd.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 物料价格对象 di_materiel_price
 *
 * <AUTHOR>
 * @date 2024-06-19
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MaterielPriceInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 物料ID/产品ID
     */
    @Excel(name = "物料ID/产品ID")
    private Long materielId;

    /**
     * 物料号
     */
    @Excel(name = "物料号")
    private String materielNo;

    /**
     * 0物料  1产品
     */
    @Excel(name = "0物料  1产品")
    private Long materielType;

    /**
     * 指导物料费
     */
    @Excel(name = "指导物料费")
    private BigDecimal guideMaterialCosts;

    /**
     * 指导制作费
     */
    @Excel(name = "指导制作费")
    private BigDecimal guideProductionCosts;

    /**
     * 指导包装费
     */
    @Excel(name = "指导包装费")
    private BigDecimal guidePackagingCosts;

    /**
     * 指导安装费
     */
    @Excel(name = "指导安装费")
    private BigDecimal guideInstallationCosts;

    /**
     * 成本物料费
     */
    @Excel(name = "成本物料费")
    private BigDecimal costMaterialCosts;

    /**
     * 成本制作费
     */
    @Excel(name = "成本制作费")
    private BigDecimal costProductionCosts;

    /**
     * 成本包装费
     */
    @Excel(name = "成本包装费")
    private BigDecimal costPackagingCosts;

    /**
     * 成本安装费
     */
    @Excel(name = "成本安装费")
    private BigDecimal costInstallationCosts;

    /**
     * 2删除 0 正常
     */
    private Long delFlag;

    /**
     * 链路id
     */
    @Excel(name = "链路id")
    private String traceId;

    /**
     * 指导生产费
     */
    private BigDecimal guideProduceCosts;

    /**
     * 指导风险费
     */
    private BigDecimal guideRiskCosts;

    /**
     * 指导其他
     */
    private BigDecimal guideOtherCosts;

    /**
     * 合计指导
     */
    private BigDecimal guideSumCosts;

    /**
     * 指导工期
     */
    private BigDecimal guideDuration;

    /**
     * 指导物料货期
     */
    private BigDecimal guideMaterielDuration;

    /**
     * 指导生产工期
     */
    private BigDecimal guideProduceDuration;

    /**
     * 成本生产费
     */
    private BigDecimal costProduceCosts;

    /**
     * 成本风险费
     */
    private BigDecimal costRiskCosts;

    /**
     * 成本其他
     */
    private BigDecimal costOtherCosts;

    /**
     * 合计成本
     */
    private BigDecimal costSumCosts;

    /**
     * 标准工期
     */
    private BigDecimal costDuration;

    /**
     * 标准物料货期
     */
    private BigDecimal costMaterielDuration;

    /**
     * 标准生产工期
     */
    private BigDecimal costProduceDuration;
}
