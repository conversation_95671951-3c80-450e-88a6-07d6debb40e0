package com.dyd.di.api;

import com.dyd.common.core.constant.ServiceNameConstants;
import com.dyd.common.core.domain.R;
import com.dyd.common.core.utils.IPage;
import com.dyd.common.core.utils.PageWrapper;
import com.dyd.common.core.web.domain.AjaxResult;
import com.dyd.common.core.web.page.TableDataInfo;
import com.dyd.di.api.factory.RemoteDiFallbackFactory;
import com.dyd.di.api.model.*;
import com.dyd.di.api.model.dto.*;
import com.dyd.di.api.model.vo.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 中间件服务
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteDiService", value = ServiceNameConstants.DI_SERVICE, fallbackFactory = RemoteDiFallbackFactory.class)
public interface RemoteDiService {

    /**
     * 小程序产品列表
     *
     * @param materielQueryPageVo 查询参数
     * @return
     */
    @PostMapping(value = "/materiel/miniProgramList")
    R<IPage<MaterielInfoDTO>> miniProgramList(@RequestBody MaterielQueryPageVo materielQueryPageVo);

    /**
     * 小程序Spu产品列表
     *
     * @param materielQueryPageVo
     * @return
     */
    @PostMapping("/materiel/miniProgramSpuList")
    R<IPage<SpuInfoDTO>> miniProgramSpuList(@RequestBody MaterielQueryPageVo materielQueryPageVo);


    /**
     * 查询比价列表
     *
     * @param materielLabelQueryVo 查询参数
     * @return
     */
    @PostMapping(value = "/materiel/queryMaterielPage")
    R<TableDataInfo> queryMaterielPage(@RequestBody MaterielLabelQueryVo materielLabelQueryVo);


    /**
     * 新增线索
     *
     * @param diMarketingClue
     * @return
     */
    @PostMapping("/clue")
    AjaxResult add(@RequestBody DiMarketingClue diMarketingClue);

    /**
     * 获取产品分类树列表
     */
    @GetMapping("/productBasic/productTypeTree")
    AjaxResult productTypeTree(@SpringQueryMap DiMarketingProductType diMarketingProductType);

    /**
     * 查询产品基础信息列表
     */
    @GetMapping("/productBasic/list")
    TableDataInfo list(@SpringQueryMap DiMarketingProductBasic diMarketingProductBasic);

    /**
     * 获取产品基础信息详细信息
     */
    @GetMapping(value = "/productBasic/{id}")
    AjaxResult getInfo(@PathVariable("id") String id);

    /**
     * @param classificationListVO
     * @return
     */
    @GetMapping("/classification/labelClassificationList")
    R<IPage<LabelClassificationListDTO>> labelClassificationList(@SpringQueryMap LabelClassificationListVO classificationListVO);

    /**
     * @param labelWarehouseListVO
     * @return
     */
    @GetMapping("/warehouse/labelWarehouseList")
    R<IPage<LabelWarehouseListDTO>> labelWarehouseList(@SpringQueryMap LabelWarehouseListVO labelWarehouseListVO);

    /**
     * 查询物料标签
     */
    @GetMapping("/materiel/warehouse/materielLabelList")
    R<IPage<LabelMaterielWarehouseListDTO>> materielLabelList(@SpringQueryMap LabelMaterielWarehouseListVO materielWarehouseListVO);

    /**
     * 查询标签配置方案
     *
     * @param pageVo
     * @return
     */
    @PostMapping("/materiel/v2/queryMaterielPage")
    R<IPage<MaterielInfoDTO>> queryMaterielPageV2(@RequestBody MaterielQueryPageVo pageVo);

    /**
     * 小程序Spu产品详情
     *
     * @param spuInfoVO
     * @return
     */
    @GetMapping("/spu/spuInfo")
    R<SpuDetailDTO> spuInfo(@SpringQueryMap SpuInfoVO spuInfoVO);

    /**
     * 小程序产品详情
     *
     * @param materielDetailVo
     * @return
     */
    @GetMapping("/materiel/productDetail")
    R<MaterielDetailDTO> productDetail(@SpringQueryMap MaterielDetailVo materielDetailVo);

    /**
     * @param classificationListVO
     * @return
     */
    @GetMapping("/classification/classificationList")
    R<List<LabelClassificationListDTO>> classificationList(@SpringQueryMap ClassificationListVO classificationListVO);


    /**
     * 查询物料标签
     */
    @GetMapping("/materiel/warehouse/materielLabel")
    R<IPage<LabelMaterielWarehouseListDTO>> materielLabel(@SpringQueryMap LabelMaterielWarehouseListVO materielWarehouseListVO);


    /**
     * 物料详情
     *
     * @param materielNo
     * @return
     */
    @GetMapping("/materiel/materielInfoSimple")
    R<MaterielInfoDTO> materielInfoSimple(@RequestParam("materielNo") String materielNo, @RequestParam("needPrice") Integer needPrice);

    /**
     * 物料详情
     *
     * @param materielId
     * @return
     */
    @GetMapping("/materiel/materielInfoSimple")
    R<MaterielInfoDTO> materielInfoSimple(@RequestParam("id") Long materielId, @RequestParam("needPrice") Integer needPrice);
    /**
     * 查询物料是否存在
     *
     * @param materielNo
     * @return
     */
    @GetMapping("/materiel/materielCount")
    R<Integer> materielCount(@RequestParam("materielNo") String materielNo);

    /**
     * 添加物料
     *
     * @param materielAddVo
     * @return
     */
    @PostMapping("/materiel/addMaterielNew")
    R<String> addMaterielNew(@Valid @RequestBody MaterielAddVo materielAddVo);

    @PostMapping("/materiel/updateMateriel")
    R updateMateriel(@RequestBody MaterielUpdateDto materielUpdateDto);

    /**
     * 添加物料价格
     *
     * @param request
     * @return
     */

    @Deprecated
    @PostMapping("/materielPrice/addMaterielPrice")
    R addMaterielPrice(@RequestBody MaterielPriceAddApiRequest request);

    /**
     * 更新物料bom
     *
     * @param diMateriel
     * @return
     */
    @PostMapping("/materiel/synBom")
    R synBom(@RequestBody MaterielUpdateDto diMateriel);

    /**
     * 修改物料价格
     *
     * @param request
     * @return
     */
    @PostMapping("/materielPrice/edit")
    R editPrice(@RequestBody MaterielPriceAddApiRequest request);

    /**
     * 分页查询物料
     *
     * @param pageNum
     * @param pageSize
     * @return
     */
    @GetMapping("/materiel/selectMaterielPageList")
    R<PageWrapper<List<DiMaterielList>>> selectMaterielPageList(@RequestParam("pageNum") Integer pageNum, @RequestParam("pageSize") Integer pageSize, @RequestParam(value = "materielNo", required = false) String materielNo);


//    /**
//     * 添加bom
//     *
//     * @param materielBomUpdateVo
//     * @return
//     */
//    @PostMapping("/materielBom/addBatchBom")
//    R addBatchBom(@RequestBody MaterielBomUpdateVo materielBomUpdateVo);


    /**
     * 根据物料id查询价格详情
     *
     * @param id
     * @return
     */
    @GetMapping("/materielPrice/getPriceInfo")
    R<MaterielPriceInfo> getPriceInfo(@RequestParam("id") Long id);


    /**
     * 获取bom详情
     *
     * @param parentMaterielId
     * @param materielId
     * @return
     */
    @GetMapping("/materielBom/getBomInfo")
    R<List<MaterielBomInfo>> getBomInfo(@RequestParam("parentMaterielId") Long parentMaterielId, @RequestParam("materielId") Long materielId);

    /**
     * 新增客户
     *
     * @param diMarketingCustomer
     * @return
     */
    @PostMapping("/customer")
    R add(@RequestBody DiMarketingCustomer diMarketingCustomer);

    /**
     * 修改客户
     *
     * @param diMarketingCustomer
     * @return
     */
    @PutMapping("/customer")
    AjaxResult edit(@RequestBody @Valid DiMarketingCustomer diMarketingCustomer);

    @PostMapping("/customer/updateCus")
    R updateCus(@RequestBody DiMarketingCustomer diMarketingCustomer);

    /**
     * 校验客户唯一
     *
     * @param diMarketingCustomer
     * @return
     */
    @PostMapping("/customer/verifyUniqueness")
    R verifyUniqueness(@RequestBody DiMarketingCustomer diMarketingCustomer);

    /**
     * 获取客户
     *
     * @param id
     * @return
     */
    @GetMapping(value = "/customer/{id}")
    AjaxResult getCusInfo(@PathVariable("id") String id);

    @GetMapping("/customer/getInfoByNo")
    R<MarketingCustomerResponse> getInfoByNo(@RequestParam("no") String no);

    /**
     * 客户跟进记录
     *
     * @param request
     * @return
     */
    @PostMapping("/follow/addCustomerFollow")
    R addCustomerFollow(@RequestBody DiMarketingFollowVo request);

    /**
     * 删除客户记录
     *
     * @param customerId
     * @return
     */
    @PostMapping("/follow/deleteCustomerFollow")
    R deleteCustomerFollow(@RequestParam("customerId") String customerId);


    /**
     * 应该删除
     *
     * @param request
     * @return
     */
    @Deprecated
    @PostMapping("/materiel/saveMaterielVersion")
    R<Long> saveMaterielVersion(@RequestBody MaterielVersionAddRequest request);


    /**
     * 需要删除
     *
     * @param materielId
     * @return
     */
    @Deprecated
    @GetMapping("/materiel/queryMaterielVersion")
    R<Long> queryMaterielVersion(@RequestParam("materielId") Long materielId);


    @GetMapping("/materielBom/bomByVersion")
    R<MaterielVersionBomMqResponse> bomByVersion(@RequestParam("materielVersionId") Long materielVersionId);


    @PostMapping("/u9/insertDiSynErpLog")
    R insertDiSynErpLog(@RequestBody AddSynErpLogRequest addSynErpLogRequest);


    @PostMapping("/sys/category/deleteCategory")
    R<Long> deleteCategory(@RequestBody SysCategoryAddRequest request);

    @PostMapping("/sys/category/addCategory")
    R<Long> addCategory(@RequestBody SysCategoryAddRequest request);

    @PostMapping("/u9/inserMaterielVersion")
    R inserMaterielVersion(@RequestBody AddMaterialVersionRequest request);

    @GetMapping("/u9/selectMaterielVersion")
    R<Long> selectMaterielVersion(@RequestParam("materielNo") String materielNo, @RequestParam("version") String version);


    @GetMapping("/processProject/projectProcessSynJdy")
    R<PageWrapper<List<ProjectProcessSynJdyResponse>>> projectProcessSynJdy(@RequestParam("pageNum") Integer pageNum,@RequestParam("pageSize") Integer pageSize);


    /**
     * 项目
     * @param pageNum
     * @param pageSize
     * @return
     */
    @GetMapping("/processProject/getProjects")
    R<PageWrapper<List<ProjectProcessSynJdyResponse>>> getProjects(@RequestParam("pageNum") Integer pageNum, @RequestParam("pageSize") Integer pageSize);
    /**
     * 商机
     * @param pageNum
     * @param pageSize
     * @return
     */
    @GetMapping("/niche/getNichePage")
    R<PageWrapper<List<DiMarketingNicheListResponse>>> getNichePage(@RequestParam("pageNum") Integer pageNum,@RequestParam("pageSize") Integer pageSize);

    /**
     * 获取客户
     * @param pageNum
     * @param pageSize
     * @param customerNos
     * @return
     */
    @GetMapping("/customer/getCusListPage")
    R<PageWrapper<List<DiMarketingCustomerListResponse>>> getCusListPage(@RequestParam("pageNum") Integer pageNum,@RequestParam("pageSize") Integer pageSize,@RequestParam(value = "customerNos",required = false) List<String> customerNos);

    @PostMapping("/contacts/synAddCusContact")
    R synAddCusContact(@RequestBody List<DiMarketingContactsVo> diMarketingContactsVos);


    @GetMapping("/materielBom/loadSubMateriel")
    R<Boolean> loadSubMateriel(@RequestParam("materielId") Long materielId);

    @GetMapping("/bi/getDeptRealData")
    R<List<DeptRealDateResponse>> getDeptRealData(@RequestParam("month") String month,@RequestParam("bu") String bu);

    @GetMapping("/bi/getSalerMonthDept")
    R<List<DeptRealDateResponse>> getSalerMonthDept(@RequestParam("month") String month,@RequestParam("saler") String saler);

    /**
     * 更新物料同步状态
     *
     * @param request
     * @return
     */
    @PostMapping(value = "/materiel/updateSyncStatus")
    public R updateSyncStatus(@RequestBody UpdateSyncStatusRequest request);

    /**
     * 更新订单账期
     * @param orderBillPeriodRequest
     * @return
     */
    @PostMapping("/diOrder/updateOrderPayRealDate")
    R<BillPeriodResponse> updateOrderPayRealDate(@RequestBody OrderBillPeriodRequest orderBillPeriodRequest);

    @GetMapping("/diOrder/selectOrderList")
    R<PageWrapper<List<DiOrderListPageResponse>>> selectOrderList(@RequestParam("pageNum") Integer pageNum,@RequestParam("pageSize") Integer pageSize);

    @GetMapping("/diOrder/selectOrderListByTime")
    R<List<DiOrderListResponse>> selectOrderListByTime(@RequestParam(value = "startTime",required = false) String startTime,@RequestParam(value = "endTime",required = false) String endTime);

    @GetMapping("/preSaleQuote/selectGp")
    R<QuoteGpResponse> selectGp(@RequestParam("nicheNo") String nicheNo);


    @GetMapping("/contract/selectContract")
    R<ContractApiResponse> selectContract(@RequestParam("projectNo") String projectNo);

    /**
     * 产品方案
     * @param
     * @return
     */
    @GetMapping("/preSale/selectDiPreSaleListForRemote")
    R<PageWrapper<List<PreSalesPageResponse>>> selectDiPreSaleListForRemote(@RequestParam("pageNum") Integer pageNum,@RequestParam("pageSize") Integer pageSize);

    /**
     * 更新产品清单领料单号
     * @param request
     * @return
     */
    @PostMapping("/preSale/updatePreSaleManifest")
    R updatePreSaleManifest(@RequestBody @Validated UpdatePreSaleManifestRequest request);


    /**
     *同步线索到简道云
     * @return
     */
    @GetMapping("/clue/synClueToJdy")
    R<List<ClueToJdyResponse>> synClueToJdy();


    /**
     * 查询机械设计外形图不为空的项目
     * @return
     */
    @GetMapping("/project/queryProjectPreSale")
    R<List<PreSaleAppearanceResponse>> queryProjectPreSale();


    /**
     * 同步DI客户到u9
     * @param customerCode
     * @return
     */
    @GetMapping("/u9/synCusToU9")
    R synCusToU9(@RequestParam(value = "customerCode",required = false) String customerCode);
}
