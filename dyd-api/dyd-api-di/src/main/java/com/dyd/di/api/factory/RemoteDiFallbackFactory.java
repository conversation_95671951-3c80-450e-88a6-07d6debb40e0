package com.dyd.di.api.factory;

import com.dyd.common.core.domain.R;
import com.dyd.common.core.utils.IPage;
import com.dyd.common.core.utils.PageWrapper;
import com.dyd.common.core.web.domain.AjaxResult;
import com.dyd.common.core.web.page.TableDataInfo;
import com.dyd.di.api.RemoteDiService;
import com.dyd.di.api.model.*;
import com.dyd.di.api.model.dto.*;
import com.dyd.di.api.model.vo.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 中间件降级处理
 *
 * <AUTHOR>
 */
@Component
public class RemoteDiFallbackFactory implements FallbackFactory<RemoteDiService> {

    private static final Logger log = LoggerFactory.getLogger(RemoteDiFallbackFactory.class);

    @Override
    public RemoteDiService create(Throwable throwable) {
        log.error("中间件服务调用失败:{}", throwable.getMessage());
        return new RemoteDiService() {

            @Override
            public R<IPage<MaterielInfoDTO>> miniProgramList(MaterielQueryPageVo materielQueryPageVo) {
                throw new RuntimeException("小程序产品列表查询失败！:" + throwable.getMessage());
            }

            /**
             * 小程序Spu产品列表
             *
             * @param materielQueryPageVo
             * @return
             */
            @Override
            public R<IPage<SpuInfoDTO>> miniProgramSpuList(MaterielQueryPageVo materielQueryPageVo) {
                throw new RuntimeException("小程序Spu产品列表查询失败！:" + throwable.getMessage());
            }

            @Override
            public R<TableDataInfo> queryMaterielPage(MaterielLabelQueryVo materielLabelQueryVo) {
                return R.fail("查询比价列表失败！:" + throwable.getMessage());
            }

            @Override
            public AjaxResult add(DiMarketingClue diMarketingClue) {
                return AjaxResult.error("新增线索失败" + throwable.getMessage());
            }

            @Override
            public AjaxResult productTypeTree(DiMarketingProductType diMarketingProductType) {
                return AjaxResult.error("获取产品分类树列表失败" + throwable.getMessage());
            }

            @Override
            public TableDataInfo list(DiMarketingProductBasic diMarketingProductBasic) {
                throw new RuntimeException("查询产品基础信息列表失败！:" + throwable.getMessage());
            }

            @Override
            public AjaxResult getInfo(String id) {
                return AjaxResult.error("获取产品基础信息失败" + throwable.getMessage());
            }

            /**
             * @param classificationListVO
             * @return
             */
            @Override
            public R<IPage<LabelClassificationListDTO>> labelClassificationList(LabelClassificationListVO classificationListVO) {
                return R.fail("查询分类失败！:" + throwable.getMessage());
            }

            /**
             * @param labelWarehouseListVO
             * @return
             */
            @Override
            public R<IPage<LabelWarehouseListDTO>> labelWarehouseList(LabelWarehouseListVO labelWarehouseListVO) {
                return R.fail("查询标签库失败！:" + throwable.getMessage());
            }

            /**
             * 查询物料标签
             *
             * @param materielWarehouseListVO
             */
            @Override
            public R<IPage<LabelMaterielWarehouseListDTO>> materielLabelList(LabelMaterielWarehouseListVO materielWarehouseListVO) {
                return R.fail("查询物料标签失败！:" + throwable.getMessage());
            }

            /**
             * 查询标签配置方案
             *
             * @param pageVo
             * @return
             */
            @Override
            public R<IPage<MaterielInfoDTO>> queryMaterielPageV2(MaterielQueryPageVo pageVo) {
                return R.fail("查询标签配置方案失败！:" + throwable.getMessage());
            }

            /**
             * 小程序Spu产品详情
             *
             * @param spuInfoVO
             * @return
             */
            @Override
            public R<SpuDetailDTO> spuInfo(SpuInfoVO spuInfoVO) {
                return R.fail("查询小程序产品详情失败！:" + throwable.getMessage());
            }

            /**
             * 小程序产品详情
             *
             * @param materielDetailVo
             * @return
             */
            @Override
            public R<MaterielDetailDTO> productDetail(MaterielDetailVo materielDetailVo) {
                return R.fail("查询小程序产品详情失败！:" + throwable.getMessage());
            }

            @Override
            public R<List<LabelClassificationListDTO>> classificationList(ClassificationListVO classificationListVO) {
                return R.fail("查询分类失败失败！:" + throwable.getMessage());
            }

            @Override
            public R<IPage<LabelMaterielWarehouseListDTO>> materielLabel(LabelMaterielWarehouseListVO materielWarehouseListVO) {
                return R.fail("查询物料标签失败！:" + throwable.getMessage());
            }

            /**
             * 物料详情
             *
             * @param materielId
             * @param needPrice
             * @return
             */
            @Override
            public R<MaterielInfoDTO> materielInfoSimple(Long materielId, Integer needPrice) {
                return R.fail("查询物料详情失败！:" + throwable.getMessage());
            }


            @Override
            public R<MaterielInfoDTO> materielInfoSimple(String materielNo,Integer needPrice) {
                return R.fail("查询物料详情失败！:" + throwable.getMessage());
            }

            @Override
            public R<Integer> materielCount(String materielNo) {
                return R.fail("查询物料数量失败！:" + throwable.getMessage());
            }

            @Override
            public R<String> addMaterielNew(MaterielAddVo materielAddVo) {
                return R.fail("新增物料失败！:" + throwable.getMessage());
            }

            @Override
            public R updateMateriel(MaterielUpdateDto materielUpdateDto) {
                return null;
            }

            @Override
            public R addMaterielPrice(MaterielPriceAddApiRequest request) {
                return R.fail("新增物料价格失败！:" + throwable.getMessage());
            }

            @Override
            public R synBom(MaterielUpdateDto diMateriel) {
                return R.fail("编辑物料详情失败！:" + throwable.getMessage());
            }

            @Override
            public R editPrice(MaterielPriceAddApiRequest request) {
                return R.fail("修改物料价格失败！:" + throwable.getMessage());
            }

            @Override
            public R<PageWrapper<List<DiMaterielList>>> selectMaterielPageList(Integer pageNum, Integer pageSize,String materielNo) {
                return R.fail("分页查询物料失败！:" + throwable.getMessage());
            }

//            @Override
//            public R addBatchBom(MaterielBomUpdateVo materielBomUpdateVo) {
//                return R.fail("添加物料bom失败！:" + throwable.getMessage());
//            }

            @Override
            public R<MaterielPriceInfo> getPriceInfo(Long id) {
                return R.fail("查询物料价格详情失败！:" + throwable.getMessage());
            }

            @Override
            public R<List<MaterielBomInfo>> getBomInfo(Long parentMaterielId , Long materielId) {
                return R.fail("查询物料bom详情失败！:" + throwable.getMessage());
            }

            @Override
            public R add(DiMarketingCustomer diMarketingCustomer) {
                return R.fail("新增客户失败！:" + throwable.getMessage());
            }

            @Override
            public AjaxResult edit(DiMarketingCustomer diMarketingCustomer) {
                return AjaxResult.error("编辑客户失败！:" + throwable.getMessage());
            }

            @Override
            public R updateCus(DiMarketingCustomer diMarketingCustomer) {
                return null;
            }

            @Override
            public R verifyUniqueness(DiMarketingCustomer diMarketingCustomer) {
                return R.fail("校验客户唯一失败！:" + throwable.getMessage());
            }

            @Override
            public AjaxResult getCusInfo(String id) {
                return AjaxResult.error("获取客户失败！:" + throwable.getMessage());
            }

            @Override
            public R<MarketingCustomerResponse> getInfoByNo(String no) {
                return R.fail("查询客户失败！:" + throwable.getMessage());
            }

            @Override
            public R addCustomerFollow(DiMarketingFollowVo request) {
                return R.fail("新增客户失败！:" + throwable.getMessage());
            }

            @Override
            public R deleteCustomerFollow(String customerId) {
                return R.fail("删除客户失败！:" + throwable.getMessage());
            }

            @Override
            public R<Long> saveMaterielVersion(MaterielVersionAddRequest request) {
                return R.fail("保存物料版本！:" + throwable.getMessage());
            }

            @Override
            public R<Long> queryMaterielVersion(Long materielId) {
                return R.fail("查询物料版本！:" + throwable.getMessage());
            }

            @Override
            public R<MaterielVersionBomMqResponse> bomByVersion(Long materielVersionId) {
                return R.fail("查询物料bom版本！:" + throwable.getMessage());
            }

            @Override
            public R insertDiSynErpLog(AddSynErpLogRequest addSynErpLogRequest) {
                return R.fail("新增同步u9记录报错！:" + throwable.getMessage());
            }

            @Override
            public R<Long> deleteCategory(SysCategoryAddRequest request) {
                return null;
            }

            @Override
            public R<Long> addCategory(SysCategoryAddRequest request) {
                return R.fail("分类报错！:" + throwable.getMessage());
            }

            @Override
            public R inserMaterielVersion(AddMaterialVersionRequest request) {
                return R.fail("版本映射报错！:" + throwable.getMessage());
            }

            @Override
            public R<Long> selectMaterielVersion(String materielNo, String version) {
                return R.fail("查询物料相同版本报错！:" + throwable.getMessage());
            }

            @Override
            public R<PageWrapper<List<ProjectProcessSynJdyResponse>>> projectProcessSynJdy(Integer pageNum, Integer pageSize) {
                return R.fail("查询项目报错！:" + throwable.getMessage());
            }

            @Override
            public R<PageWrapper<List<ProjectProcessSynJdyResponse>>> getProjects(Integer pageNum, Integer pageSize) {
                return R.fail("查询项目报错！:" + throwable.getMessage());
            }

            @Override
            public R<PageWrapper<List<DiMarketingNicheListResponse>>> getNichePage(Integer pageNum, Integer pageSize) {
                return  R.fail("查询商机报错！:" + throwable.getMessage());
            }

            @Override
            public R<PageWrapper<List<DiMarketingCustomerListResponse>>> getCusListPage(Integer pageNum, Integer pageSize,List<String> customerNos) {
                return R.fail("查询客户报错！:" + throwable.getMessage());
            }

            @Override
            public R synAddCusContact(List<DiMarketingContactsVo> diMarketingContactsVos) {
                return R.fail("同步客户报错！:" + throwable.getMessage());
            }

            @Override
            public R<Boolean> loadSubMateriel(Long materielId) {
                return null;
            }

            @Override
            public R<List<DeptRealDateResponse>> getDeptRealData(String month,String bu) {
                return R.fail("同步部门月实际值报错！:" + throwable.getMessage());
            }

            @Override
            public R<List<DeptRealDateResponse>> getSalerMonthDept(String month,String saler) {
                return R.fail("同步销售员月实际值报错！:" + throwable.getMessage());
            }

            /**
             * 更新物料同步状态
             *
             * @param request
             * @return
             */
            @Override
            public R updateSyncStatus(UpdateSyncStatusRequest request) {
                return R.fail("更新物料同步状态报错！:" + throwable.getMessage());
            }

            @Override
            public R<BillPeriodResponse> updateOrderPayRealDate(OrderBillPeriodRequest orderBillPeriodRequest) {
                return R.fail("更新实际日期报错！:" + throwable.getMessage());
            }

            @Override
            public R<PageWrapper<List<DiOrderListPageResponse>>> selectOrderList(Integer pageNum, Integer pageSize) {
                return R.fail("分页查询订单报错！:" + throwable.getMessage());
            }

            @Override
            public R<List<DiOrderListResponse>> selectOrderListByTime(String startTime, String endTime) {
                return R.fail("时间查询订单！:" + throwable.getMessage());
            }

            @Override
            public R<QuoteGpResponse> selectGp(String nicheNo) {
                return R.fail("查询报价单gp！:" + throwable.getMessage());
            }

            @Override
            public R<ContractApiResponse> selectContract(String projectNo) {
                return R.fail("查询合同！:" + throwable.getMessage());
            }

            @Override
            public R<PageWrapper<List<PreSalesPageResponse>>> selectDiPreSaleListForRemote(Integer pageNum, Integer pageSize) {
                return R.fail("分页查询方案清单！:" + throwable.getMessage());
            }

            @Override
            public R updatePreSaleManifest(UpdatePreSaleManifestRequest request) {
                return R.fail("更新方案清单！:" + throwable.getMessage());
            }

            @Override
            public R<List<ClueToJdyResponse>> synClueToJdy() {
                return R.fail("同步线索到简道云！:" + throwable.getMessage());
            }

            @Override
            public R<List<PreSaleAppearanceResponse>> queryProjectPreSale() {
                return R.fail("查询机械外形图不为空的数据报错！:" + throwable.getMessage());
            }

            @Override
            public R synCusToU9(String customerCode) {
                return R.fail("同步客户到u9错误！:" + throwable.getMessage());
            }

        };

    }
}
