package com.dyd.di.api.model;

import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
public class SchedulingInformationResponse {

    /**
     * 方案排期
     */
    private List<PreSaleSchedulingInformationResponse> list = new ArrayList<>();

    /**
     * 系统工程师
     */
    private String systemsEngineer;

    /**
     * 是否统一排期
     */
    private Boolean unifiedScheduling = false;

}
