package com.dyd.ami.api;

import com.dyd.ami.api.factory.RemoteAmiFallbackFactory;
import com.dyd.common.core.constant.ServiceNameConstants;
import com.dyd.common.core.domain.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 中间件服务
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteAmiService", value = ServiceNameConstants.AMI_SERVICE, fallbackFactory = RemoteAmiFallbackFactory.class)
public interface RemoteAmiService {
    /**
     * 财务收款创单
     */
    @PostMapping(value = "/from/collectionCreate")
    public R<String> collectionCreate();


    @PostMapping(value = "/v1/job/priceChange")
    public R<String> itemPriceCall();

    @PostMapping(value = "/v1/job/storageFree")
    public R<String> storageFree();


    @PostMapping(value = "/v1/job/storageCrmFree")
    public R<String> storageCrmFree();

    @PostMapping(value = "/v1/job/storageProjectFour")
    R<String> storageProjectFour();

    /**
     * 04-4进度管理实际完成日期u9定时更新
     * @return
     */
   /* @PostMapping(value = "/v1/job/updateActualFinishTime")
    R<String> updateActualFinishTime();*/

    /**
     *更新04-4项目管理_开发用-物料采购到货-采购-实际完成日期(引用)
     * @return
     */
    @PostMapping(value = "/v1/job/updatePurchaseFinishTime")
    R<String> updatePurchaseFinishTime();

    @PostMapping(value = "/v1/job/stockProjectFour")
    public R<String> stockProjectFour();


    @PostMapping(value = "/v1/job/ClueInuse")
    public R<String> ClueInuse();

    @PostMapping(value = "/v1/job/ClueDueDelay")
    public R<String> ClueDueDelay();

    @PostMapping(value = "/v1/job/ClueDueScoreDelay")
    public R<String> ClueDueScoreDelay();

    @PostMapping(value = "/v1/job/ClueDueRecordDelay")
    public R<String> ClueDueRecordDelay();


    @PostMapping(value = "/v1/job/ClueUpdate")
    public R<String> ClueUpdate();

    @PostMapping(value = "/v1/job/StockUpdate")
    public R<String> StockUpdate();

    @PostMapping(value = "/v1/job/bizChanceApprove")
    public R<String> bizChanceApprove();

    /**
     * 超期提醒
     */
    @PostMapping(value = "/from/overdueReminder")
    public R<String> overdueReminder();


    /**
     * 超出预算提醒
     */
    @PostMapping(value = "/from/overExcessReminder")
    public R<String> overExcessReminder();

    /**
     * 更新jdy订单基础数据
     */
    @PostMapping(value = "/from/updateOrderBasicData")
    public R<String> updateOrderBasicData();

    /**
     * 更新jdy订单基础数据
     */
    @PostMapping(value = "/v1/job/evaluationInfo")
    public R<String> evaluationInfo();

    /**
     * 售后客户联系人同步
     */
    @PostMapping(value = "/v1/job/processAfterSales")
    public R<String> processAfterSales();

    /**
     * 外综服单号同步
     */
    @PostMapping(value = "/v1/job/tradeU9Order")
    public R<String> tradeU9Order();

    /**
     * 财务应收处理
     */
    @PostMapping(value = "/v1/job/financeReceipt")
    public R<String> processFinanceReceipt();


    /**
     * 同步料品信息
     */
    @PostMapping(value = "/v1/job/syncItem")
    public R<String> syncItem();


    /**
     * 同步料品信息
     */
    @PostMapping(value = "/v1/job/scanItemMsg")
    public R<String> scanItemMsg();

    /**
     * 同步出差预算&汇报流程
     */
    @PostMapping(value = "/from/syncTravelBudgetCreate")
    public R<String> syncTravelBudgetCreate();


    /**
     * @des:合同付款方式关联数据同步到付款方式数据
     */
    @PostMapping(value = "/paymentMethod/updataContractPaymentData")
    public R<String> updataContractPaymentData();


    /**
     * 发送异常人员信息
     */
    @PostMapping(value = "/from/sendingAbnormalPersonnel")
    public R<String> sendingAbnormalPersonnel();

    /**
     * 同步供应商自检数据
     * @return
     */
    @PostMapping(value = "/v1/job/syncSelfCheck")
    public R<String> syncSelfCheck();

    /**
     * 同步贸易类订单发票
     */
    @PostMapping(value = "/v1/job/invoicingInformation")
    public R<String> invoicingInformation();

    /**
     * 完工待提管理定时任务
     * 更新内容：
     * 1，已出货金额（自动）
     * 2，已出货套数（自动）
     * 3，项目总套数（自动）
     */
    @PostMapping(value = "/v1/job/projectManagerCompletion")
    R projectManagerCompletion();

    /**
     * 更新04-4项目管理开发用表单下子表单:物料成本-采购,产品设计成本-机械+电气,外协加工成本,补货成本-项目,包装费用-仓库,技术支持人工费用,
     * 机械设计人工费用,电气设计人工费用,售后人工费用,售后差旅费用,外协安装费用-采购,货物运输费用-仓库,逾期成品未提仓储费用-项目
     * 逾期成品未提资金占用费用-项目,
     */
    @PostMapping("/v1/job/updateProjectManager")
    R updateProjectManager();

    /**
     * 重新通知供应发货
     * 针对通知失败的场景
     *
     * @return
     */
    @PostMapping("/v1/job/reSendNotice")
    R reSendNotice();

    /**
     * 同步贸易类订单发票
     */
    @PostMapping(value = "/tool/dataSync")
    public R dataSync();

    /**
     * 更新04-3合同管理付款方式1
     * @return
     */
    @PostMapping("/v1/job/jdyContractManagerUpdate")
    R jdyContractManagerUpdate();

    /**
     * 完工待提费用计算，每天晚上1：30执行
     *
     * @return
     */
    @PostMapping("/v1/job/jdyCompleteFeeCal")
    R jdyCompleteFeeCal();

    /**
     * 财务【应收管理】 - 【应收管理V2】 催款(每个月5号、10号、15号、16号 凌晨2:10 推一次)
     *
     * @return
     */
    @PostMapping("/v1/job/jdyDemandPayment")
    R jdyDemandPayment();

    /**
     * 定时更新回款计划 财务【应收管理】 - 【应收管理V2】 - 【应收汇总】 每天凌晨 3:10
     * @return
     */
    @PostMapping("/v1/job/jdySyncReceiptPlan")
    R jdySyncReceiptPlan();

    /**
     * 供应链仓储物流 --委外退料，委外收货，标准收货数据同步
     * @return
     */
    @GetMapping("/v1/job/applyChain")
    R applyChain();

    /**
     * 销售绩效同步
     * @return
     */
    @PostMapping("/performance/syncJdy")
    R syncJdy();

    /**
     * 定时同步料品数据（参考成本更新时间/参考成本有效日期）
     * @return
     */
    @PostMapping("/v1/job/jdySyncMaterialData")
    R jdySyncMaterialData();

    /**
     * 定时同步所有单/双章合同（报价商务审批-备件,商务审批）
     * @return
     */
    @PostMapping("/v1/job/jdySynContract")
    R jdySynContract();

    /**
     * 04-4推送04-4.2项目成本异常说明,创建已用成本对比表
     * @return
     */
    @GetMapping("/v1/job/usedCostSyn")
    R usedCostSyn();

    /**
     * 同步04-1
     * @return
     */
    @GetMapping("/v1/job/synJdy04T1")
    R synJdy04T1();

    /**
     * 同步文件
     * @return
     */
    @GetMapping("/v1/job/sysFile")
    R sysFile();

    /**
     * 同步物料历史报价信息(凌晨4点半执行)
     * @return
     */
    @PostMapping("/v1/job/synMaterialQuoteHistory")
    R synMaterialQuoteHistory();

    /**
     * 同步贸易类订单清单出货确认时间(每晚10点执行)
     *
     * @return
     */
    @PostMapping("/v1/job/sysOrderShipmentTime")
    R sysOrderShipmentTime();

    /**
     * u9同步简道云贸易类订单清单，贸易类订单明细
     * @return
     */
  /*  @PostMapping("/erpFrom/createTradeOrder")
    R createTradeOrder();*/

    @GetMapping("/erpFrom/createTradeOrder")
    R createTradeOrder(@RequestParam(value = "docNo",required = false) String  docNo);
    /**
     * 应收2.0同步应收数据 (每月5号凌晨1点半执行)
     * @return
     */
    @PostMapping("/v1/job/accounts/receivable")
    R accountsReceivable();

    /**
     * 同步CRM商机客户跟进时间(每天晚上10点半执行)
     *
     * @return
     */
    @PostMapping("/v1/job/synFollowDate")
    R synFollowDate();

    /**
     * 协议台账
     */
    @PostMapping(value = "/from/protocolLedgerTask")
    public R<String> protocolLedgerTask();

    /**
     * 统计项目差旅费用
     */
    @PostMapping(value = "/v1/job/countProjectFee")
    public R<String> countProjectFee();

    /**
     * 信息化工单每天统计发群
     */
    @PostMapping(value = "/from/infoCountSending")
    public R<String> infoCountSending();

    /**
     * 全量更新客户表
     */
    @PostMapping(value = "/from/updateAllAccount")
    public R<String> updateAllAccount();

    /**
     * 每天播报前两个工作日没写日报的员工
     */
    @PostMapping(value = "/from/dailyLack")
    public R<String> dailyLack();

    /**
     * 售后完工报告
     */
    @PostMapping(value = "/from/afterSalesReport")
    public R<String> afterSalesReport();

    /**
     * 售后完工报告文件
     */
    @PostMapping(value = "/from/afterSalesFile")
    public R<String> afterSalesFile();

    /**
     * 处理已审核的00-3-U9采购订单数据（来源U9)---正在入库数量定时任务
     * @return
     */
    @PostMapping("/jdyOperation/updatePurchaseOrder")
    R updatePurchaseOrder();

    @GetMapping("/from/clueJob")
    R clueJob();
}
