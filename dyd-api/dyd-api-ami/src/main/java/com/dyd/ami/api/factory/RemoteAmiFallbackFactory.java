package com.dyd.ami.api.factory;

import com.dyd.ami.api.RemoteAmiService;
import com.dyd.common.core.domain.R;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * 中间件降级处理
 *
 * <AUTHOR>
 */
@Component
public class RemoteAmiFallbackFactory implements FallbackFactory<RemoteAmiService> {

    private static final Logger log = LoggerFactory.getLogger(RemoteAmiFallbackFactory.class);

    @Override
    public RemoteAmiService create(Throwable throwable) {
        log.error("中间件服务调用失败:{}", throwable.getMessage());
        return new RemoteAmiService()
        {
            @Override
            public R<String> collectionCreate()
            {
                return R.fail("财务收款创建失败！:" + throwable.getMessage());
            }
            @Override
            public R<String> itemPriceCall()
            {
                return R.fail("料品价格变动创建失败！:" + throwable.getMessage());
            }
            @Override
            public R<String> storageFree()
            {
                return R.fail("04-4库存金额变动创建失败！:" + throwable.getMessage());
            }
            @Override
            public R<String> storageCrmFree()
            {
                return R.fail("04-4U9更新失败！:" + throwable.getMessage());
            }

            @Override
            public R<String> storageProjectFour() {
                return R.fail("04-4U9完成日期更新失败老版！:" + throwable.getMessage());
            }

           /* @Override
            public R<String> updateActualFinishTime(){
                return R.fail("04-4U9完成日期更新失败！:" + throwable.getMessage());

            }
            */

            @Override
            public R<String> updatePurchaseFinishTime() {
                return R.fail("更新04-4项目管理_开发用-物料采购到货-采购-实际完成日期(引用)失败:{}",throwable.getMessage());
            }

            @Override
            public R<String> stockProjectFour(){
                return R.fail("04-4U9库存费用问题更新失败！:" + throwable.getMessage());
            }
            @Override
            public R<String> ClueInuse(){

                return R.fail("线索使用中数据第一次初始化失败！:" + throwable.getMessage());
            }
            @Override
            public R<String> ClueDueDelay(){
                return R.fail("线索使用中延迟数据失败！:" + throwable.getMessage());
            }
            @Override
            public R<String> ClueDueScoreDelay(){
                return R.fail("销售工作配合度考核！:" + throwable.getMessage());
            }

            @Override
           // @PostMapping(value = "/v1/job/ClueDueRecordDelay")
            public R<String> ClueDueRecordDelay(){
                return R.fail("销售扣分记录！:" + throwable.getMessage());
            }

            @Override
           // @PostMapping(value = "/v1/job/ClueUpdate")
            public R<String> ClueUpdate(){
                return R.fail("转回记录！:" + throwable.getMessage());
            }
            @Override
            public R<String> StockUpdate(){
                return R.fail("基础货期！:" + throwable.getMessage());
            }

            @Override
            public R<String> bizChanceApprove(){return R.fail("商务审批变更线索未统计失败！:" + throwable.getMessage());}

            @Override
            public R<String> evaluationInfo() {
                return R.fail("06-1评分信息更新失败！:" + throwable.getMessage());
            }

            @Override
            public R<String> processAfterSales() {
                return R.fail("售后客户信息更新失败！:" + throwable.getMessage());
            }

            @Override
            public R<String> tradeU9Order() {
                return R.fail("外综服信息更新失败！:" + throwable.getMessage());
            }

            @Override
            public R<String> processFinanceReceipt() {
                return R.fail("财务应收数据处理失败！:" + throwable.getMessage());
            }

            @Override
            public R<String> syncItem() {
                return R.fail("同步料品信息数据处理失败！:" + throwable.getMessage());
            }

            @Override
            public R<String> scanItemMsg() {
                return R.fail("同步技术支持常用料信息数据处理失败！:" + throwable.getMessage());
            }

            @Override
            public R<String> overdueReminder() {
                return R.fail("超期提醒创建失败！:" + throwable.getMessage());
            }
            @Override
            public R<String> overExcessReminder(){
                return R.fail("超预算提醒创建失败!:" + throwable.getMessage());
            }

            @Override
            public R<String> updateOrderBasicData() {
                return R.fail("订单基础信息更新失败！:" + throwable.getMessage());
            }
            @Override
            public R<String> syncTravelBudgetCreate() {
                return R.fail("同步出差预算&汇报流程失败！:" + throwable.getMessage());
            }

            @Override
            public R<String> updataContractPaymentData(){
                return R.fail("04-3.4合同付款方式数据关联数据信息更新失败！:" + throwable.getMessage());
            }

            @Override
            public R<String> sendingAbnormalPersonnel() {
                return R.fail("异常人员发送到群失败！" + throwable.getMessage());
            }

            /**
             * 同步供应商自检数据
             *
             * @return
             */
            @Override
            public R<String> syncSelfCheck() {
                return R.fail("同步供应商自检数据失败！" + throwable.getMessage());
            }

            @Override
            public R<String> invoicingInformation() {
                return  R.fail("同步贸易类订单发票失败！" + throwable.getMessage());
            }

            @Override
            public R projectManagerCompletion() {
                return  R.fail("完工待提管理定时任务！" + throwable.getMessage());
            }

            @Override
            public R updateProjectManager() {
                return  R.fail("更新04-4项目管理开发用表单下子表单！" + throwable.getMessage());
            }

            /**
             * 重新通知供应发货
             * 针对通知失败的场景
             *
             * @return
             */
            @Override
            public R reSendNotice() {
                return R.fail("重新通知供应发货失败:" + throwable.getMessage());
            }

            @Override
            public R dataSync() {
                return R.fail("BI同步数据失败:" + throwable.getMessage());
            }

            @Override
            public R jdyContractManagerUpdate() {
                return R.fail("更新04-3合同管理付款方式1失败:" + throwable.getMessage());

            }

            /**
             * 完工待提费用计算，每天晚上1：30执行
             *
             * @return
             */
            @Override
            public R jdyCompleteFeeCal() {
                return  R.fail("完工待提费用计算失败:" + throwable.getMessage());
            }

            /**
             * 财务【应收管理】 - 【应收管理V2】 催款(每个月5号、10号、15号、16号 凌晨2:10 推一次)
             *
             * @return
             */
            @Override
            public R jdyDemandPayment() {
                return  R.fail("财务催款失败:" + throwable.getMessage());
            }

            /**
             * 定时更新回款计划 财务【应收管理】 - 【应收管理V2】 - 【应收汇总】 每天凌晨 3:100
             *
             * @return
             */
            @Override
            public R jdySyncReceiptPlan() {
                return R.fail("更新回款计划失败:" + throwable.getMessage());
            }

            /**
             * 委外退料，委外收货，标准收货数据同步
             * @return
             */
            @Override
            public R applyChain() {
                return R.fail("委外退料，委外收货，标准收货数据同步失败"+ throwable.getMessage());
            }

            @Override
            public R syncJdy() {
                return R.fail("销售绩效同步失败"+ throwable.getMessage());
            }

            /**
             * 定时同步料品数据（参考成本更新时间/参考成本有效日期）
             *
             * @return
             */
            @Override
            public R jdySyncMaterialData() {
                return R.fail("料品数据（参考成本更新时间/参考成本有效日期）同步失败" + throwable.getMessage());
            }

            /**
             * 定时同步所有单/双章合同（报价商务审批-备件,商务审批）
             *
             * @return
             */
            @Override
            public R jdySynContract() {
                return R.fail("单/双章合同（报价商务审批-备件,商务审批）同步失败" + throwable.getMessage());
            }

            @Override
            public R usedCostSyn() {
                return R.fail("04-4推送04-4.2项目成本异常说明,创建已用成本对比表同步失败" + throwable.getMessage());

            }

            @Override
            public R synJdy04T1() {
                return R.fail("同步04-1失败{}",throwable.getMessage());
            }

            @Override
            public R sysFile() {
                return R.fail("同步中大非标定义失败{}",throwable.getMessage());
            }

            /**
             * 同步物料历史报价信息(凌晨4点半执行)
             *
             * @return
             */
            @Override
            public R synMaterialQuoteHistory() {
                return R.fail("同步物料历史报价信息失败{}",throwable.getMessage());
            }

            /**
             * 同步贸易类订单清单出货确认时间(每晚10点执行)
             *
             * @return
             */
            @Override
            public R sysOrderShipmentTime() {
                return R.fail("同步贸易类订单清单出货确认时间失败{}",throwable.getMessage());
            }

            @Override
            public R createTradeOrder(String docNo) {
                return R.fail("u9同步简道云贸易类订单清单，贸易类订单明细{}",throwable.getMessage());
            }


            /**
             * 应收2.0同步应收数据 (每月5号凌晨1点半执行)
             *
             * @return
             */
            @Override
            public R accountsReceivable() {
                return R.fail("同步应收2.0数据失败{}",throwable.getMessage());
            }

            /**
             * 同步CRM商机客户跟进时间(每天晚上10点半执行)
             *
             * @return
             */
            @Override
            public R synFollowDate() {
                return R.fail("同步CRM商机客户跟进时间{}",throwable.getMessage());
            }

            @Override
            public R<String> protocolLedgerTask() {
                {
                    return R.fail("协议台账定时任务失败{}", throwable.getMessage());
                }
            }

            /**
             * 统计项目差旅费用
             */
            @Override
            public R<String> countProjectFee() {
                return R.fail("统计项目差旅费用定时任务失败{}", throwable.getMessage());
            }

            @Override
            public R<String> infoCountSending() {
                return R.fail("信息化工单每天统计发群失败{}", throwable.getMessage());
            }
            @Override
            public R<String> updateAllAccount() {
                return R.fail("全量更新客户表失败{}", throwable.getMessage());
            }

            @Override
            public R<String> dailyLack() {
                return R.fail("每天播报前两个工作日没写日报的员工时任务失败{}", throwable.getMessage());
            }

            @Override
            public R<String> afterSalesReport() {
                return R.fail("售后完工报告更新任务失败{}", throwable.getMessage());
            }

            @Override
            public R<String> afterSalesFile() {
                return R.fail("售后完工报告文件更新任务失败{}", throwable.getMessage());
            }

            @Override
            public R updatePurchaseOrder() {
                return R.fail("理已审核的00-3-U9采购订单数据（来源U9)---正在入库数量失败{}", throwable.getMessage());
            }

            @Override
            public R clueJob() {
                return R.fail();
            }
        };
    }
}
