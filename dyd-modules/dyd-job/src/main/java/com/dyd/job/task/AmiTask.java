package com.dyd.job.task;

import com.dyd.ami.api.RemoteAmiService;
import com.dyd.common.core.domain.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * 简道云定时任务
 *
 * <AUTHOR>
 */
@Component("amiTask")
public class AmiTask {
    @Autowired
    private RemoteAmiService remoteAmiService;


    public void  synJdy04T1(){
        R r = remoteAmiService.synJdy04T1();
    }

    /**
     * 财务收款
     */
    public void cwsk()
    {
        R<String> amiResult = remoteAmiService.collectionCreate();
        System.out.println(amiResult.toString());
    }

    /**
     * 超期提醒
     */
    public void overdueReminder()
    {
        R<String> amiResult = remoteAmiService.overdueReminder();
        System.out.println(amiResult.toString());
    }

    /**
     * 超预算提醒
     */
    public void overExcessReminder()
    {
        R<String> amiResult = remoteAmiService.overExcessReminder();
        System.out.println(amiResult.toString());
    }


    /**
     * 订单基础数据
     */
    public void ddjcsj()
    {
        R<String> amiResult = remoteAmiService.updateOrderBasicData();
        System.out.println(amiResult.toString());
    }

    /**
     * 04-4库存费用变动
     */
    public void storageFreeTask()
    {
        R<String> amiResult = remoteAmiService.storageFree();
        System.out.println(amiResult.toString());
    }


    /**
     * 04-4更新
     */
    public void storageCrmFree()
    {
        R<String> amiResult = remoteAmiService.storageCrmFree();
        System.out.println(amiResult.toString());
    }

    public void storageProjectFour(){
        remoteAmiService.storageProjectFour();
    }

    public void updateProjectManager(){
        remoteAmiService.updateProjectManager();
    }

    /**
     * 04-4进度管理实际完成日期u9定时更新
     * <AUTHOR>
     */
   /* public void updateActualFinishTime(){

        R<String> amiResult = remoteAmiService.updateActualFinishTime();
        System.out.println(amiResult.toString());
    }*/

    /**
     * 更新04-4项目管理_开发用-物料采购到货-采购-实际完成日期(引用)
     */
    public void updatePurchaseFinishTime(){
        R<String> amiResult = remoteAmiService.updatePurchaseFinishTime();
    }

    public  void  stockProjectFour(){

        R<String> amiResult = remoteAmiService.stockProjectFour();
        System.out.println(amiResult.toString());
    }


    /**
     * 线索-使用中入库
     */
    public void ClueInuse()
    {
        R<String> amiResult = remoteAmiService.ClueInuse();
        System.out.println(amiResult.toString());
    }

    /**
     * 线索-使用中退回信息入库
     */
    public void ClueDueDelay()
    {
        R<String> amiResult = remoteAmiService.ClueDueDelay();
        System.out.println(amiResult.toString());
    }

    /**
     * 销售工作配合度考核
     */
    public void ClueDueScoreDelay()
    {
        R<String> amiResult = remoteAmiService.ClueDueScoreDelay();
        System.out.println(amiResult.toString());
    }

    public  void ClueDueRecordDelay(){

        R<String> amiResult = remoteAmiService.ClueDueRecordDelay();
        System.out.println(amiResult.toString());
    }

    public  void ClueUpdate(){

        R<String> amiResult = remoteAmiService.ClueUpdate();
        System.out.println(amiResult.toString());
    }

    public  void StockUpdate(){

        R<String> amiResult = remoteAmiService.StockUpdate();
        System.out.println(amiResult.toString());
    }



    /**
     * 料品金额变动
     */
    public void itemPriceChangeTask()
    {
        R<String> amiResult = remoteAmiService.itemPriceCall();
        System.out.println(amiResult.toString());
    }

    /**
     * 料品金额变动
     */
    public void evaluationInfoTask()
    {
        R<String> amiResult = remoteAmiService.evaluationInfo();
        System.out.println(amiResult.toString());
    }

    public void bizChanceApprove(){
        R<String> amiResult = remoteAmiService.bizChanceApprove();
        System.out.println(amiResult.toString());
    }


    public void processAfterSales(){
        R<String> amiResult = remoteAmiService.processAfterSales();
        System.out.println(amiResult.toString());
    }

    /**
     * 外综服
     */
    public void tradeU9Order(){
        R<String> amiResult = remoteAmiService.tradeU9Order();
        System.out.println(amiResult.toString());
    }

    /**
     * 财务应收
     */
    public void financeReceipt(){
        R<String> amiResult = remoteAmiService.processFinanceReceipt();
        System.out.println(amiResult.toString());
    }

    /**
     * 同步料品
     */
    public void syncItem(){
        R<String> amiResult = remoteAmiService.syncItem();
        System.out.println(amiResult.toString());
    }


    /**
     * 同步技术支持常用料
     */
    public void scanItemMsg(){
        R<String> amiResult = remoteAmiService.scanItemMsg();
        System.out.println(amiResult.toString());
    }

    /**
     * 同步出差预算&汇报流程
     */
    public void syncTravelBudgetCreate(){
        R<String> amiResult = remoteAmiService.syncTravelBudgetCreate();
        System.out.println(amiResult.toString());
    }

    /**
     * @des:合同付款方式数据关联表数据更新
     */
    public void updataContractPaymentData(){
        R<String> amiResult = remoteAmiService.updataContractPaymentData();
        System.out.println(amiResult.toString());
    }

    /**
     * 发送异常人员信息
     */
    public void sendingAbnormalPersonnel(){
        R<String> amiResult = remoteAmiService.sendingAbnormalPersonnel();
        System.out.println(amiResult.toString());
    }

    /**
     * 同步供应商自检数据
     */
    public void syncSelfCheck(){
        R<String> amiResult = remoteAmiService.syncSelfCheck();
        System.out.println(amiResult.toString());
    }

    /**
     * 同步贸易类订单发票
     */
    public void invoicingInformation(){
        R<String> amiResult = remoteAmiService.invoicingInformation();
        System.out.println(amiResult.toString());
    }

    /**
     * 完工待提管理定时任务
     * 更新内容：
     * 1，已出货金额（自动）
     * 2，已出货套数（自动）
     * 3，项目总套数（自动）
     */
    public void projectManagerCompletion(){
        remoteAmiService.projectManagerCompletion();
    }

    /**
     * 重新通知供应发货
     * 针对通知失败的场景
     */
    public void reSendNotice() {
        R<String> amiResult = remoteAmiService.reSendNotice();
        System.out.println(amiResult.toString());
    }

    /**
     * BI数据同步
     */
    public void dataSync() {
        R<String> amiResult = remoteAmiService.dataSync();
        System.out.println(amiResult.toString());
    }

    /**
     * 更新04-3合同管理付款方式1
     */
    public void jdyContractManagerUpdate(){
        remoteAmiService.jdyContractManagerUpdate();
    }

    /**
     * 完工待提费用计算，每天晚上1：30执行
     */
    public void jdyCompleteFeeCal() {
        R<String> amiResult = remoteAmiService.jdyCompleteFeeCal();
        System.out.println(amiResult.toString());
    }

    /**
     * 财务【应收管理】 - 【应收管理V2】 催款(每个月5号、10号、15号、16号 凌晨2:10 推一次)
     */
    public void jdyDemandPayment() {
        R<String> amiResult = remoteAmiService.jdyDemandPayment();
        System.out.println(amiResult.toString());
    }

    /**
     * 定时更新回款计划 财务【应收管理】 - 【应收管理V2】 - 【应收汇总】 每天凌晨 3:10
     */
    public void jdySyncReceiptPlan() {
        R<String> amiResult = remoteAmiService.jdySyncReceiptPlan();
        System.out.println(amiResult.toString());
    }

    /**
     * 供应链仓储物流 --委外退料，委外收货，标准收货数据同步
     */
    public void applyChain(){
        R r = remoteAmiService.applyChain();
    }

    /**
     * 销售绩效同步
     */
    public void syncJdy(){
        R r = remoteAmiService.syncJdy();
    }

    /**
     * 同步料品数据（参考成本更新时间/参考成本有效日期）
     */
    public void jdySyncMaterialData() {
        R<String> amiResult = remoteAmiService.jdySyncMaterialData();
        System.out.println(amiResult.toString());
    }

    /**
     * 同步所有单/双章合同（报价商务审批-备件,商务审批）
     */
    public void jdySynContract() {
        R<String> amiResult = remoteAmiService.jdySynContract();
        System.out.println(amiResult.toString());
    }

    /**
     * 04-4推送04-4.2项目成本异常说明,创建已用成本对比表
     * @return
     */
    public R usedCostSyn(){
        return remoteAmiService.usedCostSyn();
    }

    /**
     * 同步中大非标定义2023-8-7.xlsx
     * @return
     */
    public R sysFile(){
        return remoteAmiService.sysFile();
    }

    /**
     * 同步物料历史报价信息(凌晨4点半执行)
     */
    public void synMaterialQuoteHistory() {
        R<String> amiResult = remoteAmiService.synMaterialQuoteHistory();
        System.out.println(amiResult.toString());
    }

    /**
     * 同步贸易类订单清单出货确认时间(每晚10点执行)
     */
    public void sysOrderShipmentTime(){
        R<String> amiResult = remoteAmiService.sysOrderShipmentTime();
        System.out.println(amiResult.toString());
    }

    /**
     * u9同步简道云贸易类订单清单，贸易类订单明细
     */
   /* public void createTradeOrder(){
        remoteAmiService.createTradeOrder();
    }*/

    /**
     * 应收2.0同步应收数据 (每月5号凌晨1点半执行)
     */
    public void accountsReceivable() {
        R<String> amiResult = remoteAmiService.accountsReceivable();
        System.out.println(amiResult.toString());
    }

    /**
     * 同步CRM商机客户跟进时间(每天晚上10点半执行)
     */
    public void synFollowDate() {
        R<String> amiResult = remoteAmiService.synFollowDate();
        System.out.println(amiResult.toString());
    }

    /**
     * 协议台账
     */
    public void protocolLedgerTask() {
        R<String> amiResult = remoteAmiService.protocolLedgerTask();
        System.out.println(amiResult.toString());
    }

    /**
     * 统计项目差旅费用
     */
    public void countProjectFee() {
        R<String> amiResult = remoteAmiService.countProjectFee();
        System.out.println(amiResult.toString());
    }

    /**
     * 信息化工单每天统计发群
     */
    public void infoCountSending() {
        R<String> amiResult = remoteAmiService.infoCountSending();
        System.out.println(amiResult.toString());
    }

    /**
     * 全量更新客户表
     */
    public void updateAllAccount() {
        R<String> amiResult = remoteAmiService.updateAllAccount();
        System.out.println(amiResult.toString());
    }

    /**
     * 每天播报前两个工作日没写日报的员工
     */
    public void dailyLack() {
        R<String> amiResult = remoteAmiService.dailyLack();
        System.out.println(amiResult.toString());
    }

    /**
     * 售后完工报告
     */
    public void afterSalesReport() {
        R<String> amiResult = remoteAmiService.afterSalesReport();
        System.out.println(amiResult.toString());
    }

    /**
     * 售后完工报告文件
     */
    public void afterSalesFile() {
        R<String> amiResult = remoteAmiService.afterSalesFile();
        System.out.println(amiResult.toString());
    }

    public void updatePurchaseOrder(){
        remoteAmiService.updatePurchaseOrder();
    }

    /**
     * 财务收款创建
     */
    public void collectionCreate(){
        remoteAmiService.collectionCreate();
    }

    public void clueJob(){
        remoteAmiService.clueJob();
    }
}
