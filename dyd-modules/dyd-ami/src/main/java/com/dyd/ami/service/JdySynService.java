package com.dyd.ami.service;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dyd.ami.config.TradeConfig;
import com.dyd.ami.domain.DTO.*;
import com.dyd.ami.domain.DdDept;
import com.dyd.ami.domain.DdDeptDto;
import com.dyd.ami.domain.DdUserDto;
import com.dyd.ami.domain.finance.FinanceStandardSalesU9Response;
import com.dyd.ami.domain.item.ItemInfoDto;
import com.dyd.ami.domain.u9.*;
import com.dyd.ami.enums.SODocStatusEnum;
import com.dyd.ami.mapper.*;
import com.dyd.common.core.domain.R;
import com.dyd.common.core.utils.DateUtils;
import com.dyd.common.core.utils.StringUtils;
import com.dyd.jdy.JdyClient;
import com.dyd.jdy.bean.common.Cond;
import com.dyd.jdy.bean.common.Filter;
import com.dyd.jdy.bean.common.ValueString;
import com.dyd.jdy.bean.common.ValueT;
import com.dyd.jdy.bean.jdy.JdyCommonDto;
import com.dyd.jdy.bean.jdy.request.*;
import com.dyd.jdy.bean.jdy.response.*;
import com.dyd.jdy.bean.receipt.JdyForeignReceiptApprovalRequest;
import com.dyd.jdy.bean.receipt.JdyForeignReceiptApprovalResponse;
import com.dyd.jdy.bean.receipt.JdyForeignReceiptRequest;
import com.dyd.jdy.bean.receipt.JdyForeignReceiptResponse;
import com.dyd.jdy.bean.trade.*;
import com.dyd.jdy.constant.JdyConstants;
import com.dyd.jdy.service.JdyCommonService;
import com.dyd.jdy.util.ValueUtil;
import com.dyd.system.api.RemoteFileService;
import com.dyd.system.api.domain.SysFile;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.dyd.jdy.constant.JdyConstants.JDY_EQ;

/**
 * 简道云同步数据
 */
@Slf4j
@Service
public class JdySynService {

    @Autowired
    private JdyCommonService jdyCommonService;

    @Autowired
    private Jdy041Mapper jdy041Mapper;

    @Autowired
    private Jdy041ItemMapper jdy041ItemMapper;

    @Autowired
    private Jdy041SaleMapper jdy041SaleMapper;

    @Autowired
    private Jdy041UrlMapper jdy041UrlMapper;

    @Autowired
    private RemoteFileService remoteFileService;

    @Autowired
    private Jdy041PayTypeMapper jdy041PayTypeMapper;

    @Autowired
    private JdyClient jdyClient;

    @Autowired
    private ErpMapper erpMapper;

    @Autowired
    private DdUserMapper ddUserMapper;

    @Autowired
    private DdDeptMapper ddDeptMapper;

    @Autowired
    private TradeConfig tradeConfig;

    @Autowired
    private JdyTradeOrderInfoMapper jdyTradeOrderInfoMapper;

    /**
     * 同步订单数据
     */
    public void syncOrderBase(String dataId){
        //查询应收款
        List<JdyFinanceOrderCollectionPlanQueryResponse.PlanData> dataList = new ArrayList<>();
        int limit = 300;
        //查询应收数据
        jdyCommonService.queryJdyFinanceNew(dataList,dataId,null,JdyConstants.AND,null,limit);
        if(CollectionUtil.isNotEmpty(dataList)){

            for(JdyFinanceOrderCollectionPlanQueryResponse.PlanData planData:dataList){
                //u9单号
                String orderNo = planData.get_widget_1617264711816();


                JdyOrderBaseUpdateRequest jdyOrderBaseUpdateRequest = new JdyOrderBaseUpdateRequest();

                JdyOrderBaseUpdateRequest.FinanceOrderCollectionPlanData financeOrderCollectionPlanData = new JdyOrderBaseUpdateRequest.FinanceOrderCollectionPlanData();
                financeOrderCollectionPlanData.set_widget_1620719335689(ValueString.builder().value(planData.get_widget_1617264711816()).build());
                //查询u9标准销售
                List<FinanceStandardSalesU9Response> financeStandardSalesU9s = erpMapper.getFinanceStandardSalesU9(null, orderNo, null,null,null);

                //financeOrderCollectionPlanData.set_widget_1620719335638(ValueString.builder().value(financeStandardSalesU9s).build());
                jdyOrderBaseUpdateRequest.setData(financeOrderCollectionPlanData);

                //查询订单数据是否存在
                List<JdyOrderBaseResponse.OrderBase> orderBaseList = new ArrayList<>();
                JdyCommonDto.Field field = new JdyCommonDto.Field();
                field.setField("_widget_1620719335689");
                field.setValue(Arrays.asList(orderNo));
                field.setMethod(JDY_EQ);
                jdyCommonService.queryOrderBase(orderBaseList,null,null,JdyConstants.AND,Arrays.asList(field),10);
                jdyOrderBaseUpdateRequest.setApp_id("609a3e38bca1f5000759c679");
                jdyOrderBaseUpdateRequest.setEntry_id("609a3e7c1299a100076c5bb6");
                if(CollectionUtil.isEmpty(orderBaseList)){

                    jdyOrderBaseUpdateRequest.setUrlName(JdyConstants.ONE_CREATE_URL);

                }else{
                    jdyOrderBaseUpdateRequest.setUrlName(JdyConstants.ONE_UPDATE_URL);
                }
                jdyClient.jdyCallV5(jdyOrderBaseUpdateRequest);
            }
        }

        int size = dataList.size();
        if( size == limit){
            String id = dataList.get(size-1).get_id();
            syncOrderBase(id);
        }
    }

    /**
     * 获取贸易类订单清单存在的u9订单号
     * @param orderData
     * @param limit
     */
    public void tradeQdList(List<TradeOrderDataV5Response.FromData> orderData,int limit,String dataId,String orderNo){
        //查询贸易类订单清单
        TradeOrderDataV5Request tradeOrderDataRequest = TradeOrderDataV5Request
                .builder()
                .limit(limit)
                .build();
        tradeOrderDataRequest.setUrlName(JdyConstants.MORE_QUERY_URL);
        tradeOrderDataRequest.setApp_id("62a99c7a9ae77800081eadeb");
        tradeOrderDataRequest.setEntry_id("62a99cb6379333000811ba0e");
        tradeOrderDataRequest.setData_id(dataId);

        Cond cond = Cond.builder().field("_widget_1655282871909").value(Arrays.asList(orderNo)).method(JdyConstants.JDY_EQ).build();
        tradeOrderDataRequest.setFilter(Filter.builder().cond(Arrays.asList(cond)).rel(JdyConstants.AND).build());
        TradeOrderDataV5Response tradeOrderDataResponse = jdyClient.jdyCallV5(tradeOrderDataRequest);
        if(Objects.nonNull(tradeOrderDataResponse)){

            orderData.addAll(tradeOrderDataResponse.getData());
        }
    }

    public void tradeQdListIter(List<TradeOrderDataV5Response.FromData> orderData,int limit,String dataId,List<String> orderNos){
        //查询贸易类订单清单
        TradeOrderDataV5Request tradeOrderDataRequest = TradeOrderDataV5Request
                .builder()
                .limit(limit)
                .build();
        tradeOrderDataRequest.setUrlName(JdyConstants.MORE_QUERY_URL);
        tradeOrderDataRequest.setApp_id("62a99c7a9ae77800081eadeb");
        tradeOrderDataRequest.setEntry_id("62a99cb6379333000811ba0e");
        tradeOrderDataRequest.setData_id(dataId);

        Cond cond = Cond.builder().field("_widget_1655282871909").value(orderNos).method(JdyConstants.JDY_IN)
                .build();
        tradeOrderDataRequest.setFilter(Filter.builder().cond(Arrays.asList(cond)).rel(JdyConstants.AND).build());
        TradeOrderDataV5Response tradeOrderDataResponse = jdyClient.jdyCallV5(tradeOrderDataRequest);
        if(Objects.nonNull(tradeOrderDataResponse)){

            orderData.addAll(tradeOrderDataResponse.getData());

        }
    }

    public void getDeptIdsIter(List<Integer> deptIds,Integer parentId){
        List<DdDeptDto> ddDepts = ddDeptMapper.selectList(Wrappers.<DdDeptDto>lambdaQuery().eq(DdDeptDto::getParentDeptId, parentId));
        if(CollectionUtil.isNotEmpty(ddDepts)){
            List<Integer> deptIdList = ddDepts.stream().map(DdDeptDto::getDeptId).collect(Collectors.toList());
            deptIds.addAll(deptIdList);
            for(Integer deptId:deptIdList){
                getDeptIdsIter(deptIds,deptId);
            }
        }

    }

    public void tradeCommon(List<SmSoForQingDanResponse> smSoForQingDanResponses, Map<String, List<TradeOrderDataV5Response.FromData>> map){
        //销售合同条款
        Map<String,BaseDefineValueResponse> contactMap = erpMapper.selectBaseDefineValue("1001910150336302",null).stream().collect(Collectors.toMap(BaseDefineValueResponse::getCode, Function.identity(),(k1,k2)->k2));

        //订单分类I级
        Map<String,BaseDefineValueResponse> orderOneMap = erpMapper.selectBaseDefineValue("1002001030018896",null).stream().collect(Collectors.toMap(BaseDefineValueResponse::getCode, Function.identity(),(k1,k2)->k2));

        //订单分类II级
        Map<String,BaseDefineValueResponse> orderTwoMap = erpMapper.selectBaseDefineValue("1002001030019105",null).stream().collect(Collectors.toMap(BaseDefineValueResponse::getCode, Function.identity(),(k1,k2)->k2));

        //品牌
        Map<String,BaseDefineValueResponse> brandMap = erpMapper.selectBaseDefineValue("1001910140120322",null).stream().collect(Collectors.toMap(BaseDefineValueResponse::getCode, Function.identity(),(k1,k2)->k2));

        //验收标准
        Map<String,BaseDefineValueResponse> yanshouMap = erpMapper.selectBaseDefineValue("1001911020000151",null).stream().collect(Collectors.toMap(BaseDefineValueResponse::getCode, Function.identity(),(k1,k2)->k2));

        //部门
        List<DdDeptDto> ddDeptList = ddDeptMapper.selectList(null);

        List<DdDeptDto> ddDeptsFilter = ddDeptList.stream().filter(ddDept -> tradeConfig.getDepts().contains(ddDept.getDeptName())).collect(Collectors.toList());
        List<Integer> deptIds = ddDeptsFilter.stream().map(DdDeptDto::getDeptId).collect(Collectors.toList());

        List<Integer> deptIdAll = new ArrayList<>();
        for(Integer deptId:deptIds){
            getDeptIdsIter(deptIdAll,deptId);
        }

        deptIds.addAll(deptIdAll);


        for(SmSoForQingDanResponse smSoForQingDanResponse:smSoForQingDanResponses){

            //所属销售员
            String businessName = smSoForQingDanResponse.getBusinessName();
           /* if(StringUtils.isEmpty(businessName)){
                continue;
            }*/


            List<DdUserDto> ddUsers = ddUserMapper.selectList(Wrappers.<DdUserDto>lambdaQuery().like(DdUserDto::getName, smSoForQingDanResponse.getBusinessName()));
            if(CollectionUtil.isNotEmpty(ddUsers)){
                if(!deptIds.contains(Integer.parseInt(ddUsers.get(0).getDeptIdList()))){
                    List<String> names = tradeConfig.getNames();
                    if(StringUtils.isNotEmpty(businessName) && !names.contains(businessName)){
                        continue;
                    }
                }
            }



            //订单号
            String docNo = smSoForQingDanResponse.getDocNo();

            String id = smSoForQingDanResponse.getId();

            //标准销售行数据,过滤短缺关闭的行
            List<SoLineResponse> soLineRespons = erpMapper.selectSoLine(id);
            if(CollectionUtil.isEmpty(soLineRespons)){
                continue;
            }



            //查询商务审批
            List<JdyBusinessCheckResponse.JdyBusinessCheckData> businessCheckDataList = new ArrayList<>();
            JdyBusinessCheckRequest.Field businessCheckField = new JdyBusinessCheckRequest.Field();
            businessCheckField.setField("_widget_1646374479684");
            businessCheckField.setValue(Arrays.asList(docNo));
            businessCheckField.setMethod(JdyConstants.JDY_EQ);
            jdyCommonService.queryJdyBusinessCheck(businessCheckDataList,null,null,JdyConstants.AND,Arrays.asList(businessCheckField),100);

            //报价单商务审批
            List<JdyReplacementResponse.JdyReplacementData> dataListReplace = new ArrayList<>();
            JdyReplacementRequest.Field replaceField = new JdyReplacementRequest.Field();
            replaceField.setField("_widget_1687325084880");
            replaceField.setValue(Arrays.asList(docNo));
            replaceField.setMethod(JdyConstants.JDY_EQ);
            //报价商务审批
            jdyCommonService.queryJdyReplacement(dataListReplace,null,null,JdyConstants.AND,Arrays.asList(replaceField),10);


            //简道云外汇收款通知
            List<JdyForeignReceiptResponse.JdyForeignReceipt> jdyForeignReceiptList = new ArrayList<>();

            JdyForeignReceiptRequest.Field foreignReceiptField = new JdyForeignReceiptRequest.Field();
            foreignReceiptField.setField("_widget_1680606761431");
            foreignReceiptField.setValue(Arrays.asList(docNo));
            foreignReceiptField.setMethod(JdyConstants.JDY_EQ);
            jdyCommonService.queryJdyForeignReceipt(jdyForeignReceiptList,null,null,JdyConstants.AND,Arrays.asList(foreignReceiptField),100);

            CrmBusinessRequest crmBusinessRequest = new CrmBusinessRequest();
            crmBusinessRequest.setApp_id("6166529614a97a0008504397");
            crmBusinessRequest.setEntry_id("020100500000000000000001");
            crmBusinessRequest.setUrlName(JdyConstants.MORE_QUERY_URL);
            List<CrmBusinessRequest.Field> fieldList = new ArrayList<>();
            CrmBusinessRequest.Field fieldCrm = new CrmBusinessRequest.Field();
            //商机编号
            fieldCrm.setField("_widget_1730095515215");
            fieldCrm.setValue(Arrays.asList(smSoForQingDanResponse.getXjCode()));
            fieldCrm.setMethod("eq");
            fieldList.add(fieldCrm);
            CrmBusinessRequest.Filter filter = new CrmBusinessRequest.Filter();
            filter.setCond(fieldList);
            filter.setRel("and");
            crmBusinessRequest.setFilter(filter);
            log.info("查询crm商机请求参数:[{}]", JSON.toJSONString(crmBusinessRequest));
            CrmBusinessResponse crmBusinessResponse = jdyClient.jdyCallV5(crmBusinessRequest);
            log.info("查询crm商机返回参数:[{}]", JSON.toJSONString(crmBusinessResponse));


            JdyBusinessCheckResponse.JdyBusinessCheckData jdyBusinessCheckData = new JdyBusinessCheckResponse.JdyBusinessCheckData();
            if(CollectionUtil.isNotEmpty(businessCheckDataList)){
                jdyBusinessCheckData = businessCheckDataList.get(0);
            }

            JdyForeignReceiptApprovalResponse jdyForeignReceiptApprovalResponse = null;

            //外汇收款通知
            JdyForeignReceiptResponse.JdyForeignReceipt jdyForeignReceipt = new JdyForeignReceiptResponse.JdyForeignReceipt();
            if(CollectionUtil.isNotEmpty(jdyForeignReceiptList)){
                jdyForeignReceipt = jdyForeignReceiptList.get(0);

                JdyForeignReceiptApprovalRequest jdyForeignReceiptApprovalRequest = new JdyForeignReceiptApprovalRequest();
                jdyForeignReceiptApprovalRequest.setInstance_id(jdyForeignReceipt.get_id());
                jdyForeignReceiptApprovalRequest.setApp_id("62a99c7a9ae77800081eadeb");
                jdyForeignReceiptApprovalRequest.setEntry_id("62c7ebb8462b19000713ee25");
                jdyForeignReceiptApprovalRequest.setUrlName(JdyConstants.APPROVAL_URL);
                jdyForeignReceiptApprovalResponse = jdyClient.jdyCallV5(jdyForeignReceiptApprovalRequest);

            }

            TradeOrderCreateRequest tradeOrderCreateRequest = new TradeOrderCreateRequest();

            TradeOrderCreateRequest.Data tradeOrderData = new TradeOrderCreateRequest.Data();

            //商机-订单类型
            String widget_1639206267166 = jdyBusinessCheckData.get_widget_1639206267166();
            if(StringUtils.isNotEmpty(widget_1639206267166)) {
                tradeOrderData.set_widget_1655543660515(ValueString.builder().value(widget_1639206267166).build());
            }else{

                if(CollectionUtil.isNotEmpty(dataListReplace)) {
                    tradeOrderData.set_widget_1655543660515(ValueString.builder().value(dataListReplace.get(0).get_widget_1687154553436()).build());
                }else if(Objects.nonNull(crmBusinessResponse) && CollectionUtil.isNotEmpty(crmBusinessResponse.getData())){
                    tradeOrderData.set_widget_1655543660515(ValueString.builder().value(crmBusinessResponse.getData().get(0).get_widget_1638779089403()).build());
                }
            }
            tradeOrderData.set_widget_1655282871909(ValueString.builder().value(docNo).build());
            //商机编号
            String widget_1638846089425 = jdyBusinessCheckData.get_widget_1638846089425();
            if(StringUtils.isNotEmpty(widget_1638846089425)){
                tradeOrderData.set_widget_1655551709961(ValueString.builder().value(widget_1638846089425).build());
            }else{
                if(CollectionUtil.isNotEmpty(dataListReplace)) {
                    tradeOrderData.set_widget_1655551709961(ValueString.builder().value(dataListReplace.get(0).get_widget_1687154553448()).build());
                }else{
                    tradeOrderData.set_widget_1655551709961(ValueString.builder().value(smSoForQingDanResponse.getXjCode()).build());
                }
            }

            tradeOrderData.set_widget_1698301644687(ValueString.builder().value(smSoForQingDanResponse.getCusName()).build());
            tradeOrderData.set_widget_1655282872548(ValueString.builder().value(smSoForQingDanResponse.getCusName()).build());
            tradeOrderData.set_widget_1655282872779(ValueString.builder().value(jdyBusinessCheckData.get_widget_1647409817719()).build());
            tradeOrderData.set_widget_1655282872867(ValueString.builder().value(jdyBusinessCheckData.get_widget_1651110918714()).build());
            tradeOrderData.set_widget_1698301749645(ValueString.builder().value(Objects.nonNull(orderOneMap.get(smSoForQingDanResponse.getOrderTypeOne()))?orderOneMap.get(smSoForQingDanResponse.getOrderTypeOne()).getName():"").build());
            tradeOrderData.set_widget_1698301749646(ValueString.builder().value(Objects.nonNull(orderTwoMap.get(smSoForQingDanResponse.getOrderTypeTwo()))?orderTwoMap.get(smSoForQingDanResponse.getOrderTypeTwo()).getName():"").build());
            tradeOrderData.set_widget_1657240757702(ValueString.builder().value(jdyBusinessCheckData.get_widget_1639206268905()).build());
            tradeOrderData.set_widget_1657240757650(ValueString.builder().value(jdyBusinessCheckData.get_widget_1639206269041()).build());
            DdUserDto ddUser = null;
            if(StringUtils.isNotEmpty(smSoForQingDanResponse.getBusinessName())) {
                if(CollectionUtil.isNotEmpty(ddUsers)){
                    ddUser = ddUsers.get(0);
                    tradeOrderData.set_widget_1655282872115(ValueT.builder().value(ddUser.getUserId()).build());
                    tradeOrderData.set_widget_1698302197840(ValueString.builder().value(ddUser.getName()).build());
                }
            }

            tradeOrderData.set_widget_1698302197841(ValueString.builder().value(smSoForQingDanResponse.getOneYj()).build());

            if(StringUtils.isNotEmpty(smSoForQingDanResponse.getTwoSale())) {
                tradeOrderData.set_widget_1698302197842(ValueString.builder().value(erpMapper.selectOpera(smSoForQingDanResponse.getTwoSale()).get(0)).build());
            }
            tradeOrderData.set_widget_1698302197843(ValueString.builder().value(smSoForQingDanResponse.getTwoYj()).build());
            if(StringUtils.isNotEmpty(smSoForQingDanResponse.getThreeSale())) {
                tradeOrderData.set_widget_1698302197844(ValueString.builder().value(erpMapper.selectOpera(smSoForQingDanResponse.getThreeSale()).get(0)).build());
            }
            tradeOrderData.set_widget_1698302197845(ValueString.builder().value(smSoForQingDanResponse.getThreeYj()).build());
            tradeOrderData.set_widget_1655282872584(ValueString.builder().value(SODocStatusEnum.getDesc(smSoForQingDanResponse.getStatus())).build());
            tradeOrderData.set_widget_1657696317963(ValueString.builder().value(smSoForQingDanResponse.getContactCode()).build());
            tradeOrderData.set_widget_1678166600983(ValueString.builder().value(smSoForQingDanResponse.getWzfCode()).build());
            tradeOrderData.set_widget_1669798974158(ValueString.builder().value(CollectionUtil.isEmpty(jdyForeignReceiptList)?"":"是").build());
            if(Objects.nonNull(jdyForeignReceiptApprovalResponse)) {
                String status = "";
                if("0".equals(jdyForeignReceiptApprovalResponse.getStatus())){
                    status = "进行中";
                }else if("1".equals(jdyForeignReceiptApprovalResponse.getStatus())){
                    status = "流转完成";
                }else if("2".equals(jdyForeignReceiptApprovalResponse.getStatus())){
                    status = "手动结束";
                }


                tradeOrderData.set_widget_1669798974164(ValueString.builder().value(status).build());
                if("1".equals(jdyForeignReceiptApprovalResponse.getStatus())) {
                    tradeOrderData.set_widget_1669799964060(ValueString.builder().value("是").build());
                }
            }
            if(Objects.nonNull(jdyForeignReceipt.get_widget_1657520216835())) {
                tradeOrderData.set_widget_1667457234205(ValueString.builder().value(String.valueOf(jdyForeignReceipt.get_widget_1657520216835())).build());
            }
            if(StringUtils.isNotEmpty(smSoForQingDanResponse.getBusinessDate())) {
                tradeOrderData.set_widget_1655282872047(ValueString.builder().value(LocalDate.parse(smSoForQingDanResponse.getBusinessDate(), DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD_HH_MM_SS_SSS)).toString()).build());
            }

            if(StringUtils.isNotEmpty(soLineRespons.get(0).getRequireDate())) {
                tradeOrderData.set_widget_1655965343789(ValueString.builder().value(LocalDateTime.parse(soLineRespons.get(0).getRequireDate(), DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD_HH_MM_SS_SSS)).toString()).build());
            }
            //订货数量
            int qtyTu = soLineRespons.stream().mapToInt(SoLineResponse::getQtyTu).sum();
            tradeOrderData.set_widget_1655435380287(ValueString.builder().value(String.valueOf(qtyTu)).build());

            //累计执行数量
            int sumQtyTu = soLineRespons.stream().mapToInt(SoLineResponse::getSumQtyTu).sum();
            tradeOrderData.set_widget_1655435380339(ValueString.builder().value(String.valueOf(sumQtyTu)).build());
            //未发货数量
            int unShipNum = qtyTu-sumQtyTu;
            tradeOrderData.set_widget_1655435380443(ValueString.builder().value(String.valueOf(unShipNum)).build());
            tradeOrderData.set_widget_1655282873343(ValueString.builder().value(Objects.nonNull(contactMap.get(smSoForQingDanResponse.getContactTk()))?contactMap.get(smSoForQingDanResponse.getContactTk()).getName():"").build());
            //合同金额
            BigDecimal totalMoneyTC = soLineRespons.stream().map(SoLineResponse::getTotalMoneyTC).reduce(BigDecimal.ZERO,BigDecimal::add);
            tradeOrderData.set_widget_1655435380582(ValueString.builder().value(String.valueOf(totalMoneyTC)).build());

            //List<FinanceStandardSalesU9Response> orderCollectionPlan = erpMapper.getOrderCollectionPlan(docNo);
            //收款计划整单金额
            BigDecimal orderTotalAmount = new BigDecimal(StringUtils.isNotEmpty(smSoForQingDanResponse.getPrivateDescSeg19())?smSoForQingDanResponse.getPrivateDescSeg19():"0");

            tradeOrderData.set_widget_1655687438021(ValueString.builder().value(String.valueOf(orderTotalAmount)).build());
            if(Objects.nonNull(totalMoneyTC) && Objects.nonNull(orderTotalAmount)) {
                tradeOrderData.set_widget_1656041079770(ValueString.builder().value(String.valueOf(totalMoneyTC.subtract(orderTotalAmount))).build());
            }
            //标准出货
            List<ShipResponse> shipResponses = erpMapper.selectShip(docNo,null,null);
            if(CollectionUtil.isNotEmpty(shipResponses)){
                //发货金额
                BigDecimal totalMoney = shipResponses.stream().map(ShipResponse::getTotalMoneyTC).reduce(BigDecimal.ZERO,BigDecimal::add);
                tradeOrderData.set_widget_1655435380634(ValueString.builder().value(String.valueOf(totalMoney)).build());
                if(Objects.nonNull(totalMoneyTC) && Objects.nonNull(totalMoney)) {
                    tradeOrderData.set_widget_1655435380721(ValueString.builder().value(String.valueOf(totalMoneyTC.subtract(totalMoney))).build());
                }
                if(StringUtils.isNotEmpty(shipResponses.get(0).getShipConfirmDate())) {
                    tradeOrderData.set_widget_1697165246983(ValueString.builder().value(LocalDateTime.parse(shipResponses.get(0).getShipConfirmDate(), DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD_HH_MM_SS_SSS)).toString()).build());
                }

                //出货方式  0:客户自提  1：配送
                String shipMode = shipResponses.get(0).getShipMode();
                if(StringUtils.isNotEmpty(shipMode)){
                    if("0".equals(shipMode)){
                        tradeOrderData.set_widget_1655282873113(ValueString.builder().value("客户自提").build());
                    }
                    if("1".equals(shipMode)){
                        tradeOrderData.set_widget_1655282873113(ValueString.builder().value("配送").build());
                    }

                }
            }

            BigDecimal invoiceAmount = BigDecimal.ZERO;
            //应收单
            /*List<BillHeadResponse> billHeadResponses = erpMapper.selectBillHead(docNo);
            if(CollectionUtil.isNotEmpty(billHeadResponses)){
                List<TradeOrderCreateRequest._widget_1687853361294> invoiceList = new ArrayList<>();
                for(BillHeadResponse billHeadResponse:billHeadResponses){

                    TradeOrderCreateRequest._widget_1687853361294 widget_1687853361294 = new TradeOrderCreateRequest._widget_1687853361294();
                    widget_1687853361294.set_widget_1687853361296(ValueString.builder().value(billHeadResponse.getDocNo()).build());
                    widget_1687853361294.set_widget_1688539037622(ValueString.builder().value(billHeadResponse.getInvoiceNum()).build());
                    BigDecimal amount = billHeadResponse.getGoodsTax().add(billHeadResponse.getNonTax());
                    widget_1687853361294.set_widget_1687853361297(ValueString.builder().value(String.valueOf(amount)).build());
                    if(StringUtils.isNotEmpty(billHeadResponse.getInvoiceDate())) {
                        widget_1687853361294.set_widget_1687853361298(ValueString.builder().value(LocalDateTime.parse(billHeadResponse.getInvoiceDate(), DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD_HH_MM_SS_SSS)).toString()).build());
                    }
                    invoiceList.add(widget_1687853361294);
                    invoiceAmount = invoiceAmount.add(amount);
                }
                tradeOrderData.set_widget_1687853361294(ValueT.<List<TradeOrderCreateRequest._widget_1687853361294>>builder().value(invoiceList).build());
            }*/

            tradeOrderData.set_widget_1655687437986(ValueString.builder().value(String.valueOf(invoiceAmount)).build());
            //单据类型
            String docType = smSoForQingDanResponse.getDocType();
            tradeOrderData.set_widget_1655282872464(ValueString.builder().value(docType).build());

            //所属事业部
            //tradeOrderData.set_widget_1655282872415(ValueString.builder().value(ddDeptMap.get(Integer.parseInt(ddUsers.get(0).getDeptIdList())).get(0).getDeptName()).build());

            if(docType.contains("备件")){
                tradeOrderData.set_widget_1655543660629(ValueString.builder().value(jdyBusinessCheckData.get_widget_1639633669694()).build());
            }else if(docType.contains("散件")){
                tradeOrderData.set_widget_1655543660629(ValueString.builder().value(jdyBusinessCheckData.get_widget_1646363537887()).build());
            }else if(docType.contains("项目")){
                tradeOrderData.set_widget_1655543660629(ValueString.builder().value(jdyBusinessCheckData.get_widget_1639633670120()).build());
            }else if(docType.contains("出口")){
                tradeOrderData.set_widget_1655543660629(ValueString.builder().value(jdyBusinessCheckData.get_widget_1656989253608()).build());
            }
            tradeOrderData.set_widget_1655452868138(ValueString.builder().value(smSoForQingDanResponse.getXjCode()).build());

            JdyPackageFeeRegistrationRequest.Field feeField = new JdyPackageFeeRegistrationRequest.Field();
            if(StringUtils.isNotEmpty(jdyBusinessCheckData.get_widget_1638846089425())) {
                feeField.setField("_widget_1640567021722");
                feeField.setValue(Arrays.asList(jdyBusinessCheckData.get_widget_1638846089425()));
                feeField.setMethod(JdyConstants.JDY_EQ);
            }

            if(StringUtils.isNotEmpty(docNo)) {
                feeField.setField("_widget_1660545470659");
                feeField.setValue(Arrays.asList(docNo));
                feeField.setMethod(JdyConstants.JDY_EQ);
            }
            List<JdyPackageFeeRegistrationResponse.JdyPackageFeeRegistrationData> dataList = new ArrayList<>();
            if(StringUtils.isNotEmpty(jdyBusinessCheckData.get_widget_1638846089425()) || StringUtils.isNotEmpty(docNo)) {

                //包装方式
                jdyCommonService.queryJdyPackageFeeRegistration(dataList,null,null,JdyConstants.JDY_OR,Arrays.asList(feeField),100);
            }
            if(CollectionUtil.isNotEmpty(dataList) && CollectionUtil.isNotEmpty(dataList.get(0).get_widget_1625033017565())) {
                tradeOrderData.set_widget_1686633281220(ValueString.builder().value(dataList.get(0).get_widget_1625033017565().get(0).get_widget_1625122710325()).build());
            }

            //发票信息
            //查询应收款
            List<JdyFinanceOrderCollectionPlanQueryResponse.PlanData> financeList = new ArrayList<>();
            JdyFinanceOrderCollectionPlanQueryRequest.Field financeField = new JdyFinanceOrderCollectionPlanQueryRequest.Field();
            financeField.setField("_widget_1617264711816");
            financeField.setValue(Arrays.asList(docNo));
            financeField.setMethod(JdyConstants.JDY_EQ);
            jdyCommonService.queryJdyFinance(financeList,null,null,JdyConstants.AND,Arrays.asList(financeField),100);
            if(CollectionUtil.isNotEmpty(financeList)){
                //开票信息
                List<JdyFinanceOrderCollectionPlanQueryResponse._widget_1689040389514> invoices = financeList.get(0).get_widget_1689040389514();
                if(CollectionUtil.isNotEmpty(invoices)){
                    BigDecimal totalAmount = invoices.stream().map(JdyFinanceOrderCollectionPlanQueryResponse._widget_1689040389514::get_widget_1689040389518).reduce(BigDecimal.ZERO, BigDecimal::add);
                    //开票金额
                    tradeOrderData.set_widget_1655687437986(ValueString.builder().value(String.valueOf(totalAmount)).build());
                    tradeOrderData.set_widget_1688439097246(ValueString.builder().value("是").build());
                    List<JdyFinanceOrderCollectionPlanQueryResponse._widget_1689040389514> collectionPlanList = invoices.stream().filter(widget_16890403895141 -> StringUtils.isNotEmpty(widget_16890403895141.get_widget_1689040389519())).sorted(Comparator.comparing(JdyFinanceOrderCollectionPlanQueryResponse._widget_1689040389514::get_widget_1689040389519).reversed()).collect(Collectors.toList());
                    if(CollectionUtil.isNotEmpty(collectionPlanList)){
                        tradeOrderData.set_widget_1656051005028(ValueString.builder().value(collectionPlanList.get(0).get_widget_1689040389519()).build());

                    }
                }else{
                    tradeOrderData.set_widget_1688439097246(ValueString.builder().value("否").build());
                }
            }

            tradeOrderCreateRequest.setData(tradeOrderData);
            tradeOrderCreateRequest.setApp_id("62a99c7a9ae77800081eadeb");
            tradeOrderCreateRequest.setEntry_id("62a99cb6379333000811ba0e");

            List<TradeOrderDataV5Response.FromData> fromDataList = map.get(smSoForQingDanResponse.getDocNo());

            //已存在
            if(CollectionUtil.isNotEmpty(fromDataList)){
                tradeOrderCreateRequest.setUrlName(JdyConstants.ONE_UPDATE_URL);
                tradeOrderCreateRequest.setData_id(fromDataList.get(0).get_id());
            }else{
                List<TradeOrderDataV5Response.FromData> orderDatas = new ArrayList<>();
                tradeQdListIter(orderDatas,1,null,Arrays.asList(smSoForQingDanResponse.getDocNo()));
                if(CollectionUtil.isEmpty(orderDatas)) {
                    tradeOrderCreateRequest.setUrlName(JdyConstants.ONE_CREATE_URL);
                }
            }

            TradeOrderCreateResponse tradeOrderCreateResponse = jdyClient.jdyCallV5(tradeOrderCreateRequest);

            List<JdyTradeOrderDetailResponse.JdyTradeOrderDetail> dataListDetail = new ArrayList<>();
            JdyCommonDto.Field fieldT = new JdyCommonDto.Field();
            fieldT.setField("_widget_1655429177003");
            fieldT.setValue(Arrays.asList(docNo));
            fieldT.setMethod(JdyConstants.JDY_EQ);
            jdyCommonService.queryJdyTradeOrderDetail(dataListDetail,null,null,JdyConstants.AND,Arrays.asList(fieldT),300);

            if(CollectionUtil.isNotEmpty(dataListDetail)){
                List<String> ids = dataListDetail.stream().map(JdyTradeOrderDetailResponse.JdyTradeOrderDetail::get_id).collect(Collectors.toList());
                JdyCommonDeleteRequest jdyCommonDeleteRequest = new JdyCommonDeleteRequest();
                jdyCommonDeleteRequest.setApp_id("62a99c7a9ae77800081eadeb");
                jdyCommonDeleteRequest.setEntry_id("62abd838d26ead0007b50e51");
                jdyCommonDeleteRequest.setData_ids(ids);
                jdyCommonDeleteRequest.setUrlName(JdyConstants.BATCH_DELETE_URL);
                jdyClient.jdyCallV5(jdyCommonDeleteRequest);
            }

            //保存明细
            for(SoLineResponse soLineResponse:soLineRespons){

                String itemCode = soLineResponse.getItemCode();

                List<ItemMasterDto> itemMasterDtos = erpMapper.selectItemMaster(itemCode);

                TradeOrderDetailCreateRequest tradeOrderDetailCreateRequest = new TradeOrderDetailCreateRequest();

                TradeOrderDetailCreateRequest.Data data = new TradeOrderDetailCreateRequest.Data();
                data.set_widget_1655429177003(ValueString.builder().value(docNo).build());
                data.set_widget_1670906496929(ValueString.builder().value(jdyBusinessCheckData.get_widget_1639206269041()).build());
                data.set_widget_1698303888090(ValueString.builder().value(smSoForQingDanResponse.getCusName()).build());
                if(StringUtils.isNotEmpty(jdyBusinessCheckData.get_widget_1638846089425())){
                    data.set_widget_1657256445501(ValueString.builder().value(jdyBusinessCheckData.get_widget_1638846089425()).build());

                }else{
                    if(CollectionUtil.isNotEmpty(dataListReplace)) {
                        data.set_widget_1657256445501(ValueString.builder().value(dataListReplace.get(0).get_widget_1687154553448()).build());
                    }
                }
                if(StringUtils.isNotEmpty(jdyBusinessCheckData.get_widget_1647409817719())){
                    data.set_widget_1657256276656(ValueString.builder().value(jdyBusinessCheckData.get_widget_1647409817719()).build());
                }else {
                    if(CollectionUtil.isNotEmpty(dataListReplace)) {
                        data.set_widget_1657256276656(ValueString.builder().value(dataListReplace.get(0).get_widget_1687154553436()).build());
                    }
                }
                data.set_widget_1655429177158(ValueString.builder().value(soLineResponse.getItemCode()).build());
                data.set_widget_1655952157109(ValueString.builder().value(soLineResponse.getItemName()).build());
                if(CollectionUtil.isNotEmpty(itemMasterDtos)) {
                    data.set_widget_1655517465796(ValueString.builder().value(itemMasterDtos.get(0).getSpecs()).build());
                    data.set_widget_1657074480896(ValueString.builder().value(Objects.nonNull(brandMap.get(itemMasterDtos.get(0).getBrand())) ? brandMap.get(itemMasterDtos.get(0).getBrand()).getName() : "").build());
                    data.set_widget_1657503641159(ValueString.builder().value(Objects.nonNull(yanshouMap.get(itemMasterDtos.get(0).getYanShouStandart()))?yanshouMap.get(itemMasterDtos.get(0).getYanShouStandart()).getName():"").build());
                }
                if(StringUtils.isNotEmpty(smSoForQingDanResponse.getBusinessDate())) {
                    data.set_widget_1655517714883(ValueString.builder().value(LocalDateTime.parse(smSoForQingDanResponse.getBusinessDate(), DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD_HH_MM_SS_SSS)).toString()).build());
                }
                data.set_widget_1655429584393(ValueString.builder().value(SODocStatusEnum.getDesc(smSoForQingDanResponse.getStatus())).build());
                //订单数量
                int qtyTuLine = soLineResponse.getQtyTu();
                data.set_widget_1655514103309(ValueString.builder().value(String.valueOf(qtyTuLine)).build());

                //累计执行数量
                int sumQtyTuLine = soLineResponse.getSumQtyTu();
                data.set_widget_1655514103327(ValueString.builder().value(String.valueOf(sumQtyTuLine)).build());

                //未发货数量
                int unshipNum = qtyTuLine-sumQtyTuLine;
                data.set_widget_1655514103346(ValueString.builder().value(String.valueOf(unshipNum)).build());

                //00-3-U9采购订单数据（来源U9)
                List<U9PurchaseOrderResponse.U9PurchaseOrderData> u9PurchaseOrderDataList = new ArrayList<>();
                U9PurchaseOrderRequest.Field field = new U9PurchaseOrderRequest.Field();
                field.setField("_widget_1620357371301");
                field.setValue(Arrays.asList(itemCode));
                field.setMethod(JdyConstants.JDY_EQ);
                jdyCommonService.queryU9PurchaseOrder(u9PurchaseOrderDataList,null,null,JdyConstants.AND,Arrays.asList(field),300);
                U9PurchaseOrderResponse.U9PurchaseOrderData u9PurchaseOrderData = new U9PurchaseOrderResponse.U9PurchaseOrderData();
                if(CollectionUtil.isNotEmpty(u9PurchaseOrderDataList)) {
                    u9PurchaseOrderData = u9PurchaseOrderDataList.get(0);
                }
                List<ItemInfoDto> itemInfosByItemCode = erpMapper.selectStock(Arrays.asList("原材料库(岱鼎)","产成品库(岱鼎)"),itemCode);

                //库存数量
                int stockNum = 0;
                if(CollectionUtil.isNotEmpty(itemInfosByItemCode)){
                    stockNum = itemInfosByItemCode.stream().mapToInt(ItemInfoDto::getBalQty).sum();
                }
                data.set_widget_1655952157145(ValueString.builder().value(String.valueOf(stockNum-unshipNum)).build());
                //交期
                String requireDate = soLineResponse.getRequireDate();
                if(StringUtils.isNotEmpty(requireDate)) {
                    data.set_widget_1655866771194(ValueString.builder().value(LocalDateTime.parse(requireDate,DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD_HH_MM_SS_SSS)).toString()).build());
                }
                //要求交货日期
                String widget_1620357371308 = u9PurchaseOrderData.get_widget_1620357371308();
                if(StringUtils.isNotEmpty(widget_1620357371308)) {
                    data.set_widget_1655946941649(ValueString.builder().value(DateUtils.utcToLocalDateTime(widget_1620357371308).toString()).build());
                }
                if(StringUtils.isNotEmpty(requireDate)) {
                    LocalDateTime requireDateLocalDateTime = LocalDateTime.parse(requireDate, DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD_HH_MM_SS_SSS)).plusDays(3);
                    data.set_widget_1656306191059(ValueString.builder().value(requireDateLocalDateTime.toString()).build());
                }

                data.set_widget_1655886436536(ValueString.builder().value(u9PurchaseOrderData.get_widget_1620357371297()).build());
                data.set_widget_1655780931428(ValueString.builder().value(LocalDateTime.now().toString()).build());
                data.set_widget_1655514103409(ValueString.builder().value(String.valueOf(stockNum)).build());
                //当前在途数量
                int waitingReceveNum = 0;
                List<U9PurchaseOrderResponse.U9PurchaseOrderData> purchaseOrderDataList = u9PurchaseOrderDataList.stream().filter(u9PurchaseOrderDataT -> "已审核".equals(u9PurchaseOrderDataT.get_widget_1620357371295())).collect(Collectors.toList());
                if(CollectionUtil.isNotEmpty(purchaseOrderDataList)){
                    waitingReceveNum = purchaseOrderDataList.stream().map(U9PurchaseOrderResponse.U9PurchaseOrderData::get_widget_1620357371305).reduce(Integer::sum).get();
                }
                data.set_widget_1655514103816(ValueString.builder().value(String.valueOf(waitingReceveNum)).build());
                //采购数量
                Integer purchaseNum = u9PurchaseOrderData.get_widget_1620357371304();
                data.set_widget_1655780931276(ValueString.builder().value(String.valueOf(purchaseNum)).build());
                if(Objects.nonNull(purchaseNum)) {
                    data.set_widget_1655780931312(ValueString.builder().value(String.valueOf(stockNum - purchaseNum)).build());
                }
                data.set_widget_1655514103130(ValueString.builder().value(soLineResponse.getId()).build());
                if(StringUtils.isNotEmpty(widget_1620357371308)) {
                    data.set_widget_1655866771205(ValueString.builder().value(DateUtils.utcToLocalDateTime(widget_1620357371308).toString()).build());
                    data.set_widget_1655946941188(ValueString.builder().value(DateUtils.utcToLocalDateTime(widget_1620357371308).toString()).build());
                    data.set_widget_1655866771526(ValueString.builder().value(DateUtils.utcToLocalDateTime(widget_1620357371308).toString()).build());
                }
                if(Objects.nonNull(ddUser)) {
                    data.set_widget_1655517465892(ValueString.builder().value(ddUser.getUserId()).build());
                }
                data.set_widget_1657088767080(ValueString.builder().value(smSoForQingDanResponse.getDocType()).build());
                data.set_widget_1657074480988(ValueString.builder().value(String.valueOf(soLineResponse.getTotalMoneyTC())).build());
                data.set_widget_1657074481057(ValueString.builder().value(String.valueOf(soLineResponse.getOrderPriceTC())).build());
                data.set_widget_1657089268148(ValueString.builder().value(jdyBusinessCheckData.get_widget_1638846089425()).build());
                tradeOrderDetailCreateRequest.setData(data);
                tradeOrderDetailCreateRequest.setApp_id("62a99c7a9ae77800081eadeb");
                tradeOrderDetailCreateRequest.setEntry_id("62abd838d26ead0007b50e51");
                tradeOrderDetailCreateRequest.setUrlName(JdyConstants.ONE_CREATE_URL);
                jdyClient.jdyCallV5(tradeOrderDetailCreateRequest);
            }
        }
    }

    /**
     * 创建贸易类订单清单和贸易类订单明细
     */
    public synchronized void createTradeOrder(String  docNo){

        //查询2022-06-01至今的u9标准销售数据
        List<SmSoForQingDanResponse> smSoForQingDanResponses = erpMapper.selectSmSoForQingDan(docNo,null);
        if(CollectionUtil.isEmpty(smSoForQingDanResponses)){
            return;
        }

       /* //测试代码
        List<SmSoForQingDanResponse> list = new ArrayList<>();
        list.add(smSoForQingDanResponses.get(0));
        smSoForQingDanResponses = list;*/
        /*List<String> docNos = smSoForQingDanResponses.stream().map(SmSoForQingDanResponse::getDocNo).collect(Collectors.toList());
        List<TradeOrderDataV5Response.FromData> orderDatas = new ArrayList<>();

        tradeQdListIter(orderDatas, docNos.size(), null, docNos);

        Map<String, List<TradeOrderDataV5Response.FromData>> map = orderDatas.stream().collect(Collectors.groupingBy(TradeOrderDataV5Response.FromData::get_widget_1655282871909));

        //已存在
        List<String> orderNos = orderDatas.stream().map(TradeOrderDataV5Response.FromData::get_widget_1655282871909).collect(Collectors.toList());
*/

        for(SmSoForQingDanResponse smSoForQingDanResponse:smSoForQingDanResponses) {
            List<TradeOrderDataV5Response.FromData> orderDatas = new ArrayList<>();

            tradeQdListIter(orderDatas, 1, null, Arrays.asList(smSoForQingDanResponse.getDocNo()));

            Map<String, List<TradeOrderDataV5Response.FromData>> map = new HashMap<>();
            map.put(smSoForQingDanResponse.getDocNo(),orderDatas);

            if(CollectionUtil.isEmpty(orderDatas)) {


                //过滤掉已经存在的优先拉取不存在的
                tradeCommon(Arrays.asList(smSoForQingDanResponse), map);
            }else{

               if(map.get(smSoForQingDanResponse.getDocNo()).get(0).get_widget_1655435380443() != 0) {
                   tradeCommon(Arrays.asList(smSoForQingDanResponse), map);
               }
            }
        }
    }

    /**
     * 同步04-1数据
     */
    @Transactional
    public void synJdy04T1(Jdy04T1CreateRequest request){

        //项目编号
        String projectCode = request.get_widget_1612402346650();

        List<JdyProjectTaskAllocationResponse.JdyProjectTaskAllocationData> dataList = new ArrayList<>();

        int limit = 300;
        JdyProjectTaskAllocationRequest.Field fieldAllocation = new JdyProjectTaskAllocationRequest.Field();
        fieldAllocation.setField("_widget_1612402346650");
        fieldAllocation.setMethod(JdyConstants.JDY_EQ);
        fieldAllocation.setValue(Arrays.asList(projectCode));

        jdyCommonService.queryJdyProjectTaskAllocation(dataList,null,null, JdyConstants.AND,Arrays.asList(fieldAllocation),limit);

        if(CollectionUtil.isNotEmpty(dataList)){

            for(JdyProjectTaskAllocationResponse.JdyProjectTaskAllocationData data:dataList){

                log.info("项目号{}",projectCode);
                Jdy041 jdy041T = jdy041Mapper.selectOne(Wrappers.<Jdy041>lambdaQuery().eq(Jdy041::getProjectCode, projectCode));
                /*//已经存在，先不支持修改
                if(Objects.nonNull(jdy041T)){
                    continue;
                }*/

                //商机编号
                String sjCode = data.get_widget_1612401382067();

                String dataId = data.get_id();

                Jdy041 jdy041 = new Jdy041();
                jdy041.setProjectManager(Objects.nonNull(data.get_widget_1612401382408())?data.get_widget_1612401382408().getName():"");
                jdy041.setCompanyName(data.get_widget_1622009557077());
                jdy041.setSjCode(sjCode);
                jdy041.setCusName(data.get_widget_1612507447043());
                jdy041.setDeliveryDate(StringUtils.isNotEmpty(data.get_widget_1612401382519())? DateUtils.utcToLocalDate(data.get_widget_1612401382519()):null);
                jdy041.setProjectCode(projectCode);
                jdy041.setDeliveryDay(data.get_widget_1612795162010());
                jdy041.setDataId(dataId);
                if(Objects.nonNull(jdy041T)){
                    jdy041.setId(jdy041T.getId());
                }

                //查询04-3合同管理
                List<JdyContractManagerResponse.JdyContractManagerData> contractManagerDataList = new ArrayList<>();
                JdyContractManagerRequest.Field field = new JdyContractManagerRequest.Field();
                field.setField("_widget_1612346722533");
                field.setValue(Arrays.asList(projectCode));
                field.setMethod(JdyConstants.JDY_EQ);
                jdyCommonService.queryJdyContractManager(contractManagerDataList,null,null,JdyConstants.AND,Arrays.asList(field),1);
                if(CollectionUtil.isNotEmpty(contractManagerDataList)){
                    JdyContractManagerResponse.JdyContractManagerData jdyContractManagerData = contractManagerDataList.get(0);
                    jdy041.setContractCode(jdyContractManagerData.get_widget_1675729553648());
                    jdy041.setContractTerm(jdyContractManagerData.get_widget_1676007063288());
                    jdy041.setOrderGrade1(jdyContractManagerData.get_widget_1675729807866());
                    jdy041.setOrderGrade2(jdyContractManagerData.get_widget_1675729807867());
                }
                if(Objects.nonNull(jdy041T)){
                    jdy041Mapper.updateById(jdy041);
                }else {
                    jdy041Mapper.insert(jdy041);
                }

                //已存在
                if(Objects.nonNull(jdy041T)){
                    jdy041SaleMapper.delete(Wrappers.<Jdy041Sale>lambdaUpdate().eq(Jdy041Sale::getJdy041Id,jdy041T.getId()));
                    jdy041ItemMapper.delete(Wrappers.<Jdy041Item>lambdaUpdate().eq(Jdy041Item::getJdy041Id,jdy041T.getId()));
                    jdy041UrlMapper.delete(Wrappers.<Jdy041Url>lambdaUpdate().eq(Jdy041Url::getJdy04Id,jdy041T.getId()));
                    jdy041PayTypeMapper.delete(Wrappers.<Jdy041PayType>lambdaUpdate().eq(Jdy041PayType::getJdy041Id,jdy041T.getId()));
                }

                if(CollectionUtil.isNotEmpty(contractManagerDataList)){
                    JdyContractManagerResponse.JdyContractManagerData jdyContractManagerData = contractManagerDataList.get(0);
                    List<JdyContractManagerResponse._widget_1612318077211> widget_1612318077211List = jdyContractManagerData.get_widget_1612318077211();
                    for(JdyContractManagerResponse._widget_1612318077211 widget_1612318077211:widget_1612318077211List){
                        Jdy041PayType jdy041PayType = new Jdy041PayType();
                        jdy041PayType.setJdy041Id(jdy041.getId());
                        jdy041PayType.setPayType(widget_1612318077211.get_widget_1612318077229());
                        jdy041PayType.setPayDate(DateUtils.utcToLocalDate(widget_1612318077211.get_widget_1612503741306()));
                        jdy041PayTypeMapper.insert(jdy041PayType);
                    }
                }

                //查询项目报价
                List<JdyProjectQuotationResponse.JdyProjectQuotationData> projectQuotationDataList = new ArrayList<>();
                JdyProjectQuotationRequest.Field fieldT = new JdyProjectQuotationRequest.Field();
                fieldT.setField("_widget_1608688709531");
                fieldT.setValue(Arrays.asList(sjCode));
                fieldT.setMethod(JdyConstants.JDY_EQ);
                jdyCommonService.queryJdyProjectQuotation(projectQuotationDataList,null,null,JdyConstants.AND,Arrays.asList(fieldT),1);
                if(CollectionUtil.isNotEmpty(projectQuotationDataList)){
                    List<JdyProjectQuotationResponse.SaleInfo> saleInfos = projectQuotationDataList.get(0).get_widget_1608692167263();
                    if(CollectionUtil.isNotEmpty(saleInfos)){
                        List<Jdy041Sale> jdy041Sales = new ArrayList<>();
                        saleInfos = saleInfos.stream().sorted(Comparator.comparing(JdyProjectQuotationResponse.SaleInfo::get_id)).collect(Collectors.toList());
                        int i = 1;
                        for(JdyProjectQuotationResponse.SaleInfo saleInfo:saleInfos){
                            Jdy041Sale jdy041Sale = new Jdy041Sale();
                            jdy041Sale.setSort(i);
                            jdy041Sale.setJdy041Id(jdy041.getId());
                            jdy041Sale.setSaleName(saleInfo.get_widget_1608692167312().getName());
                            jdy041Sale.setSalePerformance(String.valueOf(saleInfo.get_widget_1608692167410()));
                            jdy041Sales.add(jdy041Sale);
                            i++;
                        }
                        jdy041SaleMapper.insertBatchSomeColumn(jdy041Sales);
                    }
                }

                //查询04-3.1项目产品明细
                List<JdyProjectProductInfoResponse.JdyProjectProductInfoData> jdyProjectProductInfoDataList = new ArrayList<>();
                JdyProjectProductInfoRequest.Field fieldTT = new JdyProjectProductInfoRequest.Field();
                fieldTT.setField("_widget_1608271147780");
                fieldTT.setValue(Arrays.asList(projectCode));
                fieldTT.setMethod(JdyConstants.JDY_EQ);
                jdyCommonService.queryJdyProjectProductInfo(jdyProjectProductInfoDataList,null,null,JdyConstants.AND,Arrays.asList(fieldTT),500);

                //料号为key
                Map<String, List<JdyProjectProductInfoResponse.JdyProjectProductInfoData>> map = null;
                if(CollectionUtil.isNotEmpty(jdyProjectProductInfoDataList)){

                    map = jdyProjectProductInfoDataList.stream().collect(Collectors.groupingBy(JdyProjectProductInfoResponse.JdyProjectProductInfoData::get_widget_1608271147762));
                }

                //明细
                List<Jdy041Item> jdy041Items = new ArrayList<>();

                //标准系统料号清单_1
                List<JdyProjectTaskAllocationResponse._widget_1621826922707> widget_1621826922707List = data.get_widget_1621826922707();
                for(JdyProjectTaskAllocationResponse._widget_1621826922707 widget_1621826922707:widget_1621826922707List){
                    Jdy041Item jdy041Item = new Jdy041Item();
                    jdy041Item.setJdy041Id(jdy041.getId());
                    String itemCode = widget_1621826922707.get_widget_1621826922708();
                    if(StringUtils.isEmpty(itemCode)){
                        continue;
                    }
                    jdy041Item.setItemCode(itemCode);
                    jdy041Item.setNumber(StringUtils.isNotEmpty(widget_1621826922707.get_widget_1621826922710())?Integer.valueOf(widget_1621826922707.get_widget_1621826922710()):0);
                    if(Objects.nonNull(map)){
                        List<JdyProjectProductInfoResponse.JdyProjectProductInfoData> jdyProjectProductInfoDataListT = map.get(jdy041Item.getItemCode());
                        if(CollectionUtil.isNotEmpty(jdyProjectProductInfoDataListT)){
                            JdyProjectProductInfoResponse.JdyProjectProductInfoData jdyProjectProductInfoData = jdyProjectProductInfoDataListT.get(0);
                            //订单数量
                            BigDecimal widget_1608271147742 = jdyProjectProductInfoData.get_widget_1608271147742();
                            //本表单货物合同价（填写）
                            BigDecimal widget_1649441076180 = jdyProjectProductInfoData.get_widget_1649441076180();
                            if(Objects.nonNull(widget_1608271147742) && Objects.nonNull(widget_1649441076180) && widget_1608271147742.compareTo(BigDecimal.ZERO) != 0){
                                jdy041Item.setPrice(widget_1649441076180.divide(widget_1608271147742,2,BigDecimal.ROUND_HALF_UP));
                            }
                        }
                    }
                    jdy041Items.add(jdy041Item);
                }

                //非标系统料号清单_1
                List<JdyProjectTaskAllocationResponse._widget_1648626264484> _widget_1648626264484List = data.get_widget_1648626264484();
                for(JdyProjectTaskAllocationResponse._widget_1648626264484 _widget_1648626264484:_widget_1648626264484List){
                    Jdy041Item jdy041Item = new Jdy041Item();
                    jdy041Item.setJdy041Id(jdy041.getId());
                    jdy041Item.setItemCode(_widget_1648626264484.get_widget_1648626264485());
                    jdy041Item.setNumber(StringUtils.isNotEmpty(_widget_1648626264484.get_widget_1648626264487())?Integer.valueOf(_widget_1648626264484.get_widget_1648626264487()):0);
                    if(Objects.nonNull(map)){
                        List<JdyProjectProductInfoResponse.JdyProjectProductInfoData> jdyProjectProductInfoDataListT = map.get(jdy041Item.getItemCode());
                        if(CollectionUtil.isNotEmpty(jdyProjectProductInfoDataListT)){
                            JdyProjectProductInfoResponse.JdyProjectProductInfoData jdyProjectProductInfoData = jdyProjectProductInfoDataListT.get(0);
                            //订单数量
                            BigDecimal widget_1608271147742 = jdyProjectProductInfoData.get_widget_1608271147742();
                            //本表单货物合同价（填写）
                            BigDecimal widget_1649441076180 = jdyProjectProductInfoData.get_widget_1649441076180();
                            if(Objects.nonNull(widget_1608271147742) && Objects.nonNull(widget_1649441076180) && widget_1608271147742.compareTo(BigDecimal.ZERO) != 0){
                                jdy041Item.setPrice(widget_1649441076180.divide(widget_1608271147742,2,BigDecimal.ROUND_HALF_UP));
                            }
                        }
                    }
                    jdy041Items.add(jdy041Item);
                }


                //散件清单_1
                List<JdyProjectTaskAllocationResponse._widget_1650782217261> _widget_1650782217261List = data.get_widget_1650782217261();
                for(JdyProjectTaskAllocationResponse._widget_1650782217261 _widget_1650782217261:_widget_1650782217261List){
                    Jdy041Item jdy041Item = new Jdy041Item();
                    jdy041Item.setJdy041Id(jdy041.getId());
                    jdy041Item.setItemCode(_widget_1650782217261.get_widget_1650782217262());
                    jdy041Item.setNumber(StringUtils.isNotEmpty(_widget_1650782217261.get_widget_1650782217265())?Integer.valueOf(_widget_1650782217261.get_widget_1650782217265()):0);
                    if(Objects.nonNull(map)){
                        List<JdyProjectProductInfoResponse.JdyProjectProductInfoData> jdyProjectProductInfoDataListT = map.get(jdy041Item.getItemCode());
                        if(CollectionUtil.isNotEmpty(jdyProjectProductInfoDataListT)){
                            JdyProjectProductInfoResponse.JdyProjectProductInfoData jdyProjectProductInfoData = jdyProjectProductInfoDataListT.get(0);
                            //订单数量
                            BigDecimal widget_1608271147742 = jdyProjectProductInfoData.get_widget_1608271147742();
                            //本表单货物合同价（填写）
                            BigDecimal widget_1649441076180 = jdyProjectProductInfoData.get_widget_1649441076180();
                            if(Objects.nonNull(widget_1608271147742) && Objects.nonNull(widget_1649441076180) && widget_1608271147742.compareTo(BigDecimal.ZERO) != 0){
                                jdy041Item.setPrice(widget_1649441076180.divide(widget_1608271147742,2,BigDecimal.ROUND_HALF_UP));
                            }
                        }
                    }
                    jdy041Items.add(jdy041Item);
                }

                //标准系统料清单_新_1
                List<JdyProjectTaskAllocationResponse._widget_1638773015543> _widget_1638773015543List = data.get_widget_1638773015543();
                for(JdyProjectTaskAllocationResponse._widget_1638773015543 _widget_1638773015543:_widget_1638773015543List){
                    Jdy041Item jdy041Item = new Jdy041Item();
                    jdy041Item.setJdy041Id(jdy041.getId());
                    jdy041Item.setItemCode(_widget_1638773015543.get_widget_1638773015544());
                    jdy041Item.setNumber(StringUtils.isNotEmpty(_widget_1638773015543.get_widget_1638773015546())?Integer.valueOf(_widget_1638773015543.get_widget_1638773015546()):0);
                    if(Objects.nonNull(map)){
                        List<JdyProjectProductInfoResponse.JdyProjectProductInfoData> jdyProjectProductInfoDataListT = map.get(jdy041Item.getItemCode());
                        if(CollectionUtil.isNotEmpty(jdyProjectProductInfoDataListT)){
                            JdyProjectProductInfoResponse.JdyProjectProductInfoData jdyProjectProductInfoData = jdyProjectProductInfoDataListT.get(0);
                            //订单数量
                            BigDecimal widget_1608271147742 = jdyProjectProductInfoData.get_widget_1608271147742();
                            //本表单货物合同价（填写）
                            BigDecimal widget_1649441076180 = jdyProjectProductInfoData.get_widget_1649441076180();
                            if(Objects.nonNull(widget_1608271147742) && Objects.nonNull(widget_1649441076180) && widget_1608271147742.compareTo(BigDecimal.ZERO) != 0){
                                jdy041Item.setPrice(widget_1649441076180.divide(widget_1608271147742,2,BigDecimal.ROUND_HALF_UP));
                            }
                        }
                    }
                    jdy041Items.add(jdy041Item);
                }

                //非标系统清单_新_1
                List<JdyProjectTaskAllocationResponse._widget_1638773015499> _widget_1638773015499List = data.get_widget_1638773015499();
                for(JdyProjectTaskAllocationResponse._widget_1638773015499 _widget_1638773015499:_widget_1638773015499List){
                    Jdy041Item jdy041Item = new Jdy041Item();
                    jdy041Item.setJdy041Id(jdy041.getId());
                    jdy041Item.setItemCode(_widget_1638773015499.get_widget_1638773015500());
                    jdy041Item.setNumber(StringUtils.isNotEmpty(_widget_1638773015499.get_widget_1638773015503())?Integer.valueOf(_widget_1638773015499.get_widget_1638773015503()):0);
                    if(Objects.nonNull(map)){
                        List<JdyProjectProductInfoResponse.JdyProjectProductInfoData> jdyProjectProductInfoDataListT = map.get(jdy041Item.getItemCode());
                        if(CollectionUtil.isNotEmpty(jdyProjectProductInfoDataListT)){
                            JdyProjectProductInfoResponse.JdyProjectProductInfoData jdyProjectProductInfoData = jdyProjectProductInfoDataListT.get(0);
                            //订单数量
                            BigDecimal widget_1608271147742 = jdyProjectProductInfoData.get_widget_1608271147742();
                            //本表单货物合同价（填写）
                            BigDecimal widget_1649441076180 = jdyProjectProductInfoData.get_widget_1649441076180();
                            if(Objects.nonNull(widget_1608271147742) && Objects.nonNull(widget_1649441076180) && widget_1608271147742.compareTo(BigDecimal.ZERO) != 0){
                                jdy041Item.setPrice(widget_1649441076180.divide(widget_1608271147742,2,BigDecimal.ROUND_HALF_UP));
                            }
                        }
                    }
                    jdy041Items.add(jdy041Item);
                }

                //标准系统料号清单_2
                List<JdyProjectTaskAllocationResponse._widget_1648639552848> _widget_1648639552848List = data.get_widget_1648639552848();
                for(JdyProjectTaskAllocationResponse._widget_1648639552848 _widget_1648639552848:_widget_1648639552848List){
                    Jdy041Item jdy041Item = new Jdy041Item();
                    jdy041Item.setJdy041Id(jdy041.getId());
                    jdy041Item.setItemCode(_widget_1648639552848.get_widget_1648639552849());
                    jdy041Item.setNumber(StringUtils.isNotEmpty(_widget_1648639552848.get_widget_1648639552851())?Integer.valueOf(_widget_1648639552848.get_widget_1648639552851()):0);
                    if(Objects.nonNull(map)){
                        List<JdyProjectProductInfoResponse.JdyProjectProductInfoData> jdyProjectProductInfoDataListT = map.get(jdy041Item.getItemCode());
                        if(CollectionUtil.isNotEmpty(jdyProjectProductInfoDataListT)){
                            JdyProjectProductInfoResponse.JdyProjectProductInfoData jdyProjectProductInfoData = jdyProjectProductInfoDataListT.get(0);
                            //订单数量
                            BigDecimal widget_1608271147742 = jdyProjectProductInfoData.get_widget_1608271147742();
                            //本表单货物合同价（填写）
                            BigDecimal widget_1649441076180 = jdyProjectProductInfoData.get_widget_1649441076180();
                            if(Objects.nonNull(widget_1608271147742) && Objects.nonNull(widget_1649441076180) && widget_1608271147742.compareTo(BigDecimal.ZERO) != 0){
                                jdy041Item.setPrice(widget_1649441076180.divide(widget_1608271147742,2,BigDecimal.ROUND_HALF_UP));
                            }
                        }
                    }
                    jdy041Items.add(jdy041Item);
                }

                //非标系统料号清单_2
                List<JdyProjectTaskAllocationResponse._widget_1623312109034> _widget_1623312109034List = data.get_widget_1623312109034();
                for(JdyProjectTaskAllocationResponse._widget_1623312109034 _widget_1623312109034:_widget_1623312109034List){
                    Jdy041Item jdy041Item = new Jdy041Item();
                    jdy041Item.setJdy041Id(jdy041.getId());
                    jdy041Item.setItemCode(_widget_1623312109034.get_widget_1623312109035());
                    jdy041Item.setNumber(StringUtils.isNotEmpty(_widget_1623312109034.get_widget_1623312109037())?Integer.valueOf(_widget_1623312109034.get_widget_1623312109037()):0);
                    if(Objects.nonNull(map)){
                        List<JdyProjectProductInfoResponse.JdyProjectProductInfoData> jdyProjectProductInfoDataListT = map.get(jdy041Item.getItemCode());
                        if(CollectionUtil.isNotEmpty(jdyProjectProductInfoDataListT)){
                            JdyProjectProductInfoResponse.JdyProjectProductInfoData jdyProjectProductInfoData = jdyProjectProductInfoDataListT.get(0);
                            //订单数量
                            BigDecimal widget_1608271147742 = jdyProjectProductInfoData.get_widget_1608271147742();
                            //本表单货物合同价（填写）
                            BigDecimal widget_1649441076180 = jdyProjectProductInfoData.get_widget_1649441076180();
                            if(Objects.nonNull(widget_1608271147742) && Objects.nonNull(widget_1649441076180) && widget_1608271147742.compareTo(BigDecimal.ZERO) != 0){
                                jdy041Item.setPrice(widget_1649441076180.divide(widget_1608271147742,2,BigDecimal.ROUND_HALF_UP));
                            }
                        }
                    }
                    jdy041Items.add(jdy041Item);
                }

                //散件清单_2
                List<JdyProjectTaskAllocationResponse._widget_1650782218106> _widget_1650782218106List = data.get_widget_1650782218106();
                for(JdyProjectTaskAllocationResponse._widget_1650782218106 _widget_1650782218106:_widget_1650782218106List){
                    Jdy041Item jdy041Item = new Jdy041Item();
                    jdy041Item.setJdy041Id(jdy041.getId());
                    jdy041Item.setItemCode(_widget_1650782218106.get_widget_1650782218107());
                    jdy041Item.setNumber(StringUtils.isNotEmpty(_widget_1650782218106.get_widget_1650782218111())?Integer.valueOf(_widget_1650782218106.get_widget_1650782218111()):0);
                    if(Objects.nonNull(map)){
                        List<JdyProjectProductInfoResponse.JdyProjectProductInfoData> jdyProjectProductInfoDataListT = map.get(jdy041Item.getItemCode());
                        if(CollectionUtil.isNotEmpty(jdyProjectProductInfoDataListT)){
                            JdyProjectProductInfoResponse.JdyProjectProductInfoData jdyProjectProductInfoData = jdyProjectProductInfoDataListT.get(0);
                            //订单数量
                            BigDecimal widget_1608271147742 = jdyProjectProductInfoData.get_widget_1608271147742();
                            //本表单货物合同价（填写）
                            BigDecimal widget_1649441076180 = jdyProjectProductInfoData.get_widget_1649441076180();
                            if(Objects.nonNull(widget_1608271147742) && Objects.nonNull(widget_1649441076180) && widget_1608271147742.compareTo(BigDecimal.ZERO) != 0){
                                jdy041Item.setPrice(widget_1649441076180.divide(widget_1608271147742,2,BigDecimal.ROUND_HALF_UP));
                            }
                        }
                    }
                    jdy041Items.add(jdy041Item);
                }

                //标准系统料清单_新_2
                List<JdyProjectTaskAllocationResponse._widget_1638773015716> _widget_1638773015716List = data.get_widget_1638773015716();
                for(JdyProjectTaskAllocationResponse._widget_1638773015716 _widget_1638773015716:_widget_1638773015716List){
                    Jdy041Item jdy041Item = new Jdy041Item();
                    jdy041Item.setJdy041Id(jdy041.getId());
                    jdy041Item.setItemCode(_widget_1638773015716.get_widget_1638773015717());
                    jdy041Item.setNumber(StringUtils.isNotEmpty(_widget_1638773015716.get_widget_1638773015719())?Integer.valueOf(_widget_1638773015716.get_widget_1638773015719()):0);
                    if(Objects.nonNull(map)){
                        List<JdyProjectProductInfoResponse.JdyProjectProductInfoData> jdyProjectProductInfoDataListT = map.get(jdy041Item.getItemCode());
                        if(CollectionUtil.isNotEmpty(jdyProjectProductInfoDataListT)){
                            JdyProjectProductInfoResponse.JdyProjectProductInfoData jdyProjectProductInfoData = jdyProjectProductInfoDataListT.get(0);
                            //订单数量
                            BigDecimal widget_1608271147742 = jdyProjectProductInfoData.get_widget_1608271147742();
                            //本表单货物合同价（填写）
                            BigDecimal widget_1649441076180 = jdyProjectProductInfoData.get_widget_1649441076180();
                            if(Objects.nonNull(widget_1608271147742) && Objects.nonNull(widget_1649441076180) && widget_1608271147742.compareTo(BigDecimal.ZERO) != 0){
                                jdy041Item.setPrice(widget_1649441076180.divide(widget_1608271147742,2,BigDecimal.ROUND_HALF_UP));
                            }
                        }
                    }
                    jdy041Items.add(jdy041Item);
                }

                //非标系统清单_新_2
                List<JdyProjectTaskAllocationResponse._widget_1638773015672> _widget_1638773015672List = data.get_widget_1638773015672();
                for(JdyProjectTaskAllocationResponse._widget_1638773015672 _widget_1638773015672:_widget_1638773015672List){
                    Jdy041Item jdy041Item = new Jdy041Item();
                    jdy041Item.setJdy041Id(jdy041.getId());
                    jdy041Item.setItemCode(_widget_1638773015672.get_widget_1638773015673());
                    jdy041Item.setNumber(StringUtils.isNotEmpty(_widget_1638773015672.get_widget_1638773015676())?Integer.valueOf(_widget_1638773015672.get_widget_1638773015676()):0);
                    if(Objects.nonNull(map)){
                        List<JdyProjectProductInfoResponse.JdyProjectProductInfoData> jdyProjectProductInfoDataListT = map.get(jdy041Item.getItemCode());
                        if(CollectionUtil.isNotEmpty(jdyProjectProductInfoDataListT)){
                            JdyProjectProductInfoResponse.JdyProjectProductInfoData jdyProjectProductInfoData = jdyProjectProductInfoDataListT.get(0);
                            //订单数量
                            BigDecimal widget_1608271147742 = jdyProjectProductInfoData.get_widget_1608271147742();
                            //本表单货物合同价（填写）
                            BigDecimal widget_1649441076180 = jdyProjectProductInfoData.get_widget_1649441076180();
                            if(Objects.nonNull(widget_1608271147742) && Objects.nonNull(widget_1649441076180) && widget_1608271147742.compareTo(BigDecimal.ZERO) != 0){
                                jdy041Item.setPrice(widget_1649441076180.divide(widget_1608271147742,2,BigDecimal.ROUND_HALF_UP));
                            }
                        }
                    }
                    jdy041Items.add(jdy041Item);
                }

                //标准系统料号清单_3
                List<JdyProjectTaskAllocationResponse._widget_1648639554077> _widget_1648639554077List = data.get_widget_1648639554077();
                for(JdyProjectTaskAllocationResponse._widget_1648639554077 _widget_1648639554077:_widget_1648639554077List){
                    Jdy041Item jdy041Item = new Jdy041Item();
                    jdy041Item.setJdy041Id(jdy041.getId());
                    jdy041Item.setItemCode(_widget_1648639554077.get_widget_1648639554078());
                    jdy041Item.setNumber(StringUtils.isNotEmpty(_widget_1648639554077.get_widget_1648639554080())?Integer.valueOf(_widget_1648639554077.get_widget_1648639554080()):0);
                    if(Objects.nonNull(map)){
                        List<JdyProjectProductInfoResponse.JdyProjectProductInfoData> jdyProjectProductInfoDataListT = map.get(jdy041Item.getItemCode());
                        if(CollectionUtil.isNotEmpty(jdyProjectProductInfoDataListT)){
                            JdyProjectProductInfoResponse.JdyProjectProductInfoData jdyProjectProductInfoData = jdyProjectProductInfoDataListT.get(0);
                            //订单数量
                            BigDecimal widget_1608271147742 = jdyProjectProductInfoData.get_widget_1608271147742();
                            //本表单货物合同价（填写）
                            BigDecimal widget_1649441076180 = jdyProjectProductInfoData.get_widget_1649441076180();
                            if(Objects.nonNull(widget_1608271147742) && Objects.nonNull(widget_1649441076180) && widget_1608271147742.compareTo(BigDecimal.ZERO) != 0){
                                jdy041Item.setPrice(widget_1649441076180.divide(widget_1608271147742,2,BigDecimal.ROUND_HALF_UP));
                            }
                        }
                    }
                    jdy041Items.add(jdy041Item);
                }

                //非标系统料号清单_3
                List<JdyProjectTaskAllocationResponse._widget_1623312109379> _widget_1623312109379List = data.get_widget_1623312109379();
                for(JdyProjectTaskAllocationResponse._widget_1623312109379 _widget_1623312109379:_widget_1623312109379List){
                    Jdy041Item jdy041Item = new Jdy041Item();
                    jdy041Item.setJdy041Id(jdy041.getId());
                    jdy041Item.setItemCode(_widget_1623312109379.get_widget_1623312109380());
                    jdy041Item.setNumber(StringUtils.isNotEmpty(_widget_1623312109379.get_widget_1623312109382())?Integer.valueOf(_widget_1623312109379.get_widget_1623312109382()):0);
                    if(Objects.nonNull(map)){
                        List<JdyProjectProductInfoResponse.JdyProjectProductInfoData> jdyProjectProductInfoDataListT = map.get(jdy041Item.getItemCode());
                        if(CollectionUtil.isNotEmpty(jdyProjectProductInfoDataListT)){
                            JdyProjectProductInfoResponse.JdyProjectProductInfoData jdyProjectProductInfoData = jdyProjectProductInfoDataListT.get(0);
                            //订单数量
                            BigDecimal widget_1608271147742 = jdyProjectProductInfoData.get_widget_1608271147742();
                            //本表单货物合同价（填写）
                            BigDecimal widget_1649441076180 = jdyProjectProductInfoData.get_widget_1649441076180();
                            if(Objects.nonNull(widget_1608271147742) && Objects.nonNull(widget_1649441076180) && widget_1608271147742.compareTo(BigDecimal.ZERO) != 0){
                                jdy041Item.setPrice(widget_1649441076180.divide(widget_1608271147742,2,BigDecimal.ROUND_HALF_UP));
                            }
                        }
                    }
                    jdy041Items.add(jdy041Item);
                }

                //散件清单_3
                List<JdyProjectTaskAllocationResponse._widget_1650782218163> _widget_1650782218163List = data.get_widget_1650782218163();
                for(JdyProjectTaskAllocationResponse._widget_1650782218163 _widget_1650782218163:_widget_1650782218163List){
                    Jdy041Item jdy041Item = new Jdy041Item();
                    jdy041Item.setJdy041Id(jdy041.getId());
                    jdy041Item.setItemCode(_widget_1650782218163.get_widget_1650782218164());
                    jdy041Item.setNumber(StringUtils.isNotEmpty(_widget_1650782218163.get_widget_1650782218168())?Integer.valueOf(_widget_1650782218163.get_widget_1650782218168()):0);
                    if(Objects.nonNull(map)){
                        List<JdyProjectProductInfoResponse.JdyProjectProductInfoData> jdyProjectProductInfoDataListT = map.get(jdy041Item.getItemCode());
                        if(CollectionUtil.isNotEmpty(jdyProjectProductInfoDataListT)){
                            JdyProjectProductInfoResponse.JdyProjectProductInfoData jdyProjectProductInfoData = jdyProjectProductInfoDataListT.get(0);
                            //订单数量
                            BigDecimal widget_1608271147742 = jdyProjectProductInfoData.get_widget_1608271147742();
                            //本表单货物合同价（填写）
                            BigDecimal widget_1649441076180 = jdyProjectProductInfoData.get_widget_1649441076180();
                            if(Objects.nonNull(widget_1608271147742) && Objects.nonNull(widget_1649441076180) && widget_1608271147742.compareTo(BigDecimal.ZERO) != 0){
                                jdy041Item.setPrice(widget_1649441076180.divide(widget_1608271147742,2,BigDecimal.ROUND_HALF_UP));
                            }
                        }
                    }
                    jdy041Items.add(jdy041Item);
                }

                //标准系统料清单_新_3
                List<JdyProjectTaskAllocationResponse._widget_1638773016043> _widget_1638773016043List = data.get_widget_1638773016043();
                for(JdyProjectTaskAllocationResponse._widget_1638773016043 _widget_1638773016043:_widget_1638773016043List){
                    Jdy041Item jdy041Item = new Jdy041Item();
                    jdy041Item.setJdy041Id(jdy041.getId());
                    jdy041Item.setItemCode(_widget_1638773016043.get_widget_1638773016044());
                    jdy041Item.setNumber(StringUtils.isNotEmpty(_widget_1638773016043.get_widget_1638773016046())?Integer.valueOf(_widget_1638773016043.get_widget_1638773016046()):0);
                    if(Objects.nonNull(map)){
                        List<JdyProjectProductInfoResponse.JdyProjectProductInfoData> jdyProjectProductInfoDataListT = map.get(jdy041Item.getItemCode());
                        if(CollectionUtil.isNotEmpty(jdyProjectProductInfoDataListT)){
                            JdyProjectProductInfoResponse.JdyProjectProductInfoData jdyProjectProductInfoData = jdyProjectProductInfoDataListT.get(0);
                            //订单数量
                            BigDecimal widget_1608271147742 = jdyProjectProductInfoData.get_widget_1608271147742();
                            //本表单货物合同价（填写）
                            BigDecimal widget_1649441076180 = jdyProjectProductInfoData.get_widget_1649441076180();
                            if(Objects.nonNull(widget_1608271147742) && Objects.nonNull(widget_1649441076180) && widget_1608271147742.compareTo(BigDecimal.ZERO) != 0){
                                jdy041Item.setPrice(widget_1649441076180.divide(widget_1608271147742,2,BigDecimal.ROUND_HALF_UP));
                            }
                        }
                    }
                    jdy041Items.add(jdy041Item);
                }

                //非标系统清单_新_3
                List<JdyProjectTaskAllocationResponse._widget_1638773016102> _widget_1638773016102List = data.get_widget_1638773016102();
                for(JdyProjectTaskAllocationResponse._widget_1638773016102 _widget_1638773016102:_widget_1638773016102List){
                    Jdy041Item jdy041Item = new Jdy041Item();
                    jdy041Item.setJdy041Id(jdy041.getId());
                    jdy041Item.setItemCode(_widget_1638773016102.get_widget_1638773016103());
                    jdy041Item.setNumber(StringUtils.isNotEmpty(_widget_1638773016102.get_widget_1638773016106())?Integer.valueOf(_widget_1638773016102.get_widget_1638773016106()):0);
                    if(Objects.nonNull(map)){
                        List<JdyProjectProductInfoResponse.JdyProjectProductInfoData> jdyProjectProductInfoDataListT = map.get(jdy041Item.getItemCode());
                        if(CollectionUtil.isNotEmpty(jdyProjectProductInfoDataListT)){
                            JdyProjectProductInfoResponse.JdyProjectProductInfoData jdyProjectProductInfoData = jdyProjectProductInfoDataListT.get(0);
                            //订单数量
                            BigDecimal widget_1608271147742 = jdyProjectProductInfoData.get_widget_1608271147742();
                            //本表单货物合同价（填写）
                            BigDecimal widget_1649441076180 = jdyProjectProductInfoData.get_widget_1649441076180();
                            if(Objects.nonNull(widget_1608271147742) && Objects.nonNull(widget_1649441076180) && widget_1608271147742.compareTo(BigDecimal.ZERO) != 0){
                                jdy041Item.setPrice(widget_1649441076180.divide(widget_1608271147742,2,BigDecimal.ROUND_HALF_UP));
                            }
                        }
                    }
                    jdy041Items.add(jdy041Item);
                }

                //标准系统料号清单_4
                List<JdyProjectTaskAllocationResponse._widget_1650004173467> _widget_1650004173467List = data.get_widget_1650004173467();
                for(JdyProjectTaskAllocationResponse._widget_1650004173467 _widget_1650004173467:_widget_1650004173467List){
                    Jdy041Item jdy041Item = new Jdy041Item();
                    jdy041Item.setJdy041Id(jdy041.getId());
                    jdy041Item.setItemCode(_widget_1650004173467.get_widget_1650004173468());
                    jdy041Item.setNumber(StringUtils.isNotEmpty(_widget_1650004173467.get_widget_1650004173470())?Integer.valueOf(_widget_1650004173467.get_widget_1650004173470()):0);
                    if(Objects.nonNull(map)){
                        List<JdyProjectProductInfoResponse.JdyProjectProductInfoData> jdyProjectProductInfoDataListT = map.get(jdy041Item.getItemCode());
                        if(CollectionUtil.isNotEmpty(jdyProjectProductInfoDataListT)){
                            JdyProjectProductInfoResponse.JdyProjectProductInfoData jdyProjectProductInfoData = jdyProjectProductInfoDataListT.get(0);
                            //订单数量
                            BigDecimal widget_1608271147742 = jdyProjectProductInfoData.get_widget_1608271147742();
                            //本表单货物合同价（填写）
                            BigDecimal widget_1649441076180 = jdyProjectProductInfoData.get_widget_1649441076180();
                            if(Objects.nonNull(widget_1608271147742) && Objects.nonNull(widget_1649441076180) && widget_1608271147742.compareTo(BigDecimal.ZERO) != 0){
                                jdy041Item.setPrice(widget_1649441076180.divide(widget_1608271147742,2,BigDecimal.ROUND_HALF_UP));
                            }
                        }
                    }
                    jdy041Items.add(jdy041Item);
                }

                //非标系统料号清单_4
                List<JdyProjectTaskAllocationResponse._widget_1650004173619> _widget_1650004173619List = data.get_widget_1650004173619();
                for(JdyProjectTaskAllocationResponse._widget_1650004173619 _widget_1650004173619:_widget_1650004173619List){
                    Jdy041Item jdy041Item = new Jdy041Item();
                    jdy041Item.setJdy041Id(jdy041.getId());
                    jdy041Item.setItemCode(_widget_1650004173619.get_widget_1650004173620());
                    jdy041Item.setNumber(StringUtils.isNotEmpty(_widget_1650004173619.get_widget_1650004173623())?Integer.valueOf(_widget_1650004173619.get_widget_1650004173623()):0);
                    if(Objects.nonNull(map)){
                        List<JdyProjectProductInfoResponse.JdyProjectProductInfoData> jdyProjectProductInfoDataListT = map.get(jdy041Item.getItemCode());
                        if(CollectionUtil.isNotEmpty(jdyProjectProductInfoDataListT)){
                            JdyProjectProductInfoResponse.JdyProjectProductInfoData jdyProjectProductInfoData = jdyProjectProductInfoDataListT.get(0);
                            //订单数量
                            BigDecimal widget_1608271147742 = jdyProjectProductInfoData.get_widget_1608271147742();
                            //本表单货物合同价（填写）
                            BigDecimal widget_1649441076180 = jdyProjectProductInfoData.get_widget_1649441076180();
                            if(Objects.nonNull(widget_1608271147742) && Objects.nonNull(widget_1649441076180) && widget_1608271147742.compareTo(BigDecimal.ZERO) != 0){
                                jdy041Item.setPrice(widget_1649441076180.divide(widget_1608271147742,2,BigDecimal.ROUND_HALF_UP));
                            }
                        }
                    }
                    jdy041Items.add(jdy041Item);
                }

                //散件清单_4
                List<JdyProjectTaskAllocationResponse._widget_1650782218262> _widget_1650782218262List = data.get_widget_1650782218262();
                for(JdyProjectTaskAllocationResponse._widget_1650782218262 _widget_1650782218262:_widget_1650782218262List){
                    Jdy041Item jdy041Item = new Jdy041Item();
                    jdy041Item.setJdy041Id(jdy041.getId());
                    jdy041Item.setItemCode(_widget_1650782218262.get_widget_1650782218263());
                    jdy041Item.setNumber(StringUtils.isNotEmpty(_widget_1650782218262.get_widget_1650782218267())?Integer.valueOf(_widget_1650782218262.get_widget_1650782218267()):0);
                    if(Objects.nonNull(map)){
                        List<JdyProjectProductInfoResponse.JdyProjectProductInfoData> jdyProjectProductInfoDataListT = map.get(jdy041Item.getItemCode());
                        if(CollectionUtil.isNotEmpty(jdyProjectProductInfoDataListT)){
                            JdyProjectProductInfoResponse.JdyProjectProductInfoData jdyProjectProductInfoData = jdyProjectProductInfoDataListT.get(0);
                            //订单数量
                            BigDecimal widget_1608271147742 = jdyProjectProductInfoData.get_widget_1608271147742();
                            //本表单货物合同价（填写）
                            BigDecimal widget_1649441076180 = jdyProjectProductInfoData.get_widget_1649441076180();
                            if(Objects.nonNull(widget_1608271147742) && Objects.nonNull(widget_1649441076180) && widget_1608271147742.compareTo(BigDecimal.ZERO) != 0){
                                jdy041Item.setPrice(widget_1649441076180.divide(widget_1608271147742,2,BigDecimal.ROUND_HALF_UP));
                            }
                        }
                    }
                    jdy041Items.add(jdy041Item);
                }

                //标准系统料清单_新_4
                List<JdyProjectTaskAllocationResponse._widget_1650004173857> _widget_1650004173857List = data.get_widget_1650004173857();
                for(JdyProjectTaskAllocationResponse._widget_1650004173857 _widget_1650004173857:_widget_1650004173857List){
                    Jdy041Item jdy041Item = new Jdy041Item();
                    jdy041Item.setJdy041Id(jdy041.getId());
                    jdy041Item.setItemCode(_widget_1650004173857.get_widget_1650004173858());
                    jdy041Item.setNumber(StringUtils.isNotEmpty(_widget_1650004173857.get_widget_1650004173860())?Integer.valueOf(_widget_1650004173857.get_widget_1650004173860()):0);
                    if(Objects.nonNull(map)){
                        List<JdyProjectProductInfoResponse.JdyProjectProductInfoData> jdyProjectProductInfoDataListT = map.get(jdy041Item.getItemCode());
                        if(CollectionUtil.isNotEmpty(jdyProjectProductInfoDataListT)){
                            JdyProjectProductInfoResponse.JdyProjectProductInfoData jdyProjectProductInfoData = jdyProjectProductInfoDataListT.get(0);
                            //订单数量
                            BigDecimal widget_1608271147742 = jdyProjectProductInfoData.get_widget_1608271147742();
                            //本表单货物合同价（填写）
                            BigDecimal widget_1649441076180 = jdyProjectProductInfoData.get_widget_1649441076180();
                            if(Objects.nonNull(widget_1608271147742) && Objects.nonNull(widget_1649441076180) && widget_1608271147742.compareTo(BigDecimal.ZERO) != 0){
                                jdy041Item.setPrice(widget_1649441076180.divide(widget_1608271147742,2,BigDecimal.ROUND_HALF_UP));
                            }
                        }
                    }
                    jdy041Items.add(jdy041Item);
                }

                //非标系统清单_新_4
                List<JdyProjectTaskAllocationResponse._widget_1650004173908> _widget_1650004173908List = data.get_widget_1650004173908();
                for(JdyProjectTaskAllocationResponse._widget_1650004173908 _widget_1650004173908:_widget_1650004173908List){
                    Jdy041Item jdy041Item = new Jdy041Item();
                    jdy041Item.setJdy041Id(jdy041.getId());
                    jdy041Item.setItemCode(_widget_1650004173908.get_widget_1650004173909());
                    jdy041Item.setNumber(StringUtils.isNotEmpty(_widget_1650004173908.get_widget_1650004173912())?Integer.valueOf(_widget_1650004173908.get_widget_1650004173912()):0);
                    if(Objects.nonNull(map)){
                        List<JdyProjectProductInfoResponse.JdyProjectProductInfoData> jdyProjectProductInfoDataListT = map.get(jdy041Item.getItemCode());
                        if(CollectionUtil.isNotEmpty(jdyProjectProductInfoDataListT)){
                            JdyProjectProductInfoResponse.JdyProjectProductInfoData jdyProjectProductInfoData = jdyProjectProductInfoDataListT.get(0);
                            //订单数量
                            BigDecimal widget_1608271147742 = jdyProjectProductInfoData.get_widget_1608271147742();
                            //本表单货物合同价（填写）
                            BigDecimal widget_1649441076180 = jdyProjectProductInfoData.get_widget_1649441076180();
                            if(Objects.nonNull(widget_1608271147742) && Objects.nonNull(widget_1649441076180) && widget_1608271147742.compareTo(BigDecimal.ZERO) != 0){
                                jdy041Item.setPrice(widget_1649441076180.divide(widget_1608271147742,2,BigDecimal.ROUND_HALF_UP));
                            }
                        }
                    }
                    jdy041Items.add(jdy041Item);
                }


                if(CollectionUtil.isNotEmpty(jdy041Items)) {
                    jdy041ItemMapper.insertBatchSomeColumn(jdy041Items);
                }

                List<Jdy041Url> jdy041UrlListT = jdy041UrlMapper.selectList(Wrappers.<Jdy041Url>lambdaQuery().eq(Jdy041Url::getJdy04Id, jdy041.getId()));
                if(CollectionUtil.isEmpty(jdy041UrlListT)) {
                    //商务审批
                    JdyBusinessCheckRequest jdyBusinessCheckRequest = new JdyBusinessCheckRequest();

                    JdyBusinessCheckRequest.Field fieldCheck = new JdyBusinessCheckRequest.Field();
                    fieldCheck.setField("_widget_1638846089425");
                    fieldCheck.setMethod(JdyConstants.JDY_EQ);
                    fieldCheck.setValue(Arrays.asList(sjCode));
                    JdyBusinessCheckRequest.Filter filter = new JdyBusinessCheckRequest.Filter();
                    filter.setCond(Arrays.asList(fieldCheck));
                    filter.setRel(JdyConstants.AND);
                    jdyBusinessCheckRequest.setFilter(filter);
                    jdyBusinessCheckRequest.setApp_id("6166529614a97a0008504397");
                    jdyBusinessCheckRequest.setEntry_id("61aece8a5fe8e400071dc300");
                    jdyBusinessCheckRequest.setUrlName(JdyConstants.MORE_QUERY_URL);
                    jdyBusinessCheckRequest.setLimit(100);
                    JdyBusinessCheckResponse jdyBusinessCheckResponse = jdyClient.jdyCallV5(jdyBusinessCheckRequest);

                    if (Objects.nonNull(jdyBusinessCheckResponse) && CollectionUtil.isNotEmpty(jdyBusinessCheckResponse.getData())) {
                        List<Jdy041Url> jdy041UrlList = new ArrayList<>();
                        for (JdyBusinessCheckResponse.JdyBusinessCheckData jdyBusinessCheckData : jdyBusinessCheckResponse.getData()) {

                            //双章合同
                            List<JdyBusinessCheckResponse._widget_1639206267386> widget_1639206267386List = jdyBusinessCheckData.get_widget_1639206267386();
                            for (JdyBusinessCheckResponse._widget_1639206267386 jdyUrl : widget_1639206267386List) {
                                Jdy041Url jdy041Url = new Jdy041Url();
                                jdy041Url.setJdy04Id(jdy041.getId());
                                jdy041Url.setType(1);
                                String fileType = jdyUrl.getName().substring(jdyUrl.getName().lastIndexOf(".")+1);
                                R<SysFile> fileResult = remoteFileService.upload(ValueUtil.getFile(jdyUrl.getUrl(),fileType));
                                if(Objects.nonNull(fileResult) && Objects.nonNull(fileResult.getData())) {
                                    jdy041Url.setUrl(fileResult.getData().getUrl());
                                }
                                jdy041UrlList.add(jdy041Url);
                            }
                            //技术协议
                            List<JdyBusinessCheckResponse._widget_1668482787401> widget_1668482787401List = jdyBusinessCheckData.get_widget_1668482787401();
                            for (JdyBusinessCheckResponse._widget_1668482787401 jdyUrl : widget_1668482787401List) {
                                Jdy041Url jdy041Url = new Jdy041Url();
                                jdy041Url.setJdy04Id(jdy041.getId());
                                jdy041Url.setType(2);
                                String fileType = jdyUrl.getName().substring(jdyUrl.getName().lastIndexOf(".")+1);
                                R<SysFile> fileResult = remoteFileService.upload(ValueUtil.getFile(jdyUrl.getUrl(),fileType));
                                if(Objects.nonNull(fileResult) && Objects.nonNull(fileResult.getData())) {
                                    jdy041Url.setUrl(fileResult.getData().getUrl());
                                }
                                jdy041UrlList.add(jdy041Url);

                            }
                        }

                        if (CollectionUtil.isNotEmpty(jdy041UrlList)) {
                            jdy041UrlMapper.insertBatchSomeColumn(jdy041UrlList);
                        }
                    }
                }
            }

        }

        /*if(CollectionUtil.isNotEmpty(dataList) && dataList.size() == limit){
            String id = dataList.get(dataList.size() - 1).get_id();
            dataList.clear();
            synJdy04T1(id);
        }*/
    }
}
