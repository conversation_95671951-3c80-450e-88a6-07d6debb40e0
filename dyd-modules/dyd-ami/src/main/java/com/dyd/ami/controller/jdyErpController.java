package com.dyd.ami.controller;

import com.alibaba.fastjson.JSON;
import com.dyd.ami.aop.JobAnnotation;
import com.dyd.ami.domain.*;
import com.dyd.ami.domain.jdy.request.JdyFinancialReceiptsRequest;
import com.dyd.ami.domain.u9.CreateU9ProjectOrderRequest;
import com.dyd.ami.mapper.JobFailureLogMapper;
import com.dyd.ami.service.*;
import com.dyd.ami.service.impl.JdyErpService;
import com.dyd.common.core.domain.R;
import com.dyd.common.core.utils.StringUtils;
import com.dyd.common.core.web.domain.AjaxResult;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;

/**
 * 简道云表单操作
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/erpFrom")
public class jdyErpController {

    @Resource
    private IJdyFromService jdyFromService;

    @Resource
    private IJdyErpService iJdyErpService;

    @Resource
    private IJdyEventsService jdyEventsService;


    @Autowired
    private StorageFreeInfoService storageFreeInfoService;

    @Autowired
    private JdyErpService jdyErpService;

    @Autowired
    private JdySynService jdySynService;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private JobFailureLogMapper jobFailureLogMapper;

    /**
     * 查询所有订单类型
     */
    @PostMapping(value = "/getOrderType")
    public AjaxResult getOrderType() {
        iJdyErpService.getJdyOrderTypes();
        return AjaxResult.success();
    }

    /**
     * 查询u9code
     */
    @PostMapping(value = "/getCunstomerByCode")
    public AjaxResult getCunstomerByCode(String code) {
        code = "JCGYDJSJY";
        iJdyErpService.getCunstomerByCode(code);
        return AjaxResult.success();
    }


    /**
     * 保存crm中客户信息到u9
     */
    @PostMapping(value = "/dealCust")
    public AjaxResult dealCust() {

        jdyEventsService.dealCust();
        return AjaxResult.success();
    }


    /**
     *简道云04-4表单进度管理字段更新
     * @param prCode
     * @return
     */
    @GetMapping("/syncFourProject")
    public R syncFourProject(@RequestParam("prCode") String prCode,@RequestParam(value = "freezeStatus",required = false) String freezeStatus){
        log.info("04-4进度管理更新请求字段{}",prCode);
        if(StringUtils.isNotEmpty(freezeStatus) && "已冻结".equals(freezeStatus)){
            return R.ok();
        }
        SyncFourProjectResponse syncFourProjectResponse = new SyncFourProjectResponse();
        try {
            syncFourProjectResponse = storageFreeInfoService.syncFourProjectNew(prCode,"");
        }catch (Exception e){
            log.error("4进度管理更新请求错误{}",e);
        }
        return R.ok(syncFourProjectResponse);
    }

    /**
     * 校验和同步u9hs编码
     * @param request
     * @return
     */
    @PostMapping("/getSHCode")
    public R getSHCode(@RequestBody JdyHsCodeRequest request){

        log.info("校验和同步u9hs编码请求参数{}", JSON.toJSONString(request));

        jdyErpService.getSHCode(request);
        return R.ok();
    }

    /**
     * 财务收款分配2.1版数据推送u9收款单
     * @return
     */
    @PostMapping("/synJdyFinancialReceipts")
    public R synJdyFinancialReceipts(@RequestBody JdyFinancialReceiptsRequest request){
        log.info("财务收款分配2.1版数据推送u9收款单请求参数{}",JSON.toJSONString(request));
        jdyErpService.synJdyFinancialReceipts(request);
        return R.ok();
    }


    /**
     * u9同步简道云贸易类订单清单，贸易类订单明细
     * @return
     */
    @JobAnnotation
    @GetMapping("/createTradeOrder")
    public R createTradeOrder(@RequestParam(value = "docNo",required = false) String  docNo){
        jdySynService.createTradeOrder(docNo);
        return R.ok();
    }

    /**
     * 岱鼎云—CRM—基础数据—备件服务——报价商务审批  备件u9z自动建单
     *
     * @return
     */
    @PostMapping("/createU9SoOrder")
    public CreateU9SoResult createU9SoOrder(@RequestBody CreateU9SoRequest request){
        CreateU9SoResult createU9SoResult = new CreateU9SoResult();

        RLock lock = redissonClient.getLock("createU9SoOrder:" + request.getBusinessNo());


        try{

            if (!lock.tryLock(1, TimeUnit.SECONDS)) {
                return createU9SoResult;
            }

            return jdyErpService.createU9SoOrder(request.getBusinessNo());
        }catch (Exception e){
            log.error("失败{}",e);
        }finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        return createU9SoResult;
    }

    /**
     *04-1项目建单
     * @return
     */
    @PostMapping("/createU9ProjectOrder")
    public CreateU9SoResult  createU9ProjectOrder(@RequestBody CreateU9ProjectOrderRequest request){

        log.info("04-1项目建单请求参数{}",JSON.toJSONString(request));
        CreateU9SoResult createU9SoResult = new CreateU9SoResult();
        RLock lock = redissonClient.getLock("createU9ProjectOrder:");
        try{
            if(!lock.tryLock()){
                return createU9SoResult;
            }
            return jdyErpService.createU9ProjectOrder(request);
        }catch (Exception e){
            log.info("04-1项目建单失败{}",e);
        }finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        return createU9SoResult;
    }


}
