package com.dyd.notice.controller.fsm;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dyd.common.core.web.controller.BaseController;
import com.dyd.common.core.web.domain.AjaxResult;
import com.dyd.common.core.web.page.TableDataInfo;
import com.dyd.common.security.utils.SecurityUtils;
import com.dyd.notice.domain.fsm.*;
import com.dyd.notice.service.FSMService;
import com.dyd.notice.service.FiletagService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @description TODO
 * @author: wx
 * @date: 2023/5/17 10:52
 */
@Slf4j
@RestController
@RequestMapping(value = "/fsm")
public class FsmController extends BaseController {
    @Autowired
    private FSMService fsmService;
    @Autowired
    private FiletagService filetagService;

    @GetMapping(value = "/tagList")
    public AjaxResult tagList(FileTag fileTag) {
        try {
            if (fileTag.isNeedPage()) {
                startPage();
            }
            QueryWrapper<FileTag> queryWrapper = new QueryWrapper<>(fileTag);
            return AjaxResult.success(filetagService.list(queryWrapper));
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    @PostMapping("/saveTag")
    public AjaxResult saveTag(@RequestBody FileTag fileTag) {
        try {
            filetagService.save(fileTag);
            return AjaxResult.success();
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    @PostMapping("/updateTag")
    public AjaxResult updateTag(@RequestBody FileTag fileTag) {
        try {
            filetagService.updateById(fileTag);
            return AjaxResult.success();
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    @PostMapping("/deleteTag")
    public AjaxResult deleteTag(@RequestBody FileTag fileTag) {
        try {
            FsmFile file = new FsmFile();
            file.setFileTags(fileTag.getId().toString());
            List<FsmFile> fsmFiles = fsmService.getFsmFiles(file, null, null);
            if (!fsmFiles.isEmpty()) {
                throw new Exception("改标签绑定了文件，请解除绑定。");
            }
            filetagService.removeById(fileTag.getId());
            return AjaxResult.success();
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    @GetMapping(value = "/folderList")
    public AjaxResult folderList() {
        try {
            String jobNum = SecurityUtils.getUsername();
            return AjaxResult.success(fsmService.getFsmFolderList(jobNum));
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 文件列表
     * @param fsmFile
     * @param pageNum
     * @param pageSize
     * @param startDate
     * @param endDate
     * @return
     */
    @GetMapping("/fileList")
    public TableDataInfo fileList(FsmFile fsmFile, Integer pageNum, Integer pageSize, String startDate, String endDate) {
        startPage();
        fsmFile.setStartDate(startDate);
        fsmFile.setEndDate(endDate);
        List<FsmFile> list = fsmService.getFsmFiles(fsmFile, pageNum, pageSize);
        return getDataTable(list);
    }

    @GetMapping("/fileLogList")
    public TableDataInfo fileLogList(Long fileId) {
        startPage();
        List<FsmFileLog> logs = fsmService.getFsmFileLogs(fileId);
        return getDataTable(logs);
    }

    @PostMapping("/saveFile")
    public AjaxResult saveFile(@RequestBody FsmFile fsmFile) {
        try {
            fsmService.saveFsmFile(fsmFile);
            return AjaxResult.success();
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    @PostMapping("/saveFileBatch")
    public AjaxResult saveFileBatch(@RequestBody FsmFileBatch fsmFile) {
        try {
            fsmService.saveFsmFileList(fsmFile.getFsmFiles());
            return AjaxResult.success();
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 更新
     * @param fsmFile
     * @return
     */
    @PostMapping("/updateFile")
    public AjaxResult updateFile(@RequestBody FsmFile fsmFile) {
        try {
            fsmService.updateFsmFile(fsmFile);
            return AjaxResult.success();
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    @PostMapping("/deleteFile")
    public AjaxResult deleteFile(@RequestBody FsmFile fsmFile) {
        try {
            fsmService.deleteFsmFile(fsmFile.getId());
            return AjaxResult.success();
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    //------------------------------------------------

    @GetMapping("/admin/userFolderRoleList")
    public TableDataInfo userFolderRole(FsmFolderUser fsmFolderUser) {
        if (fsmFolderUser.isNeedPage()) {
            startPage();
        }
        List<FsmFolderUser> list = fsmService.getFsmFolderUserList(fsmFolderUser);
        return getDataTable(list);
    }

    @GetMapping("/admin/roleTmpList")
    public TableDataInfo getRoleList(UserRole role) {
        List<UserRole> list = fsmService.getRoleList(role);
        return getDataTable(list);
    }


    @PostMapping(value = "/admin/allocationRole")
    public AjaxResult allocationRole(@RequestBody FsmAllocationRole fsmAllocationRole) {
        try {
            fsmService.allocationRole(fsmAllocationRole);
            return AjaxResult.success("后台正在处理，稍后刷新页面");
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    @PostMapping(value = "/admin/cancelRole")
    public AjaxResult cancelRole(@RequestBody FsmAllocationRole fsmAllocationRole) {
        try {
            fsmService.cancelRole(fsmAllocationRole);
            return AjaxResult.success();
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    @PostMapping(value = "/admin/deleteFolder")
    public AjaxResult deleteFolder(@RequestBody FsmFolder folder) {
        try {
            fsmService.deleteFolder(folder.getId());
            return AjaxResult.success();
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    @PostMapping(value = "/admin/updateFolder")
    public AjaxResult updateFolder(@RequestBody FsmFolder folder) {
        try {
            fsmService.updateFolder(folder);
            return AjaxResult.success();
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    @PostMapping(value = "/admin/saveFolder")
    public AjaxResult saveFolder(@RequestBody FsmFolder folder) {
        try {
            fsmService.saveFolder(folder);
            return AjaxResult.success();
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    @GetMapping(value = "/admin/getUserTree")
    public AjaxResult getUserTree(FsmFolderUser fsmFolderUser) {
        try {
            return AjaxResult.success(fsmService.getAllDdUserDeptTree(fsmFolderUser.getFolderId()));
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    @GetMapping(value = "/admin/folderList")
    public AjaxResult adminFolderList() {
        try {
            return AjaxResult.success(fsmService.getFsmFolderList(""));
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    @PostMapping(value = "/admin/saveRole")
    public AjaxResult saveFsmRole(@RequestBody FsmRole fsmRole) {
        try {
            fsmService.saveFsmRole(fsmRole);
            return AjaxResult.success();
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    @PostMapping(value = "/admin/updateRole")
    public AjaxResult updateRole(@RequestBody FsmRole fsmRole) {
        try {
            fsmService.saveFsmRole(fsmRole);
            return AjaxResult.success("后台正在处理，稍后刷新一下页面");
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    @PostMapping(value = "/admin/deleteRole")
    public AjaxResult deleteRole(@RequestBody FsmRole fsmRole) {
        try {
            fsmService.deleteFsmRole(fsmRole);
            return AjaxResult.success();
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    @GetMapping("/admin/roleList")
    public TableDataInfo list(FsmFolderUserTmp folderUserTmp) {
        startPage();
        List<FsmRole> list = fsmService.getFsmRoleList(folderUserTmp);
        return getDataTable(list);
    }
}
