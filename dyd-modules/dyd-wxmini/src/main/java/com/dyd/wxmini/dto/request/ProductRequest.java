package com.dyd.wxmini.dto.request;

import java.io.Serializable;
import lombok.Data;

@Data
public class ProductRequest implements Serializable {

    private static final long serialVersionUID = -2560943574237345634L;

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 每页数量
     */
    private Integer pageSize;

    /**
     * 排序
     */
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    private String isAsc;

    /**
     * 分页参数合理化
     */
    private String reasonable;

    /**
     * 分类
     */
    private String classification;

    /**
     * 类型
     */
    private String productClass;

    /**
     * 物料名称
     */
    private String materielName;

}
