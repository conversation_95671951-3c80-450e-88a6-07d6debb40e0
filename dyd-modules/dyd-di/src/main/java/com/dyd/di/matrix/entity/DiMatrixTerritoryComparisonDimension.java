package com.dyd.di.matrix.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dyd.common.security.entity.BaseEntity;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 业务版图对比维度
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
@Getter
@Setter
@TableName("di_matrix_territory_comparison_dimension")
@ApiModel(value = "DiMatrixTerritoryComparisonDimension对象", description = "业务版图对比维度")
public class DiMatrixTerritoryComparisonDimension extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("分组ID")
    @TableField("group_id")
    private Long groupId;

    @ApiModelProperty("对比维度名称")
    @TableField("dimension_name")
    private String dimensionName;

    @ApiModelProperty("排序权重")
    @TableField("weight")
    private Long weight;

    @ApiModelProperty("2删除 0 正常")
    @TableField("del_flag")
    @TableLogic
    private Integer delFlag;
}
