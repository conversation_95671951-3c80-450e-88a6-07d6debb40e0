package com.dyd.di.comment.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dyd.di.comment.entity.DiComment;
import com.dyd.di.comment.vo.CommentQueryVO;
import com.dyd.di.comment.vo.CommentRespVO;
import com.dyd.di.comment.vo.CommentStageVO;

/**
 * 星记 Service 接口
 */
public interface DiCommentService extends IService<DiComment> {
    CommentStageVO getStage(String projectNo, String nicheNo, String clueNo);
    void tryUpdate(String projectNo, String nicheNo);
    CommentQueryVO getCommentQuery(String projectNo, String nicheNo, String clueNo);
    void createStage(CommentRespVO createReqVO);
}