package com.dyd.di.interceptor.diMarketingNiche;

import org.springframework.context.ApplicationEvent;

public class DiMarketingNicheStatusEvent extends ApplicationEvent {

    private String id;
    private String nicheNo;

    public DiMarketingNicheStatusEvent(Object source, String id, String nicheNo) {
        super(source);
        this.id = id;
        this.nicheNo = nicheNo;
    }

    public String getId() {
        return id;
    }

    public String getNicheNo() {
        return nicheNo;
    }
}
