package com.dyd.di.marketing.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.dyd.common.core.annotation.Excel;
import com.dyd.common.security.entity.BaseEntity;
import com.dyd.di.oss.FileVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 线索对象 di_marketing_clue
 *
 * <AUTHOR>
 * @date 2024-06-19
 */
 @Data
public class DiMarketingClue extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private String id;

    /**
     * 线索号
     */
    @Excel(name = "线索号")
    private String clueNo;

    /**
     * 客户号
     */
    @Excel(name = "客户号")
    private String customerNo;

    /**
     * 是否建立微信群（num_yes_no）
     */
    @Excel(name = "是否建立微信群", readConverterExp = "n=um_yes_no")
    private String wechatGriyos;

    /**
     * 线索是否形成闭环（num_yes_no）
     */
    @Excel(name = "线索是否形成闭环", readConverterExp = "n=um_yes_no")
    private String closedLoop;

    /**
     * 需求类型（clue_demand_type）
     */
    @Excel(name = "需求类型", readConverterExp = "c=lue_demand_type")
    private String demandType;

    /**
     * 线索来源（clue_source）
     */
    @Excel(name = "线索来源", readConverterExp = "c=lue_source")
    private String clueSource;

    /**
     * 其他来源
     */
    @Excel(name = "其他来源")
    private String otherSource;

    /**
     * 线索状态（clue_status）
     */
    @Excel(name = "线索状态", readConverterExp = "c=lue_status")
    private String clueStatus;

    /**
     * 关键词
     */
    @Excel(name = "关键词")
    private String keyword;

    /**
     * 搜索词
     */
    @Excel(name = "搜索词")
    private String searchTerms;

    /**
     * 是否转客户（num_yes_no）
     */
    @Excel(name = "是否转客户", readConverterExp = "n=um_yes_no")
    private String transferringCustomers;

    /**
     * 是否转商机（num_yes_no）
     */
    @Excel(name = "是否转商机", readConverterExp = "n=um_yes_no")
    private String transferringNiche;

    /**
     * 行业大类
     */
    @Excel(name = "行业大类")
    private String industryCategories;

    /**
     * 应用大类
     */
    @Excel(name = "应用大类")
    private String applicationCategories;

    /**
     * 应用小类
     */
    @Excel(name = "应用小类")
    private String applicationSubcategories;

    /**
     * 四级分类
     */
    @Excel(name = "四级分类")
    private String fourLevelClassification;

    /**
     * 线索备注
     */
    @Excel(name = "线索备注")
    private String remarks;

    /**
     * 归属销售
     */
    private String clueOwner;

    /**
     * 归属销售名称(用于详情回显)
     */
    @TableField(exist = false)
    private String clueOwnerName;

    /**
     * 所属销售部门
     */
    private String clueOwnerDept;

    /**
     * 所属销售部门名称(用于详情回显)
     */
    @TableField(exist = false)
    private String clueOwnerDeptName;

    /**
     * 2删除 0 正常
     */
    private String delFlag;

    /**
     * 链路id
     */
    @Excel(name = "链路id")
    private String traceId;

    /**
     * 线索附件
     */
    @Excel(name = "线索附件")
    private String clueFile;

    /**
     * 客户名称
     */
    @TableField(exist = false)
    private String companyName;

    @TableField(exist = false)
    private DiMarketingCustomer diMarketingCustomer;

    /**
     * 联系人信息
     */
    @TableField(exist = false)
    private List<DiMarketingContacts> diMarketingContactsList;

    /**
     * 文件列表
     */
    @TableField(exist = false)
    private List<FileVo> fileList;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 共享销售
     */
    private String sharedBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 查询关键词
     */
    @TableField(exist = false)
    private String searchValue;

    /**
     * 无效理由
     */
    private String invalidReason;

    /**
     * 线下展会分类(exhibition_classification)
     */
    private String exhibitionClassification;

    @TableField(exist = false)
    /** 请求参数 */
    private Map<String, Object> params;

    public Map<String, Object> getParams() {
        if (params == null) {
            params = new HashMap<>();
        }
        return params;
    }
}
