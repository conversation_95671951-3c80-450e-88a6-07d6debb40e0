package com.dyd.di.marketing.domain;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 营销文件
 * @TableName di_marketing_file
 */
@TableName(value ="di_marketing_file")
@Data
public class DiMarketingFile implements Serializable {
    /**
     *
     */
    @TableId
    private Long id;

    /**
     * 关联ID
     */
    private Long belongingId;

    /**
     * 业务编号
     */
    private String belongingNo;

    /**
     * 所属业务：商机（0）
     */
    private String belonging;

    /**
     * 文件key
     */
    private String fileKey;

    /**
     * 2删除 0 正常
     */
    private Integer delFlag;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 链路id
     */
    private String traceId;

    private Long commentId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
