package com.dyd.di.matrix.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dyd.common.security.entity.BaseEntity;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 业务版图对比分组
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
@Getter
@Setter
@TableName("di_matrix_territory_comparison_group")
@ApiModel(value = "DiMatrixTerritoryComparisonGroup对象", description = "业务版图对比分组")
public class DiMatrixTerritoryComparisonGroup extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("版图Id")
    @TableField("territory_id")
    private Long territoryId;

    @ApiModelProperty("分组名称")
    @TableField("group_name")
    private String groupName;

    @ApiModelProperty("排序权重")
    @TableField("weight")
    private Long weight;

    @ApiModelProperty("2删除 0 正常")
    @TableField("del_flag")
    @TableLogic
    private Integer delFlag;
}
