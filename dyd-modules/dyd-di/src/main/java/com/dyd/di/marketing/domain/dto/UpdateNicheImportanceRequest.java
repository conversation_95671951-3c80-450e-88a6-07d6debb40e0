package com.dyd.di.marketing.domain.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 更新商机询价定性 和 定性原因
 */
@Data
public class UpdateNicheImportanceRequest {

    /**
     * 商机id
     */
    @NotNull(message = "id不能为空")
    private Long id;

    /**
     * 询价定性
     */
    @NotNull(message = "询价定性不能为空")
    private Integer importance;

    /**
     * A+商机判断原因
     */
    private String determineCause;
}
