package com.dyd.di.marketing.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dyd.common.core.domain.R;
import com.dyd.common.core.enums.OssSysCodeEnum;
import com.dyd.common.core.utils.DateUtils;
import com.dyd.common.core.utils.StringUtils;
import com.dyd.common.security.utils.SecurityUtils;
import com.dyd.dbc.api.RemoteDbcService;
import com.dyd.dbc.api.model.DdUserDTO;
import com.dyd.dbc.api.model.QueryDdUserDTO;
import com.dyd.di.marketing.domain.DiMarketingFile;
import com.dyd.di.marketing.service.DiMarketingFileService;
import com.dyd.di.matrix.domain.DiMarketingFileVo;
import com.dyd.di.oss.FileAndBusinessVo;
import com.dyd.di.oss.FileVo;
import com.dyd.di.oss.OssService;
import com.dydtec.base.oss.api.dto.response.OssPreviewDTO;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 营销附件
 *
 * <AUTHOR>
 * @date 2025-04-10
 */
@Slf4j
@RestController
@RequestMapping("/marketingFile")
public class DiMarketingFileController {

    @Autowired
    private DiMarketingFileService diMarketingFileService;

    @Autowired
    private OssService ossService;

    @Autowired
    private RemoteDbcService remoteDbcService;

    /**
     * 文件列表
     *
     * @return 实体
     */
    @PostMapping(value = "/fileList")
    public R<List<FileAndBusinessVo>> fileList(@RequestBody DiMarketingFileVo vo) {
        if (StringUtils.isBlank(vo.getBelonging())) {
            return R.fail("所属业务不能为空!");
        }
        if (null == vo.getBelongingId()) {
            return R.fail("关联ID不能为空!");
        }
        List<FileAndBusinessVo> fileVoList = new ArrayList<>();
        QueryWrapper<DiMarketingFile> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(vo.getBelongingId()) ,"belonging_id", vo.getBelongingId());
        queryWrapper.eq(Objects.nonNull(vo.getBelongingNo()) ,"belonging_no", vo.getBelongingNo());
        queryWrapper.eq("belonging", vo.getBelonging());
        queryWrapper.eq("del_flag", "0");
        queryWrapper.orderByDesc("create_time");
        List<DiMarketingFile> files = diMarketingFileService.list(queryWrapper);
        Map<String, DiMarketingFile> fileKeyMap = Maps.newHashMap();
        fileKeyMap = files.stream()
                .collect(Collectors.groupingBy(DiMarketingFile::getFileKey))
                .entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue().get(0)
                ));

        Map<String, String> userNameMap = Maps.newHashMap();
        List<String> list =  files.stream()
                .map(DiMarketingFile::getCreateBy)
                .collect(Collectors.toList());
        R<List<DdUserDTO>> userListResult = remoteDbcService.queryDdUserList(QueryDdUserDTO.builder().jobNumberList(new ArrayList<>(list)).build());
        userNameMap = userListResult.getData().stream().collect(Collectors.toMap(DdUserDTO::getJobNumber, DdUserDTO::getName));

        //附件处理
        if (!CollectionUtils.isEmpty(files)) {
            List<String> fileList = files.stream()
                    .map(DiMarketingFile::getFileKey)
                    .collect(Collectors.toList());
            List<OssPreviewDTO> previewDTOMap = ossService.getOssFileByList(OssSysCodeEnum.MARKETING.getType(), fileList, 0);
            for (OssPreviewDTO ossPreviewDTO : previewDTOMap) {
                FileAndBusinessVo fileVo = new FileAndBusinessVo();
                fileVo.setFileKey(ossPreviewDTO.getOssKey());
                fileVo.setFileName(ossPreviewDTO.getMimeName());
                fileVo.setIsPic(ossPreviewDTO.getMimeType().contains("image") ? 1 : 0);
                fileVo.setFileUrl(ossPreviewDTO.getShowUrl());
                fileVo.setCreateByName(userNameMap.get(fileKeyMap.get(fileVo.getFileKey()).getCreateBy()));
                fileVo.setCreateTime(fileKeyMap.get(fileVo.getFileKey()).getCreateTime());
                fileVo.setCommentId(fileKeyMap.get(fileVo.getFileKey()).getCommentId());
                fileVoList.add(fileVo);
            }
        }
        return R.ok(fileVoList.stream().sorted(Comparator.comparing(FileAndBusinessVo::getCreateTime)).toList());
    }

    /**
     * 新增文件
     *
     * @param diMarketingFile diMarketingFile
     * @return R
     */
    @PostMapping("/addFile")
    public R addFile(@RequestBody DiMarketingFile diMarketingFile) {
        log.info("请求参数{}", JSON.toJSONString(diMarketingFile));
        diMarketingFile.setBelonging("0");
        diMarketingFile.setCreateBy(SecurityUtils.getUsername());
        diMarketingFile.setCreateTime(DateUtils.getNowDate());
        diMarketingFileService.save(diMarketingFile);
        return R.ok();
    }

    /**
     * 删除文件
     *
     * @param vo 文件key
     * @return R
     */
    @PostMapping("/deleteFile")
    public R deleteFile(@RequestBody DiMarketingFileVo vo) {
        if (StringUtils.isBlank(vo.getFileKey())) {
            return R.fail("文件key不能为空");
        }
        QueryWrapper<DiMarketingFile> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("file_key", vo.getFileKey());
        DiMarketingFile file = new DiMarketingFile();
        file.setDelFlag(2);
        file.setUpdateBy(SecurityUtils.getUsername());
        file.setUpdateTime(DateUtils.getNowDate());
        diMarketingFileService.update(file,queryWrapper);
        return R.ok();
    }
}
