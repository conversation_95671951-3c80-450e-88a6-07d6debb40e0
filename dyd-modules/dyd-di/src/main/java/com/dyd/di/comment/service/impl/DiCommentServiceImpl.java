package com.dyd.di.comment.service.impl;

import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dyd.common.core.utils.StringUtils;
import com.dyd.di.comment.entity.DiComment;
import com.dyd.di.comment.mapper.DiCommentMapper;
import com.dyd.di.comment.service.DiCommentService;
import com.dyd.di.comment.vo.CommentQueryVO;
import com.dyd.di.comment.vo.CommentRespVO;
import com.dyd.di.comment.vo.CommentStageVO;
import com.dyd.di.interceptor.diMarketingClue.DiMarketingClueCreateEvent;
import com.dyd.di.interceptor.diMarketingClue.DiMarketingClueCreateListener;
import com.dyd.di.interceptor.diMarketingNiche.DiMarketingNicheStatusEvent;
import com.dyd.di.interceptor.diMarketingNiche.DiMarketingNicheStatusListener;
import com.dyd.di.interceptor.diOrder.DiOrderListener;
import com.dyd.di.interceptor.diOrder.DiOrderStageEvent;
import com.dyd.di.interceptor.entity.DiProjectStageChange;
import com.dyd.di.interceptor.mapper.DiProjectStageChangeMapper;
import com.dyd.di.marketing.domain.DiMarketingClue;
import com.dyd.di.marketing.domain.DiMarketingCustomer;
import com.dyd.di.marketing.domain.DiMarketingNiche;
import com.dyd.di.marketing.mapper.DiMarketingClueMapper;
import com.dyd.di.marketing.mapper.DiMarketingCustomerMapper;
import com.dyd.di.marketing.mapper.DiMarketingNicheMapper;
import com.dyd.di.order.domain.DiOrder;
import com.dyd.di.order.mapper.DiOrderMapper;
import com.dyd.di.process.domain.DiProjectRelation;
import com.dyd.di.process.mapper.DiProjectRelationMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 星记 Service 实现类
 */
@Service
public class DiCommentServiceImpl extends ServiceImpl<DiCommentMapper, DiComment> implements DiCommentService {
    @Autowired
    private DiOrderMapper orderMapper;
    @Autowired
    private DiMarketingNicheMapper nicheMapper;
    @Autowired
    private DiProjectRelationMapper relationMapper;
    @Autowired
    private DiMarketingClueMapper clueMapper;
    @Autowired
    private DiMarketingCustomerMapper customerMapper;
    @Autowired
    private DiProjectStageChangeMapper projectStageChangeMapper;
    @Autowired
    private DiOrderListener orderListener;
    @Autowired
    private DiMarketingNicheStatusListener nicheStatusListener;
    @Autowired
    private DiMarketingClueCreateListener clueCreateListener;

    @Override
    public CommentStageVO getStage(String projectNo, String nicheNo, String clueNo) {
        DiOrder diOrder = null;
        DiProjectRelation projectRelation = null;
        DiMarketingNiche niche = null;
        if (StringUtils.hasText(projectNo) || StringUtils.hasText(nicheNo)) {
            diOrder = orderMapper.selectOne(Wrappers.<DiOrder>lambdaQuery()
                            .eq(StringUtils.hasText(projectNo), DiOrder::getProjectNo, projectNo)
                            .eq(StringUtils.hasText(nicheNo), DiOrder::getNicheNo, nicheNo));
            projectRelation = relationMapper.selectOne(Wrappers.<DiProjectRelation>lambdaQuery()
                            .eq(StringUtils.hasText(projectNo), DiProjectRelation::getProjectNo, projectNo)
                            .eq(StringUtils.hasText(nicheNo), DiProjectRelation::getRelationNo, nicheNo)
                            .eq(DiProjectRelation::getRelationType, "business"));
            niche = nicheMapper.selectOne(Wrappers.<DiMarketingNiche>lambdaQuery()
                            .eq(StringUtils.hasText(nicheNo), DiMarketingNiche::getNicheNo, projectRelation.getRelationNo()));
        }else {
            niche = nicheMapper.selectOne(Wrappers.<DiMarketingNiche>lambdaQuery()
                    .eq(StringUtils.hasText(clueNo), DiMarketingNiche::getClueNo, clueNo));
        }
        if (diOrder != null) {
            DiProjectStageChange diProjectStageChange = projectStageChangeMapper.selectOne(
                    Wrappers.<DiProjectStageChange>lambdaQuery()
                            .eq(DiProjectStageChange::getRefNo, diOrder.getProjectNo())
                            .orderByDesc(DiProjectStageChange::getCreateTime)
                            .last("limit 1"));
            if (diProjectStageChange != null) {
                return CommentStageVO
                        .builder()
                        .stage(diProjectStageChange.getStage())
                        .projectNo(diOrder.getProjectNo())
                        .nicheNo(diOrder.getNicheNo())
                        .clueNo(niche.getClueNo())
                        .stageId(diProjectStageChange.getId())
                        .build();
            }
            return CommentStageVO
                    .builder()
                    .stage("unknown")
                    .projectNo(diOrder.getProjectNo())
                    .nicheNo(diOrder.getNicheNo())
                    .clueNo(niche.getClueNo())
                    .stageId(0L)
                    .build();
        }
        if (niche != null) {
            DiProjectStageChange diProjectStageChange = projectStageChangeMapper.selectOne(
                    Wrappers.<DiProjectStageChange>lambdaQuery()
                            .eq(DiProjectStageChange::getRefNo, niche.getNicheNo())
                            .orderByDesc(DiProjectStageChange::getCreateTime)
                            .last("limit 1"));
            if (diProjectStageChange != null && projectRelation != null) {
                return CommentStageVO
                        .builder()
                        .stage(diProjectStageChange.getStage())
                        .projectNo(projectRelation.getProjectNo())
                        .nicheNo(niche.getNicheNo())
                        .clueNo(niche.getClueNo())
                        .stageId(diProjectStageChange.getId())
                        .build();
            }
            return CommentStageVO
                    .builder()
                    .stage("unknown")
                    .projectNo(projectRelation != null ? projectRelation.getProjectNo() : null)
                    .nicheNo(niche.getNicheNo())
                    .clueNo(niche.getClueNo())
                    .stageId(0L)
                    .build();
        }
        if (StringUtils.hasText(clueNo)) {
            DiProjectStageChange diProjectStageChange = projectStageChangeMapper.selectOne(
                    Wrappers.<DiProjectStageChange>lambdaQuery()
                            .eq(DiProjectStageChange::getRefNo, clueNo)
                            .orderByDesc(DiProjectStageChange::getCreateTime)
                            .last("limit 1"));
            if (diProjectStageChange != null) {
                return CommentStageVO
                        .builder()
                        .stage(diProjectStageChange.getStage())
                        .projectNo(null)
                        .nicheNo(null)
                        .clueNo(clueNo)
                        .stageId(diProjectStageChange.getId())
                        .build();
            }
            return CommentStageVO
                    .builder()
                    .stage("unknown")
                    .projectNo(null)
                    .nicheNo(null)
                    .clueNo(clueNo)
                    .stageId(0L)
                    .build();
        }
        return null;
    }

    @Override
    public CommentQueryVO getCommentQuery(String projectNo, String nicheNo, String clueNo) {
        try {
            if (StringUtils.hasText(projectNo) || StringUtils.hasText(nicheNo)) {
                DiProjectRelation projectRelation = relationMapper.selectOne(Wrappers.<DiProjectRelation>lambdaQuery()
                        .eq(StringUtils.hasText(projectNo), DiProjectRelation::getProjectNo, projectNo)
                        .eq(StringUtils.hasText(nicheNo), DiProjectRelation::getRelationNo, nicheNo)
                        .eq(DiProjectRelation::getRelationType, "business"));
                DiMarketingNiche niche= null;
                DiMarketingCustomer customer = null;
                if (StringUtils.isNotBlank(nicheNo)) {
                    niche = nicheMapper.selectOne(Wrappers.<DiMarketingNiche>lambdaQuery()
                            .eq(DiMarketingNiche::getNicheNo, nicheNo));
                    customer = customerMapper.selectOne(Wrappers.<DiMarketingCustomer>lambdaQuery()
                            .eq(DiMarketingCustomer::getCustomerNo, niche.getCustomerNo())
                            .eq(DiMarketingCustomer::getDelFlag, "0"));
                }
                if (projectRelation != null) {
                    if (Objects.isNull(niche)) {
                        niche = nicheMapper.selectOne(Wrappers.<DiMarketingNiche>lambdaQuery()
                                .eq(DiMarketingNiche::getNicheNo, projectRelation.getRelationNo()));
                        customer = customerMapper.selectOne(Wrappers.<DiMarketingCustomer>lambdaQuery()
                                .eq(DiMarketingCustomer::getCustomerNo, niche.getCustomerNo())
                                .eq(DiMarketingCustomer::getDelFlag, "0"));
                    }
                    return CommentQueryVO.builder()
                            .company(MapUtil.<String,String>builder().put("key", customer.getCustomerNo()).put("value", customer.getCompanyName()).build())
                            .project(MapUtil.<String,String>builder().put("key", projectRelation.getProjectNo()).put("value", niche.getProjectName()).build())
                            .nicheNo(niche.getNicheNo())
                            .clueNo(niche.getClueNo())
                            .build();
                }else {
                    if (Objects.nonNull(niche)) {
                        return CommentQueryVO.builder()
                                .company(MapUtil.<String,String>builder().put("key", customer.getCustomerNo()).put("value", customer.getCompanyName()).build())
                                .project(null)
                                .nicheNo(niche.getNicheNo())
                                .clueNo(niche.getClueNo())
                                .build();
                    }
                }
                return null;
            }
            if (StringUtils.hasText(clueNo)) {
                DiMarketingNiche niche = nicheMapper.selectOne(Wrappers.<DiMarketingNiche>lambdaQuery()
                        .eq(DiMarketingNiche::getClueNo, clueNo));
                if (niche != null) {
                    DiProjectRelation projectRelation = relationMapper.selectOne(Wrappers.<DiProjectRelation>lambdaQuery()
                            .eq(DiProjectRelation::getRelationNo, niche.getNicheNo())
                            .eq(DiProjectRelation::getRelationType, "business"));
                    DiMarketingCustomer customer = customerMapper.selectOne(Wrappers.<DiMarketingCustomer>lambdaQuery()
                            .eq(DiMarketingCustomer::getCustomerNo, niche.getCustomerNo())
                            .eq(DiMarketingCustomer::getDelFlag, "0"));
                    return CommentQueryVO.builder()
                            .company(MapUtil.<String,String>builder().put("key", customer.getCustomerNo()).put( "value", customer.getCompanyName()).build())
                            .project(Objects.nonNull(projectRelation) ?
                                    MapUtil.<String,String>builder().put("key", projectRelation.getProjectNo()).put("value", niche.getProjectName()).build() : null)
                            .nicheNo(niche.getNicheNo())
                            .clueNo(clueNo)
                            .build();
                }else {
                    DiMarketingClue clue =
                            clueMapper.selectOne(Wrappers.<DiMarketingClue>lambdaQuery().eq(DiMarketingClue::getClueNo, clueNo));
                    DiMarketingCustomer customer = customerMapper.selectOne(Wrappers.<DiMarketingCustomer>lambdaQuery()
                            .eq(DiMarketingCustomer::getCustomerNo, clue.getCustomerNo())
                            .eq(DiMarketingCustomer::getDelFlag, "0"));
                    return CommentQueryVO.builder()
                            .company(MapUtil.<String,String>builder().put("key", customer.getCustomerNo()).put("value", customer.getCompanyName()).build())
                            .project(null)
                            .nicheNo(null)
                            .clueNo(clueNo)
                            .build();
                }
            }
        }catch (Exception e) {
            log.error(e.getMessage());
        }
        return null;
    }

    @Override
    public void tryUpdate(String projectNo, String nicheNo) {
        DiMarketingNiche niche =
                nicheMapper.selectOne(Wrappers.<DiMarketingNiche>lambdaQuery().eq(DiMarketingNiche::getNicheNo, nicheNo));
        if (Objects.nonNull(niche) && StringUtils.hasText(niche.getClueNo())) {
            List<DiComment> commentList = list(Wrappers.<DiComment>lambdaQuery()
                    .eq(DiComment::getClueNo, niche.getClueNo()));
            if (CollectionUtils.isEmpty(commentList)) {
                return;
            }
            commentList.forEach(comment -> {
                comment.setProjectNo(projectNo);
                comment.setNicheNo(nicheNo);
                comment.setClueNo(niche.getClueNo());
            });
            saveOrUpdateBatch(commentList);

        }
    }

    @Override
    public void createStage(CommentRespVO createReqVO) {
        DiMarketingNiche niche = null;
        if (StringUtils.hasText(createReqVO.getProjectNo()) || StringUtils.hasText(createReqVO.getNicheNo())) {
            DiOrder diOrder =
                    orderMapper.selectOne(Wrappers.<DiOrder>lambdaQuery()
                            .eq(StringUtils.hasText(createReqVO.getProjectNo()), DiOrder::getProjectNo, createReqVO.getProjectNo())
                            .eq(StringUtils.hasText(createReqVO.getNicheNo()), DiOrder::getNicheNo, createReqVO.getNicheNo()));
            if (Objects.nonNull(diOrder)) {
                orderListener.handleDataChange0(new DiOrderStageEvent(this, diOrder.getId(), diOrder.getOrderNo()));
                return;
            }else {
                DiProjectRelation relation = relationMapper.selectOne(Wrappers.<DiProjectRelation>lambdaQuery()
                        .eq(StringUtils.hasText(createReqVO.getProjectNo()), DiProjectRelation::getProjectNo, createReqVO.getProjectNo())
                        .eq(StringUtils.hasText(createReqVO.getNicheNo()), DiProjectRelation::getRelationNo, createReqVO.getNicheNo())
                        .eq(DiProjectRelation::getRelationType, "business"));
                if (relation != null) {
                    niche = nicheMapper.selectOne(Wrappers.<DiMarketingNiche>lambdaQuery()
                            .eq(DiMarketingNiche::getNicheNo, relation.getRelationNo()));
                }else {
                    if (StringUtils.hasText(createReqVO.getNicheNo())) {
                        niche = nicheMapper.selectOne(Wrappers.<DiMarketingNiche>lambdaQuery()
                                .eq(DiMarketingNiche::getNicheNo, createReqVO.getNicheNo()));
                    }
                }
            }
        }
        if (Objects.isNull(niche) && StringUtils.hasText(createReqVO.getClueNo())) {
            niche = nicheMapper.selectOne(Wrappers.<DiMarketingNiche>lambdaQuery()
                    .eq(DiMarketingNiche::getClueNo,createReqVO.getClueNo()));
        }
        if (Objects.nonNull(niche)) {
            nicheStatusListener.handleDataChange0(new DiMarketingNicheStatusEvent(this, niche.getId(), niche.getNicheNo()));
        }else {
            if (StringUtils.hasText(createReqVO.getClueNo())) {
                DiMarketingClue diMarketingClue =
                        clueMapper.selectOne(Wrappers.<DiMarketingClue>lambdaQuery().eq(DiMarketingClue::getClueNo, createReqVO.getClueNo()));
                if (Objects.nonNull(diMarketingClue)) {
                    clueCreateListener.handleDataChange0(new DiMarketingClueCreateEvent(this, diMarketingClue.getId()));
                }
            }
        }
    }
}