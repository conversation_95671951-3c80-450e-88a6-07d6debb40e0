package com.dyd.di.matrix.service;

import com.dyd.di.matrix.entity.DiMatrixTerritoryComparisonDetail;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 业务版图对比内容 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
public interface IDiMatrixTerritoryComparisonDetailService extends IService<DiMatrixTerritoryComparisonDetail> {

    default List<DiMatrixTerritoryComparisonDetail> queryByTerritoryId(Long territoryId, List<Long> groupIdList) {
        return this.lambdaQuery()
                .in(DiMatrixTerritoryComparisonDetail::getGroupId, groupIdList)
                .eq(
                        DiMatrixTerritoryComparisonDetail::getDelFlag, 0
                )
                .list();
    }

    default void removeByGroupId(List<Long> groupIds) {
        if (groupIds == null || groupIds.size() == 0) {
            return;
        }
        this.lambdaUpdate().in(DiMatrixTerritoryComparisonDetail::getGroupId, groupIds).eq(DiMatrixTerritoryComparisonDetail::getDelFlag, 0).remove();
    }
}
