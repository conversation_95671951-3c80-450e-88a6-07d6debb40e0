package com.dyd.di.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dyd.common.core.domain.R;
import com.dyd.di.order.domain.DiOrderMachineDesign;
import com.dyd.di.order.domain.DiOrderMaterielPlan;
import com.dyd.di.order.pojo.*;
import com.dyd.di.order.pojo.dto.MaterielPlanListDTO;
import com.dyd.di.order.pojo.dto.OrderMaterielPlanDetailDTO;
import org.apache.commons.collections4.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【di_order_materiel_plan(订单物料计划表)】的数据库操作Service
* @createDate 2024-09-20 09:36:48
*/
public interface DiOrderMaterielPlanService extends IService<DiOrderMaterielPlan> {

    OrderMaterielPlanDetailDTO detail(OrderInstallDetailVO orderInstallDetailVO);

    Long add(OrderMaterielAddVO orderMaterielAddVO);

    Long updateMateriel(OrderMaterielUpdateVO orderMaterielUpdateVO);

    R<String> operate(OrderMaterielOperateVO orderMaterielOperateVO);

    MaterielPlanListDTO checkList(OrderProduceBomVO orderProduceBomVO);

    default Map<String,DiOrderMaterielPlan> queryByOrderNo(String orderNo){
        var list = this.lambdaQuery().eq(DiOrderMaterielPlan::getOrderNo,orderNo).list();
        if (CollectionUtils.isEmpty(list)) {
            return new HashMap<>();
        }
        return list.stream().collect(Collectors.toMap(DiOrderMaterielPlan::getPreSaleCode, x -> x, (a, b) -> a));
    }

    List<String>  giveUp(String orderNo);
}
