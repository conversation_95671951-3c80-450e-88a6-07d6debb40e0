package com.dyd.di.matrix.service;

import com.dyd.di.matrix.dto.ComparisonGroupDTO;
import com.dyd.di.matrix.entity.DiMatrixTerritoryComparisonGroup;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 业务版图对比分组 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
public interface IDiMatrixTerritoryComparisonGroupService extends IService<DiMatrixTerritoryComparisonGroup> {

    /**
     * 保存对比分组
     *
     * @param territoryId
     * @param groupList
     */
    void saveComparison(Long territoryId, List<ComparisonGroupDTO> groupList);

    /**
     * 根据territoryId查询对比分组
     *
     * @param territoryId
     * @return
     */
    List<ComparisonGroupDTO> queryComparisonByTerritoryId(Long territoryId);

    default List<DiMatrixTerritoryComparisonGroup> queryByTerritoryId(Long territoryId) {
        return this.lambdaQuery().eq(DiMatrixTerritoryComparisonGroup::getTerritoryId, territoryId)
                .eq(DiMatrixTerritoryComparisonGroup::getDelFlag, 0)
                .orderByAsc(DiMatrixTerritoryComparisonGroup::getWeight)
                .list();
    }

    ;
}
