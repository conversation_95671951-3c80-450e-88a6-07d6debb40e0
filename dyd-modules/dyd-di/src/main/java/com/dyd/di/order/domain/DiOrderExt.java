package com.dyd.di.order.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.dyd.common.core.annotation.Excel;
import com.dyd.common.security.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单对象 di_order
 *
 * <AUTHOR>
 * @date 2024-06-19
 */
@EqualsAndHashCode(callSuper = true)
@Data

public class DiOrderExt extends BaseEntity implements Serializable {

    private Long id;

    /**
     * 主键
     */
    private String orderNo;
    /**
     * 临时排期
     */
    private String tempSchedule;

}
