package com.dyd.di.interceptor.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import com.baomidou.mybatisplus.annotation.*;
import java.util.Date;

/**
 * 项目阶段变更记录 DO
 */
@TableName("di_project_stage_change")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DiProjectStageChange {

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 阶段
     */
    private String stage;
    /**
     * 业务ID
     */
    private String refNo;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

}