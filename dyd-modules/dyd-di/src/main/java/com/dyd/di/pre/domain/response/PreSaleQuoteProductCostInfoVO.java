package com.dyd.di.pre.domain.response;

import com.dyd.di.pre.domain.DiPreSaleQuoteEstimatingGp;
import com.dyd.di.pre.entity.DiPreSaleLabel;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 销售报价单产品费用信息DTO(合同计算报价信息和真实成本时使用)
 */
@Data
public class PreSaleQuoteProductCostInfoVO {

    /**
     * 客户名称
     */
    private String firstPartyName;

    /**
     * 客户税号
     */
    private String firstPartyTaxNumber;

//    /**
//     * 客户账户
//     */
//    private String firstPartyAccount;
//
//    /**
//     * 客户开户行
//     */
//    private String firstPartyBank;
//
//    /**
//     * 客户开户预留手机号
//     */
//    private String firstPartyBankPhone;

    /**
     * 销售报价单ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long preSaleQuoteId;

    /**
     * 销售报价单编号
     */
    private String preSaleQuoteNo;

    /**
     * 理论成本合计
     */
    private BigDecimal costFeeTotal;

    /**
     * 销售报价合计
     */
    private BigDecimal saleQuoteTotal;

    /**
     * 毛利额合计
     */
    private BigDecimal grossProfitTotal;

    /**
     * 整单毛利率
     */
    private BigDecimal grossMarginTotal;

    /**
     * 销售服务周期报价成本合计
     */
    private BigDecimal saleServiceCycleTotal;

    /**
     * 销售服务报价合计
     */
    private BigDecimal saleServiceQuoteTotal;

    /**
     * 订单期望交付日期
     */
    private LocalDate expecteDate;

    /**
     * 订单类型
     */
    private String orderType;

    /**
     * 是否提前开票
     */
    private Integer preInvoice;

    /**
     * 合同交付周期(周)
     */
    private BigDecimal contractDeliveryCycleWeek;

    /**
     * 方案列表数据
     */
    private List<ProductMaterialDetailed> detailedList;

    /**
     * 账期列表数据
     */
    private List<PreSaleQuotePeriod> periodList;


    /**
     * 报价明细对应的方案信息
     */
    @Data
    public static class ProductMaterialDetailed {

        /**
         * 产品方案ID
         */
        @JsonSerialize(using = ToStringSerializer.class)
        private Long id;

        /**
         * 商机ID
         */
        @JsonSerialize(using = ToStringSerializer.class)
        private Long nicheId;

        /**
         * 商机编号
         */
        private String nicheNo;

        @JsonFormat(shape = JsonFormat.Shape.STRING)
        private Long preSaleId;

        /**
         * 产品方案编号
         */
        private String preSaleCode;

        /**
         * 方案名称
         */
        private String preSaleName;

        /**
         * 方案类型 1:项目 2:备件 3:燃配 4:出口  5:标准品
         */
        private String orderType;

        /**
         * 产品方案类型（1：标准品，2：非标准品）
         */
        private Integer preSaleType;

        /**
         * 物料号
         */
        private String materialNo;

        /**
         * 物料名称
         */
        private String materialName;

        /**
         * 版本号
         */
        private String versionNo;

        /**
         * 需求数量
         */
        private Integer demandNum;

        /**
         * 物料库存
         */
        private Integer materialStock;

        /**
         * 理论成本小计(产品方案清单费用合计)
         */
        private BigDecimal feeTotal;

        /**
         * 单套理论成本(亚杰那边怎么算的这里就是怎么算的)
         */
        private BigDecimal costFee;

        /**
         * 理论成本合计(亚杰那边怎么算的这里就是怎么算的)
         */
        private BigDecimal costFeeSum;

        /**
         * 单套理论交期（天）(现在取的是售前方案表中的指导工期字段)
         */
        private String guideDuration;

        /**
         * 售价报价(报价明细)
         */
        private BigDecimal saleQuote;

        /**
         * 单套报价
         */
        private BigDecimal singleSaleQuote;

        /**
         * 单套基准推荐报价
         */
        private BigDecimal recommendedQuotation;

        /**
         * 单套剩余推荐报价
         */
        private BigDecimal remainingRecommendedQuotation;

        /**
         * 单方案报价额最小值
         */
        private BigDecimal minimumQuotationAmount;

        /**
         * 单方案报价额最大值
         */
        private BigDecimal maximumQuotationAmount;

        /**
         * 交期报价(报价明细)
         */
        private BigDecimal deliveryQuote;

        /**
         * 毛利额(报价明细)
         */
        private BigDecimal grossProfit;

        /**
         * 毛利率(报价明细)
         */
        private BigDecimal grossMargin;

        /**
         * 国家
         */
        @JsonProperty("countryName")
        private String country;

        /**
         * 国家编码
         */
        private String countryCode;

        /**
         * 省
         */
        @JsonProperty("provinceName")
        private String province;

        /**
         * 省编码
         */
        private String provinceCode;

        /**
         * 市
         */
        @JsonProperty("cityName")
        private String city;

        /**
         * 市编码
         */
        private String cityCode;

        /**
         * 区
         */
        @JsonProperty("areaName")
        private String area;

        /**
         * 区编码
         */
        private String areaCode;

        /**
         * 销售服务报价
         */
        private BigDecimal saleServiceQuote;

        /**
         * 销售服务周期报价（人/天）
         */
        private BigDecimal saleServiceCycle;

        /**
         * 单套理论服务费
         */
        private BigDecimal systemServiceFee;

        /**
         * 单套服务理论周期
         */
        private BigDecimal systemServiceCycle;

        /**
         * 方案交付日期(项目期望交付日期)
         */
        private String expecteDate;

        /**
         * 包装方式费用(方案表)
         */
        private BigDecimal packageType;

        /**
         * 现场安装杂费(方案表)
         */
        private BigDecimal installationFee;

        /**
         * 标准成本(现在取的是物料价格表中的合计指导字段)
         */
        private BigDecimal guideSumCosts;

        /**
         * 合计成本
         */
        private BigDecimal costSumCosts;

        /**
         * 图纸文件Map,key:文件类型,value:文件key
         */
        private Map<String, List<String>> drawingFileKeyMap;


        /**
         * 图纸文件Map,key:文件类型,value:文件key
         */
        private List<String> drawingFileTypeList;

        /**
         * 产品参数配置信息
         */
        private List<DiPreSaleLabel> labelList;
        /**
         * 机械设计工期
         */
        private Integer mechanicalDesignDuration;

        /**
         * 电器设计工期
         */
        private Integer electricalDesignPeriod;

        /**
         * 研发周期(天)
         */
        private Integer rdDay;

        /**
         * 供应货期(天)
         */
        private Integer supplyDay;

        /**
         * 生产周期(天)
         */
        private Integer produceDay;

        /**
         * 实施周期(天)
         */
        private Integer implementDay;
        /**
         * 展示的版本号
         */
        private String showVersionNo;

        /**
         * 技术支持负责人代码
         */
        private String techSupportOwnerCode;

        /**
         * 技术支持负责人名称
         */
        private String techSupportOwnerName;
        /**
         * 技术支持天数
         */
        private Integer techSupportDay;


        /**
         * 技术难度
         */
        private String difficultyLevel;

        /**
         * 机械难度
         */
        private String mechanicalDifficulty;

        /**
         * 电气难度
         */
        private String electricalDifficulty;

        /**
         * 生产难度
         */
        private String productionDifficulty;

        /**
         * 售后难度
         */
        private String afterSalesDifficulty;
    }

    /**
     * 销售报价单中的账期信息
     */
    @Data
    public static class PreSaleQuotePeriod {

        /**
         *
         */
        private Integer id;

        /**
         * 账期
         */
        private Integer billPeriod;

        /**
         * 支付金额
         */
        private BigDecimal payAmount;

        /**
         * 账期描述
         */
        private String remark;

        /**
         * 付款比例
         */
        private BigDecimal payPercent;

        /**
         * 计划支付日
         */
        private LocalDate planPayDate;

        /**
         * 税率
         */
        private BigDecimal rate;

        /**
         * 发票情况
         */
        private String invoiceDesc;

        /**
         * 实际支付日期
         */
        private LocalDate realPayDate;
    }

    /**
     * GP1234 汇总
     */
    private GpDetailResponse gpDetailResponse;

    /**
     * 预测GP 汇总
     */
    private DiPreSaleQuoteEstimatingGp gpEstimating;

    /**
     * 预测GP 项目
     */
    private DiPreSaleQuoteEstimatingGp gpEstimating4Project;
    /**
     * Gp1234 项目
     */
    private GpDetailResponse gpDetailResponse4Project;
    /**
     * 预测GP 贸易
     */
    private DiPreSaleQuoteEstimatingGp gpEstimating4Trade;
    /**
     * Gp1234 贸易
     */
    private GpDetailResponse gpDetailResponse4Trade;

}
