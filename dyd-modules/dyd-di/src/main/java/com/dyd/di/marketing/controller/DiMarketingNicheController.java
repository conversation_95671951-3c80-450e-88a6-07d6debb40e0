package com.dyd.di.marketing.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.dyd.common.core.domain.R;
import com.dyd.common.core.exception.ServiceException;
import com.dyd.common.core.utils.DateUtils;
import com.dyd.common.core.utils.PageWrapper;
import com.dyd.common.core.utils.poi.ExcelUtil;
import com.dyd.common.core.web.controller.BaseController;
import com.dyd.common.core.web.domain.AjaxResult;
import com.dyd.common.core.web.page.TableDataInfo;
import com.dyd.common.log.annotation.Log;
import com.dyd.common.log.enums.BusinessType;
import com.dyd.common.security.utils.SecurityUtils;
import com.dyd.di.agency.constants.AgencyConstants;
import com.dyd.di.agency.domain.dto.AgencyTaskInfoDTO;
import com.dyd.di.agency.entity.DiAgencyTask;
import com.dyd.di.agency.enums.AgencyTaskTypeEnum;
import com.dyd.di.agency.mapper.DiAgencyTaskMapper;
import com.dyd.di.api.model.DiMarketingNicheListResponse;
import com.dyd.di.marketing.domain.*;
import com.dyd.di.marketing.domain.dto.RollbackRequirementStageRequest;
import com.dyd.di.marketing.domain.dto.UpdateNicheImportanceRequest;
import com.dyd.di.marketing.enums.NicheDingTalkEnum;
import com.dyd.di.marketing.domain.dto.Rollback2solutionStageRequest;
import com.dyd.di.marketing.manage.DiMarketingNicheAddManage;
import com.dyd.di.marketing.manage.DiMarketingNicheEditManage;
import com.dyd.di.marketing.service.DiMarketingNicheDemandService;
import com.dyd.di.marketing.service.IDiMarketingContactsService;
import com.dyd.di.marketing.service.IDiMarketingNicheService;
import com.dyd.di.matrix.domain.DiMatrixLabel;
import com.dyd.di.message.domain.DiMessageList;
import com.dyd.di.message.service.IDiMessageListService;
import com.dyd.di.pre.domain.request.ApprovalProcessRequest;
import com.dyd.di.pre.domain.request.NicheApprovalRequest;
import com.dyd.jdy.bean.stockUp.StockUpDataResponse;
import com.dyd.system.api.vo.BpmTaskApproveReqVO;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * 商机
 *
 * <AUTHOR>
 * @date 2024-06-19
 */
@RestController
@RequestMapping("/niche")
public class DiMarketingNicheController extends BaseController {
    @Autowired
    private IDiMarketingNicheService diMarketingNicheService;
    @Autowired
    private DiMarketingNicheAddManage diMarketingNicheAddManage;
    @Autowired
    private DiMarketingNicheEditManage diMarketingNicheEditManage;
    @Autowired
    private DiMarketingNicheDemandService diMarketingNicheDemandService;
    @Autowired
    private IDiMessageListService iDiMessageListService;
    @Autowired
    private RocketMQTemplate rocketMQTemplate;
    @Autowired
    private IDiMarketingContactsService diMarketingContactsService;
    @Resource
    private DiAgencyTaskMapper diAgencyTaskMapper;

    @PostMapping("/checkApprovalByV2")
    public R<Boolean> checkApprovalBy(@Validated @RequestBody NicheApprovalRequest request) {
        return R.ok(diMarketingNicheEditManage.checkApprovalBy(request));
    }

    /**
     * 查询商机列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DiMarketingNiche diMarketingNiche) {
        startPage();
        List<DiMarketingNiche> list = diMarketingNicheService.selectDiMarketingNicheList(diMarketingNiche);
        return getDataTable(list);
    }



    /**
     * 导出商机列表
     */
    @Log(title = "商机", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DiMarketingNiche diMarketingNiche) {
        List<DiMarketingNiche> list = diMarketingNicheService.selectDiMarketingNicheList(diMarketingNiche);
        ExcelUtil<DiMarketingNiche> util = new ExcelUtil<DiMarketingNiche>(DiMarketingNiche.class);
        util.exportExcel(response, list, "商机数据");
    }

    /**
     * 获取商机详细信息
     */
    @GetMapping(value = "/no_permission_required/{id}")
    public AjaxResult getInfoNoAuth(@PathVariable("id") String id) {
        return AjaxResult.success(diMarketingNicheService.selectDiMarketingNicheById(id, false));
    }

    /**
     * 获取商机详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return AjaxResult.success(diMarketingNicheService.selectDiMarketingNicheById(id, false));
    }

    /**
     * 新增商机
     */
    @Deprecated
    @Log(title = "商机", businessType = BusinessType.INSERT)
    @PostMapping
    public R add(@RequestBody DiMarketingNiche diMarketingNiche) {
        return R.ok(diMarketingNicheService.insertDiMarketingNiche(diMarketingNiche));
    }

    /**
     * 新增商机-New
     */
    @Log(title = "商机", businessType = BusinessType.INSERT)
    @PostMapping(value = "/add")
    public R addNew(@RequestBody DiMarketingNiche diMarketingNiche) {
        return R.ok(diMarketingNicheAddManage.manage(diMarketingNiche));
    }

    /**
     * 修改商机
     */
    @Log(title = "商机", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DiMarketingNiche diMarketingNiche) {
        diMarketingNiche.setCreateBy(null);
        diMarketingNiche.setCreateTime(null);
        diMarketingNiche.setUpdateBy(null);
        return toAjax(diMarketingNicheService.updateDiMarketingNiche(diMarketingNiche));
    }

    /**
     * 修改商机-NEW
     */
    @Log(title = "商机", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/edit")
    public AjaxResult editNew(@RequestBody DiMarketingNiche diMarketingNiche) {
        diMarketingNiche.setCreateBy(null);
        diMarketingNiche.setCreateTime(null);
        diMarketingNiche.setUpdateBy(null);
//        return toAjax( diMarketingNicheEditManage.manage(diMarketingNiche));
        JSONObject json = diMarketingNicheEditManage.manage(diMarketingNiche);
        return toAjax(json.getIntValue("cnt"));
    }

    @PostMapping(value = "/approval/create")
    public AjaxResult createApproval(@RequestBody DiMarketingNiche diMarketingNiche) {
        diMarketingNicheEditManage.createApproval(diMarketingNiche);
        return AjaxResult.success();
    }

    @Log(title = "商机流程审批", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/approval/{nicheId}/{agree}")
    public AjaxResult approval(@PathVariable("nicheId") String nicheId, @PathVariable("agree") Integer agree) {
        if (Objects.equals(agree, 1)) {
            Map statusMap = diMarketingNicheEditManage.approval(nicheId);
            if (Objects.nonNull(statusMap)) {
                return AjaxResult.success(statusMap);
            }else {
                return AjaxResult.error();
            }
        }else {
            if (!diMarketingNicheEditManage.rejectTask(nicheId)) {
                return AjaxResult.error();
            }
        }
        return AjaxResult.success();
    }

    /**
     * 申请技术支持推送
     */
    @Log(title = "商机", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/applyForTechnicalSupport")
    public R<String> applyForTechnicalSupport(@RequestBody DiMarketingNiche diMarketingNiche) {
        DiMarketingNiche niche = new DiMarketingNiche();
        niche.setId(diMarketingNiche.getId());
        niche.setApplyForTechnicalSupport("1");
        niche.setNicheStatus("1");
        diMarketingNicheService.updateById(niche);
        diMarketingNiche.setNicheStatus("1");
        diMarketingNicheAddManage.technicalSupportDingTalk(diMarketingNiche);
        return R.ok("申请成功");
    }

    /**
     * 变更技术支持及推送
     */
    @Log(title = "商机", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/updateDictPersonCharge")
    public R<String> updateDictPersonCharge(@RequestBody DiMarketingNiche diMarketingNiche) {
        if(StrUtil.isBlank(diMarketingNiche.getDiMarketingNicheDemand().getDictPersonChargeNo())){
            throw new ServiceException("技术支持工号不能为空！");
        }
        if(StrUtil.isBlank(diMarketingNiche.getDiMarketingNicheDemand().getDictPersonChargeName())){
            throw new ServiceException("技术支持名字不能为空！");
        }
        diMarketingNicheAddManage.updateTechnicalSupportDingTalk(diMarketingNiche);
        DiMarketingNicheDemand niche = new DiMarketingNicheDemand();
        niche.setId(diMarketingNiche.getDiMarketingNicheDemand().getId());
        niche.setDictPersonChargeNo(diMarketingNiche.getDiMarketingNicheDemand().getDictPersonChargeNo());
        niche.setDictPersonChargeName(diMarketingNiche.getDiMarketingNicheDemand().getDictPersonChargeName());
        diMarketingNicheDemandService.updateById(niche);
        return R.ok("变更成功");
    }

    /**
     * 提交方案
     */
    @Log(title = "商机", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/submitProposal")
    public R<String> submitProposal(@RequestBody DiMarketingNiche diMarketingNiche) {
        //判断商机状态，不等于1，证明还未方案报价，将商机状态改为方案报价并发送钉钉消息
        diMarketingNicheService.updateNicheStatus(diMarketingNiche.getNicheNo(), "8");
        //发送钉钉消息
        String nickName = "";
        if (SecurityUtils.getLoginUser() != null && SecurityUtils.getLoginUser().getSysUser() != null) {
            nickName = SecurityUtils.getLoginUser().getSysUser().getNickName();
        }
        String content = StrUtil.format(NicheDingTalkEnum.NICHE_STATE_PLAN_OFFER.getMessage(), nickName);
        diMarketingNiche.setNicheStatus("8");
        diMarketingNicheService.updateNicheStatusSendDingTalk(diMarketingNiche, content);
        //将方案阶段代办删除
        AgencyTaskInfoDTO agencyTaskInfoDto = new AgencyTaskInfoDTO();
        agencyTaskInfoDto.setBusinessKey(diMarketingNiche.getNicheNo());
        agencyTaskInfoDto.setTaskTypeEnum(AgencyTaskTypeEnum.NICHE_TECHNICAL_SUPPORT);
        agencyTaskInfoDto.setType("2");
        String topic = AgencyConstants.AGENCY_TASK_TOPIC + AgencyConstants.DELIMITER_COLON + AgencyConstants.AGENCY_TASK_TAG;
        rocketMQTemplate.syncSend(topic, agencyTaskInfoDto);
        return R.ok("提交成功");
    }

    /**
     * 自建方案
     */
    @Log(title = "商机", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/selfBuiltPlan")
    public R<String> selfBuiltPlan(@RequestBody DiMarketingNiche diMarketingNiche) {
        DiMarketingNiche niche = new DiMarketingNiche();
        niche.setId(diMarketingNiche.getId());
        niche.setNicheStatus("1");
        diMarketingNicheService.updateById(niche);

        //商机状态变更发送钉钉消息给主要销售跟共享销售
        String nickName = "";
        if (SecurityUtils.getLoginUser() != null && SecurityUtils.getLoginUser().getSysUser() != null) {
            nickName = SecurityUtils.getLoginUser().getSysUser().getNickName();
        }
        String content = StrUtil.format(NicheDingTalkEnum.NICHE_STATE_SCHEME_STAGE.getMessage(), nickName);

        //获取共享销售
        DiMarketingContacts diMarketingContacts = new DiMarketingContacts();
        diMarketingContacts.setBusinessId(diMarketingNiche.getNicheNo());
        List<DiMarketingContacts> contactsList = diMarketingContactsService.selectDiMarketingContactsList(diMarketingContacts);
        String sharedBy = "";
        for (int i = 0; i < contactsList.size(); i++) {
            contactsList.get(i).setBelonging("商机");
            contactsList.get(i).setBusinessId(diMarketingNiche.getNicheNo());
            if (StringUtils.isNotBlank(contactsList.get(i).getContactsOwner())) {
                sharedBy += contactsList.get(i).getContactsOwner() + ",";
            }
        }
        diMarketingNiche.setSharedBy(sharedBy);
        diMarketingNicheService.updateNicheStatusSendDingTalk(diMarketingNiche, content);

        //删除代办任务
        LambdaUpdateChainWrapper<DiAgencyTask> updateWrapper = new LambdaUpdateChainWrapper<>(diAgencyTaskMapper);
        updateWrapper.eq(DiAgencyTask::getBusinessKey, diMarketingNiche.getNicheNo())
                .eq(DiAgencyTask::getTaskTypeCode, "niche_manage")
                .set(DiAgencyTask::getIsDeleted, AgencyConstants.STR_ONE)
                .update();
        return R.ok("创建成功");
    }


    /**
     * 删除商机
     */
    @Log(title = "商机", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(diMarketingNicheService.deleteDiMarketingNicheByIds(ids));
    }

    @GetMapping("/getNichePage")
    public R<PageWrapper<List<DiMarketingNicheListResponse>>> getNichePage(@RequestParam("pageNum") Integer pageNum, @RequestParam("pageSize") Integer pageSize) {
        return R.ok(diMarketingNicheService.getNichePage(pageNum, pageSize));
    }

    /**
     * 获取字典值的负责人
     *
     * @param dictPersonCharge
     * @return
     */
    @PostMapping("/getDictPersonChargeName")
    public R<DictPersonChargeVo> getDictPersonChargeName(@RequestBody DictPersonCharge dictPersonCharge) {

        return R.ok(diMarketingNicheService.getDictPersonChargeName(dictPersonCharge));
    }

    /**
     * 获取 01无合同提前备物料&成品
     *
     * @return
     */
    @PostMapping("/getStockUpList")
    public R<List<? extends Object>> getStockUpList(@RequestBody StockUpRequest request) {
        return R.ok(diMarketingNicheService.getStockUpList(request.getDataId()));
    }

    /**
     * 更新拆解状态
     *
     * @param diMarketingNicheDemand 入参
     * @return 结果
     */
    @PostMapping("/updateDisassembleState")
    public R<Void> updateDisassembleState(@RequestBody DiMarketingNicheDemand diMarketingNicheDemand) {
        diMarketingNicheDemandService.updateDisassembleState(diMarketingNicheDemand);
        return R.ok();
    }

    /**
     * 回滚到方案阶段
     *
     * @param request 入参
     * @return 结果
     */
    @PostMapping("/rollback2solutionStage")
    public R<Void> rollback2solutionStage(@RequestBody Rollback2solutionStageRequest request) {
        diMarketingNicheService.rollback2solutionStage(request.getNicheId());
        return R.ok();
    }

    /**
     * 退回需求阶段
     *
     * @param request 入参
     * @return 结果
     */
    @PostMapping("/rollbackRequirementStage")
    public R<Void> rollbackRequirementStage(@RequestBody RollbackRequirementStageRequest request) {
        diMarketingNicheService.rollbackRequirementStage(request.getNicheId());
        return R.ok();
    }

    /**
     * 修改商机备注
     *
     * @param diMarketingNiche 入参
     * @return 结果
     */
    @PostMapping("/updateNicheRemarks")
    public R<String> updateNicheRemarks(@RequestBody DiMarketingNiche diMarketingNiche) {
        DiMarketingNiche niche = new DiMarketingNiche();
        niche.setId(diMarketingNiche.getId());
        niche.setNicheRemarks(diMarketingNiche.getNicheRemarks());
        diMarketingNicheService.updateById(niche);
        return R.ok("修改成功");
    }

    /**
     * 修改商机预计成交金额
     *
     * @param diMarketingNiche 入参
     * @return 结果
     */
    @PostMapping("/updateNicheExpectedTransactionAmount")
    public R<String> updateNicheExpectedTransactionAmount(@RequestBody DiMarketingNiche diMarketingNiche) {
        DiMarketingNiche niche = new DiMarketingNiche();
        niche.setId(diMarketingNiche.getId());
        niche.setExpectedTransactionAmount(diMarketingNiche.getExpectedTransactionAmount());
        diMarketingNicheService.updateById(niche);
        return R.ok("修改成功");
    }

    @GetMapping("/dataMaintenance")
    public R<Void> dataMaintenance() {
        diMarketingNicheService.dataMaintenance();
        return R.ok();
    }

    @GetMapping("/reissueAgencyTaskAndMessage")
    public R<Void> reissueAgencyTaskAndMessage() {
        List<DiMarketingNiche> nicheList = diMarketingNicheService.list(new LambdaQueryWrapper<DiMarketingNiche>()
                .eq(DiMarketingNiche::getNicheStatus, "8")
                .eq(DiMarketingNiche::getDelFlag, "0"));
        if (CollectionUtil.isNotEmpty(nicheList)) {
            nicheList.forEach(niche -> {
                if (StrUtil.isNotBlank(niche.getNicheOwner())) {
                    String title = StrUtil.format(NicheDingTalkEnum.NICHE_UPDATE_QUOTE_STATE.getTitle(), niche.getNicheNo(), niche.getProjectName());
                    String message = StrUtil.format(NicheDingTalkEnum.NICHE_UPDATE_QUOTE_STATE.getMessage(), niche.getNicheNo(), niche.getProjectName());
                    //生成代办
                    diMarketingNicheService.saveAgencyTaskSendMessage(Collections.singletonList(niche.getNicheOwner()), niche, 3);
                    DiMessageList diMessageList = new DiMessageList();
                    diMessageList.setBusinessModule("商机");
                    diMessageList.setContent(message);
                    diMessageList.setTitle(title);
                    diMessageList.setSendingTime(new Date());
                    diMessageList.setRemarks(diMessageList.getTitle());
                    diMessageList.setSendingUser(niche.getNicheOwner());
                    iDiMessageListService.insertDiMessageList(diMessageList);
                }
            });
        }
        return R.ok();
    }

    /**
     * 更新商机询价定性 和 定性原因
     * @param request
     * @return
     */
    @PostMapping("/updateNicheImportance")
    public R updateNicheImportance(@RequestBody @Validated UpdateNicheImportanceRequest request){

        diMarketingNicheService.updateNicheImportance(request);
        return R.ok();
    }

}
