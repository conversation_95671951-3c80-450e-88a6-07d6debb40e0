package com.dyd.di.comment.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CommentQueryReqVO {

    /**
     * 项目编号
     */
    private String projectNo;

    /**
     * 商机编号
     */
    private String nicheNo;

    /**
     * 线索编号
     */
    private String clueNo;

    /**
     * 客户编号
     */
    private String companyNo;

    /**
     * 是否是重点消息
     */
    private Integer importanceMark;

    private String content;

    private Integer needComment;

}
