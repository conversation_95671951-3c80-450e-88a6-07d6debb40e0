package com.dyd.di.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dyd.common.core.domain.R;
import com.dyd.common.core.utils.PageWrapper;
import com.dyd.common.core.web.page.TableDataInfo;
import com.dyd.di.api.model.*;
import com.dyd.di.common.DiHelper;
import com.dyd.di.contract.domain.response.ContractListPageResponse;
import com.dyd.di.order.domain.DiOrder;
import com.dyd.di.order.domain.DiOrderBillPeriod;
import com.dyd.di.order.domain.DiOrderExt;
import com.dyd.di.order.enums.OrderDingTalkEnum;
import com.dyd.di.order.pojo.*;
import com.dyd.di.order.pojo.dto.*;

import java.util.List;

/**
 * 订单Service接口
 *
 * <AUTHOR>
 * @date 2024-06-19
 */
public interface IDiOrderExtService extends IService<DiOrderExt> {


    default DiOrderExt queryByOrderNo(String orderNo) {
        return DiHelper.first(this.lambdaQuery().eq(DiOrderExt::getOrderNo, orderNo).list());
    }


}
