package com.dyd.di.agency.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.nacos.shaded.com.google.common.collect.FluentIterable;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dyd.common.core.exception.ServiceException;
import com.dyd.common.core.utils.PageHelp;
import com.dyd.common.core.utils.PageWrapper;
import com.dyd.common.security.utils.SecurityUtils;
import com.dyd.di.agency.constants.AgencyConstants;
import com.dyd.di.agency.conver.AgencyTaskConvertUtil;
import com.dyd.di.agency.domain.dto.ChangeLiabilityByDTO;
import com.dyd.di.agency.domain.dto.CommonDTO;
import com.dyd.di.agency.domain.dto.FindAgencyTaskListDTO;
import com.dyd.di.agency.domain.vo.AgencyTaskListVO;
import com.dyd.di.agency.domain.vo.TaskArgs;
import com.dyd.di.agency.domain.vo.TaskChangeResult;
import com.dyd.di.agency.entity.DiAgencyChangeRecord;
import com.dyd.di.agency.entity.DiAgencyTask;
import com.dyd.di.agency.enums.AgencyTaskTypeEnum;
import com.dyd.di.agency.mapper.DiAgencyChangeRecordMapper;
import com.dyd.di.agency.mapper.DiAgencyTaskMapper;
import com.dyd.di.agency.service.DiAgencyTaskService;
import com.dyd.di.material.service.CommonService;
import com.dyd.di.process.domain.DiProjectRelationUser;
import com.dyd.di.process.mapper.DiProjectRelationUserMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 代办任务Service业务层处理
 */
@Service
@Slf4j
public class DiAgencyTaskServiceImpl extends ServiceImpl<DiAgencyTaskMapper, DiAgencyTask> implements DiAgencyTaskService {

    @Resource
    private DiAgencyTaskMapper diAgencyTaskMapper;

    @Resource
    private DiAgencyChangeRecordMapper diAgencyChangeRecordMapper;

    @Resource
    private DiProjectRelationUserMapper diProjectRelationUserMapper;

    @Resource
    private CommonService commonService;

    @Resource
    private AgencyTaskConvertUtil agencyTaskConvertUtil;

    /**
     * 查询代办任务列表(无分页)
     *
     * @param dto 查询条件
     * @return 结果
     */
    @Override
    public List<AgencyTaskListVO> findAgencyTaskList(FindAgencyTaskListDTO dto) {
        //根据当前登陆者查询
        dto.setLiabilityBy(SecurityUtils.getUsername());
        List<AgencyTaskListVO> voList = diAgencyTaskMapper.findAgencyTaskList(dto);
        if (CollectionUtil.isEmpty(voList)) {
            return voList;
        }
        //构建列表员工姓名
        formListName(voList);
        return voList;
    }

    /**
     * 查询代办任务列表(分页)
     *
     * @param dto 查询条件
     * @return 结果
     */
    @Override
    public PageWrapper<List<AgencyTaskListVO>> findAgencyTaskListPage(FindAgencyTaskListDTO dto) {
        //根据当前登陆者查询
        dto.setLiabilityBy(SecurityUtils.getUsername());
        Page<Object> page = PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        List<AgencyTaskListVO> voList = diAgencyTaskMapper.findAgencyTaskList(dto);
        if (CollectionUtil.isEmpty(voList)) {
            return PageHelp.render(page, voList);
        }
        //构建列表员工姓名
        formListName(voList);
        voList = voList.stream().sorted(Comparator.comparing(AgencyTaskListVO::getUpdateTime).reversed()).collect(Collectors.toList());
        return PageHelp.render(page, voList);
    }

    /**
     * 构建列表员工姓名
     *
     * @param voList 列表信息
     */
    private void formListName(List<AgencyTaskListVO> voList) {
        //获取员工姓名
        Map<String, String> nameMap = commonService.getUserNameByJob(null, voList.stream().map(AgencyTaskListVO::getLiabilityBy).filter(StringUtils::isNotBlank).toList());
        if (CollectionUtil.isEmpty(nameMap)) {
            log.info("代办任务列表查询---根据员工工号未获取到员工姓名");
            return;
        }
        voList.forEach(vo -> {
            if (nameMap.containsKey(vo.getLiabilityBy())) {
                vo.setLiabilityByName(nameMap.get(vo.getLiabilityBy()));
            }
        });
    }

    /**
     * 更新是否已读
     *
     * @param dto 更新参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateIsReadById(CommonDTO dto) {
        //获取代办任务数据
        DiAgencyTask agencyTask = diAgencyTaskMapper.selectById(dto.getId());
        if (null == agencyTask) {
            throw new ServiceException("代办任务不存在");
        }
        //将未读改为已读
        LambdaUpdateChainWrapper<DiAgencyTask> updateWrapper = new LambdaUpdateChainWrapper<>(diAgencyTaskMapper);
        updateWrapper.eq(DiAgencyTask::getId, dto.getId())
                .set(DiAgencyTask::getIsRead, AgencyConstants.STR_TWO)
                .update();
    }

    /**
     * 删除代办任务
     *
     * @param dto 删除参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteAgencyTask(CommonDTO dto) {
        //获取代办任务数据
        DiAgencyTask agencyTask = diAgencyTaskMapper.selectById(dto.getId());
        if (null == agencyTask) {
            throw new ServiceException("代办任务不存在");
        }
        LambdaUpdateChainWrapper<DiAgencyTask> updateWrapper = new LambdaUpdateChainWrapper<>(diAgencyTaskMapper);
        updateWrapper.eq(DiAgencyTask::getId, dto.getId())
                .set(DiAgencyTask::getIsDeleted, AgencyConstants.STR_ONE)
                .update();
    }

    /**
     * 变更代办任务责任人
     *
     * @param dto 变更参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String changeLiabilityBy(ChangeLiabilityByDTO dto) {
        //获取代办任务数据
        DiAgencyTask agencyTask = diAgencyTaskMapper.selectById(dto.getId());
        if (null == agencyTask) {
            return "代办任务不存在";
        }
        if (dto.getNewLiabilityBy().equals(agencyTask.getLiabilityBy())) {
            return "责任人相同";
        }
        //新增代办变更记录
        DiAgencyChangeRecord agencyChangeRecord = new DiAgencyChangeRecord();
        agencyChangeRecord.setAgencyType(AgencyConstants.ONE);
        agencyChangeRecord.setAgencyId(agencyTask.getId());
        agencyChangeRecord.setOldLiabilityBy(agencyTask.getLiabilityBy());
        agencyChangeRecord.setNewLiabilityBy(dto.getNewLiabilityBy());
        agencyChangeRecord.setCreateBy(SecurityUtils.getUsername());
        agencyChangeRecord.setUpdateBy(SecurityUtils.getUsername());

        List<DiAgencyTask> newUserOldTaskList = this.lambdaQuery()
                .eq(DiAgencyTask::getLiabilityBy, dto.getNewLiabilityBy())
                .eq(DiAgencyTask::getTaskTypeCode, agencyTask.getTaskTypeCode())
                .eq(DiAgencyTask::getBusinessKey, agencyTask.getBusinessKey())
                .eq(DiAgencyTask::getIsDeleted, AgencyConstants.STR_ZERO)
                .list();

        if (CollectionUtil.isNotEmpty(newUserOldTaskList)) {
            //已有代办的情况下，插入转交记录，消除当前代办
            agencyChangeRecord.setRemark("用户已经有代办:" + newUserOldTaskList.get(0).getId());
            diAgencyChangeRecordMapper.insert(agencyChangeRecord);
            LambdaUpdateChainWrapper<DiAgencyTask> updateWrapper = new LambdaUpdateChainWrapper<>(diAgencyTaskMapper);
            updateWrapper.eq(DiAgencyTask::getId, dto.getId())
                    .set(DiAgencyTask::getIsDeleted, AgencyConstants.STR_ONE)
                    .update();
            return null;
        }
        diAgencyChangeRecordMapper.insert(agencyChangeRecord);

        //原记录变更为已处理
        agencyTask.setIsDeleted("1");
        agencyTask.setIsRead("2");
        agencyTask.setUpdateTime(new Date());
        diAgencyTaskMapper.updateById(agencyTask);

        //转交新增记录
        agencyTask.setId(null);
        agencyTask.setIsDeleted("0");
        agencyTask.setLiabilityBy(dto.getNewLiabilityBy());
        agencyTask.setIsRead(AgencyConstants.STR_ONE);
        agencyTask.setCreateTime(new Date());
        agencyTask.setUpdateTime(new Date());
        diAgencyTaskMapper.insert(agencyTask);

        //变更负责人
        /*LambdaUpdateChainWrapper<DiAgencyTask> updateWrapper = new LambdaUpdateChainWrapper<>(diAgencyTaskMapper);
        updateWrapper.eq(DiAgencyTask::getId, dto.getId())
                .set(DiAgencyTask::getLiabilityBy, dto.getNewLiabilityBy())
                .set(DiAgencyTask::getIsRead, AgencyConstants.STR_ONE)
                .update();*/
        //将变更后的负责人加入项目关联用户表中
        if (StringUtils.isNotBlank(agencyTask.getProjectNo())) {
            List<DiProjectRelationUser> relationUserList = diProjectRelationUserMapper.selectList(new LambdaQueryWrapper<DiProjectRelationUser>()
                    .eq(DiProjectRelationUser::getProjectNo, agencyTask.getProjectNo())
                    .eq(DiProjectRelationUser::getDelFlag, AgencyConstants.ZERO));
            if (CollectionUtil.isNotEmpty(relationUserList)) {
                boolean judgeNum = relationUserList.size() > 1;
                DiProjectRelationUser relationUser = relationUserList.get(AgencyConstants.ZERO);
                List<String> userList = Arrays.stream(relationUser.getRelationUsers().split(",")).filter(StringUtils::isNotBlank).collect(Collectors.toList());
                userList.add(dto.getNewLiabilityBy());
                relationUser.setRelationUsers(String.join(",", userList));
                diProjectRelationUserMapper.updateById(relationUser);
                if (judgeNum) {
                    log.info("DiAgencyTaskServiceImpl---changeLiabilityBy()---项目关联的用户表数量大于1，项目编号：{}", agencyTask.getProjectNo());
                }
            } else {
                log.info("DiAgencyTaskServiceImpl---changeLiabilityBy()---项目未关联用户，项目编号：{}", agencyTask.getProjectNo());
            }
        }
        return null;
    }

    /**
     * 获取当前登录者的未读代办任务数量
     *
     * @return 数量
     */
    @Override
    public Integer getUnReadCountByLogin(String taskTypeCode) {
        Long num = diAgencyTaskMapper.selectCount(new LambdaQueryWrapper<DiAgencyTask>()
                .eq(DiAgencyTask::getLiabilityBy, SecurityUtils.getUsername())
                .eq(DiAgencyTask::getIsRead, AgencyConstants.STR_ONE)
                .eq(DiAgencyTask::getIsDeleted, AgencyConstants.STR_ZERO)
                .eq(StringUtils.isNotBlank(taskTypeCode), DiAgencyTask::getTaskTypeCode, taskTypeCode));
        return num.intValue();
    }

    /**
     * 添加代办任务
     *
     * @param bizCode
     * @param taskType
     * @param userList
     * @param args
     * @return
     */
    @Override
    public TaskChangeResult addTask(String bizCode, AgencyTaskTypeEnum taskType, List<String> userList, TaskArgs args) {
        return this.mergeTask(bizCode, taskType, userList, args, false);
    }

    public TaskChangeResult mergeTask(String bizCode, AgencyTaskTypeEnum taskType, List<String> userList, TaskArgs args, boolean removeNotMatch) {
        TaskChangeResult taskChangeResult = new TaskChangeResult();
        if (CollectionUtil.isEmpty(userList)) {
            return taskChangeResult;
        }
        Set<String> newSet = new HashSet<>(userList);
        Set<String> oldSet = new HashSet<>();
        List<DiAgencyTask> oldTaskList = this.queryByTaskType(bizCode, taskType);
        if (oldTaskList != null) {
            oldSet = oldTaskList.stream().map(DiAgencyTask::getLiabilityBy).collect(Collectors.toSet());
        }
        taskChangeResult.setExistedUserList(oldSet.stream().filter(newSet::contains).collect(Collectors.toList()));
        Set<String> finalOldSet = oldSet;
        taskChangeResult.setAddUserList(newSet.stream().filter(x -> !finalOldSet.contains(x)).toList());

        List<DiAgencyTask> saveList = new ArrayList<>();
        for (String saveTaskLiabilityBy : taskChangeResult.getAddUserList()) {
            DiAgencyTask agencyTask = agencyTaskConvertUtil.argsConvertAgencyTaskEntity(args);
            if (StringUtils.isNotBlank(args.getJumpKey())) {
                agencyTask.setJumpKey(args.getJumpKey());
            } else {
                agencyTask.setJumpKey(bizCode);
            }
            if (StringUtils.isNotBlank(args.getTaskId())) {
                agencyTask.setTaskId(args.getTaskId());
            } else {
                agencyTask.setTaskId(bizCode);
            }
            agencyTask.setTaskType(taskType.getTypeDesc());
            agencyTask.setTaskTypeCode(taskType.getTypeCode());
            agencyTask.setLiabilityBy(saveTaskLiabilityBy);
            agencyTask.setBusinessKey(bizCode);
            saveList.add(agencyTask);
        }
        if (CollectionUtil.isNotEmpty(saveList)) {
            this.saveBatch(saveList);
        }

        if (removeNotMatch) {
            taskChangeResult.setRemoveUserList(oldSet.stream().filter(x -> !newSet.contains(x)).toList());
            if (CollectionUtils.isNotEmpty(taskChangeResult.getRemoveUserList())) {
                LambdaUpdateWrapper<DiAgencyTask> queryWrapper = new LambdaUpdateWrapper<>();
                queryWrapper.in(DiAgencyTask::getLiabilityBy, taskChangeResult.getRemoveUserList());
                queryWrapper.eq(DiAgencyTask::getBusinessKey, bizCode);
                queryWrapper.eq(DiAgencyTask::getTaskTypeCode, taskType.getTypeCode());
                queryWrapper.set(DiAgencyTask::getIsDeleted, AgencyConstants.STR_ONE);
                this.update(queryWrapper);
            }
        }

        return taskChangeResult;
    }

    /**
     * 移除代办任务
     *
     * @param bizCode
     * @param taskType
     * @param userList
     * @return
     */
    @Override
    public TaskChangeResult removeTask(String bizCode, AgencyTaskTypeEnum taskType, List<String> userList) {
        TaskChangeResult taskChangeResult = new TaskChangeResult();
        if (CollectionUtil.isEmpty(userList)) {
            return taskChangeResult;
        }

        List<DiAgencyTask> oldTaskList = this.queryByTaskType(bizCode, taskType);
        taskChangeResult.setRemoveUserList(oldTaskList.stream().map(DiAgencyTask::getLiabilityBy).filter(userList::contains).toList());
        if (CollectionUtil.isNotEmpty(taskChangeResult.getRemoveUserList())) {
            LambdaUpdateWrapper<DiAgencyTask> queryWrapper = new LambdaUpdateWrapper<>();
            queryWrapper.in(DiAgencyTask::getLiabilityBy, taskChangeResult.getRemoveUserList());
            queryWrapper.eq(DiAgencyTask::getBusinessKey, bizCode);
            queryWrapper.eq(DiAgencyTask::getTaskTypeCode, taskType.getTypeCode());
            queryWrapper.set(DiAgencyTask::getIsDeleted, AgencyConstants.STR_ONE);
            this.update(queryWrapper);
        }
        return taskChangeResult;
    }

    /**
     * 设置代办任务
     *
     * @param bizCode
     * @param taskType
     * @param userList
     * @param args
     * @return
     */
    @Override
    public TaskChangeResult setTask(String bizCode, AgencyTaskTypeEnum taskType, List<String> userList, TaskArgs args) {
        return this.mergeTask(bizCode, taskType, userList, args, true);
    }

    /**
     * 清除代办任务
     *
     * @param bizCode
     * @param taskType
     * @return
     */
    @Override
    public TaskChangeResult clearTask(String bizCode, AgencyTaskTypeEnum taskType) {
        log.info("清除代办任务,bizCode={},task:{}", bizCode, taskType);
        TaskChangeResult taskChangeResult = new TaskChangeResult();
        if (taskType != null) {
            taskChangeResult.setRemoveUserList(this.queryByTaskType(bizCode, taskType).stream().map(DiAgencyTask::getLiabilityBy).toList());
        }
        LambdaUpdateWrapper<DiAgencyTask> queryWrapper = new LambdaUpdateWrapper<>();
        if (CollectionUtil.isNotEmpty(taskChangeResult.getRemoveUserList())) {
            queryWrapper.in(DiAgencyTask::getLiabilityBy, taskChangeResult.getRemoveUserList());
        }
        queryWrapper.eq(DiAgencyTask::getBusinessKey, bizCode);
        queryWrapper.eq(DiAgencyTask::getTaskTypeCode, taskType.getTypeCode());
        queryWrapper.eq(DiAgencyTask::getIsDeleted, AgencyConstants.STR_ZERO);
        queryWrapper.set(DiAgencyTask::getIsDeleted, AgencyConstants.STR_ONE);
        this.update(queryWrapper);
        return taskChangeResult;
    }

    /**
     * 清除代办任务
     *
     * @param bizCode
     * @return
     */
    @Override
    public TaskChangeResult clearTask(String bizCode) {
        log.info("清除代办任务,{}", bizCode);
        TaskChangeResult taskChangeResult = new TaskChangeResult();
        taskChangeResult.setRemoveUserList(this.queryByBiz(bizCode).stream().map(DiAgencyTask::getLiabilityBy).toList());
        LambdaUpdateWrapper<DiAgencyTask> queryWrapper = new LambdaUpdateWrapper<>();
        if (CollectionUtil.isNotEmpty(taskChangeResult.getRemoveUserList())) {
            queryWrapper.in(DiAgencyTask::getLiabilityBy, taskChangeResult.getRemoveUserList());
        }
        queryWrapper.eq(DiAgencyTask::getBusinessKey, bizCode);
        queryWrapper.eq(DiAgencyTask::getIsDeleted, AgencyConstants.STR_ZERO);
        queryWrapper.set(DiAgencyTask::getIsDeleted, AgencyConstants.STR_ONE);
        this.update(queryWrapper);
        return taskChangeResult;
    }

}
