package com.dyd.di.interceptor.diOrder;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dyd.common.core.utils.StringUtils;
import com.dyd.di.comment.enums.CommentStageEnum;
import com.dyd.di.interceptor.entity.DiProjectStageChange;
import com.dyd.di.interceptor.mapper.DiProjectStageChangeMapper;
import com.dyd.di.order.domain.DiOrder;
import com.dyd.di.order.enums.OrderStageEnum;
import com.dyd.di.order.mapper.DiOrderMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;

@Component
public class DiOrderListener {
    @Autowired
    private DiOrderMapper orderMapper;
    @Autowired
    private DiProjectStageChangeMapper projectStageChangeMapper;

    @Async
    @EventListener
    public void handleDataChange(DiOrderStageEvent event) {
        handleDataChange0(event);
    }

    public void handleDataChange0(DiOrderStageEvent event) {
        DiOrder order = null;
        if (StringUtils.hasText(event.getOrderNo())) {
            order =
                    orderMapper.selectOne(Wrappers.<DiOrder>lambdaQuery().eq(DiOrder::getOrderNo, event.getOrderNo()));
        }
        if (Objects.nonNull(event.getId())) {
            order =
                    orderMapper.selectOne(Wrappers.<DiOrder>lambdaQuery().eq(DiOrder::getId, event.getId()));
        }
        if (Objects.nonNull(order)) {
            DiProjectStageChange diProjectStageChange = projectStageChangeMapper.selectOne(
                    Wrappers.<DiProjectStageChange>lambdaQuery()
                            .eq(DiProjectStageChange::getRefNo, order.getProjectNo())
                            .orderByDesc(DiProjectStageChange::getCreateTime)
                            .last("limit 1"));
            String currStage = Objects.nonNull(diProjectStageChange) ? diProjectStageChange.getStage() :null;
            Integer orderStage = order.getOrderStage();
            if (OrderStageEnum.ONE.getCode().equals(orderStage) && !Objects.equals(currStage, CommentStageEnum.order.getCode())) {
                projectStageChangeMapper.insert(DiProjectStageChange.builder()
                        .stage(CommentStageEnum.order.getCode())
                        .refNo(order.getProjectNo())
                        .createTime(new Date()).build());
            } else if ((OrderStageEnum.TWO.getCode().equals(orderStage) || OrderStageEnum.THREE.getCode().equals(orderStage)) &&
                    !Objects.equals(currStage, CommentStageEnum.design.getCode())) {
                projectStageChangeMapper.insert(DiProjectStageChange.builder()
                        .stage(CommentStageEnum.design.getCode())
                        .refNo(order.getProjectNo())
                        .createTime(new Date()).build());
            } else if (OrderStageEnum.FOUR.getCode().equals(orderStage) && !Objects.equals(currStage, CommentStageEnum.purchase.getCode())) {
                projectStageChangeMapper.insert(DiProjectStageChange.builder()
                        .stage(CommentStageEnum.purchase.getCode())
                        .refNo(order.getProjectNo())
                        .createTime(new Date()).build());
            } else if (OrderStageEnum.FIVE.getCode().equals(orderStage) && !Objects.equals(currStage, CommentStageEnum.production.getCode())) {
                projectStageChangeMapper.insert(DiProjectStageChange.builder()
                        .stage(CommentStageEnum.production.getCode())
                        .refNo(order.getProjectNo())
                        .createTime(new Date()).build());
            } else if (OrderStageEnum.SIX.getCode().equals(orderStage) && !Objects.equals(currStage, CommentStageEnum.quality_inspection.getCode())) {
                projectStageChangeMapper.insert(DiProjectStageChange.builder()
                        .stage(CommentStageEnum.quality_inspection.getCode())
                        .refNo(order.getProjectNo())
                        .createTime(new Date()).build());
            } else if (OrderStageEnum.SEVEN.getCode().equals(orderStage) && !Objects.equals(currStage, CommentStageEnum.to_be_shipped.getCode())) {
                projectStageChangeMapper.insert(DiProjectStageChange.builder()
                        .stage(CommentStageEnum.to_be_shipped.getCode())
                        .refNo(order.getProjectNo())
                        .createTime(new Date()).build());
            } else if (OrderStageEnum.EIGHT.getCode().equals(orderStage) && !Objects.equals(currStage, CommentStageEnum.completed.getCode())) {
                projectStageChangeMapper.insert(DiProjectStageChange.builder()
                        .stage(CommentStageEnum.completed.getCode())
                        .refNo(order.getProjectNo())
                        .createTime(new Date()).build());
            }
        }
    }

}
