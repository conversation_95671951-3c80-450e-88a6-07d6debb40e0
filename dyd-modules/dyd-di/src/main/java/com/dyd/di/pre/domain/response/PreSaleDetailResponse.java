package com.dyd.di.pre.domain.response;

import com.dyd.common.core.annotation.Excel;
import com.dyd.di.common.DiHelper;
import com.dyd.di.material.domain.vo.DiMaterialInquiryFormVo;
import com.dyd.di.materiel.pojo.dto.MaterielChecklistNewDTO;
import com.dyd.di.materiel.pojo.dto.MaterielFileDTO;
import com.dyd.di.materiel.pojo.dto.MaterielVersionBomDTO;
import com.dyd.di.oss.IOssFile;
import com.dyd.di.pre.domain.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 售前方案详情返回
 */
@Data
public class PreSaleDetailResponse {

    /**
     * id
     */
    private Integer id;

    /**
     * 项目类型 1:项目 2:备件 3:燃配 4:出口 5:标准品 6:非标
     */
    private String orderType;

    /**
     * 产品方案编号
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long preSaleId;

    /**
     * 方案编码
     */
    private String preSaleCode;

    /**
     * 方案名称
     */
    private String preSaleName;

    /**
     * 方案类型，1：标准品；2：非标
     */
    private Integer preSaleType;

    /**
     * 商机编码
     */
    private String nicheCode;

    /**
     * 商机订单类型
     */
    private String nicheOrderType;

    /**
     * 方案状态  1:需求&验证 2:技术支持 3:商务报价 4:方案设计 5:已完成 6：已放弃 7:方案锁定
     */
    private String preSaleStatus;

    /**
     * 售中方案状态
     * INIT(1, "待开始"),
     * TWO(2,"技术支持复核"),
     * THREE(3,"待设计"),
     * FOUR(4,"备货生产中"),
     * FIVE(5,"已完成"),
     * SIX(6,"设计中"),
     */
    private Integer preSaleOrderStatus;

    /**
     * 需要套数
     */
    private Integer num;

    /**
     * 物料号
     */
    private String materialCode;

    /**
     * 物料版本ID
     */
    private String materialVersion;

    /**
     * 物料展示的版本
     */
    private String materialShowVersion;

    /**
     * 包装方式费用
     */
    private String packageType;

    /**
     * 最新方案版本
     */
    private Integer preSaleVersion;


    /**
     * 收货地址 1:统一收货地址 2:根据产品分开收货 3：公司所在地
     */
    private Integer deliveryAddressType;

    /**
     * 国家
     */
    @JsonProperty(value = "countryName")
    private String country;

    /**
     * 国家编码
     */
    private String countryCode;

    /**
     * 省
     */
    @JsonProperty("provinceName")
    private String province;

    /**
     * 省编码
     */
    private String provinceCode;

    /**
     * 市
     */
    @JsonProperty("cityName")
    private String city;

    /**
     * 市编码
     */
    private String cityCode;

    /**
     * 区
     */
    @JsonProperty("areaName")
    private String area;

    /**
     * 区编码
     */
    private String areaCode;

    /**
     * 统一收货地址
     */
    private String commonDeliveryAddress;

    /**
     * 期望交付
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate expecteDate;

    /**
     * 是否需要现场安装 0:不需要 1：需要
     */
    private Integer isInstallation;

    /**
     * 现场安装杂费
     */
    private BigDecimal installationFee;

    /**
     * 是否需要施工资质 0:不需要 1:需要
     */
    private Integer isConstructionQualification;

    /**
     * 需要资质内容
     */
    private String constructionQualificationContext;

    /**
     * 现场工作面交接表
     */
    private List<SceneUrl> sceneUrls;

    /**
     * PID图
     */
    private List<MaterielFileDTO> pidFiles;

    /**
     * 技术协议
     */
    private List<MaterielFileDTO> techFiles;

    /**
     * 机械外形图
     */
    private List<MaterielFileDTO> mechineShapeFiles;

    /**
     * 电气图
     */
    private List<MaterielFileDTO> electricFiles;

    /**
     * 机械生产图
     */
    private List<MaterielFileDTO> mechineProductFiles;

    /**
     * 三维图纸
     */
    private List<MaterielFileDTO> threeDDesignList;

    /**
     * 物料附件
     */
    private List<MaterielFileDTO> bomAnnexList;

    /**
     * 费用及各交付周期
     */
    private List<Fee> fees;

    /**
     * BOM物料清单
     */
    private List<MaterielChecklistNewDTO> materielChecklistNewDTOS;

    /**
     * 物料清单
     */
    private List<MaterielChecklistNewDTO> materielHistoryChecklistNewDTOS;

    /**
     * 物料清单
     */
    private MaterielVersionBomDTO materielVersionBomDTO;

    /**
     * 物料制作费待询价列表
     */
    private List<MaterielChecklistNewDTO> materieProductionCostlInquiryList;

    /**
     * Bom信息
     */
    private PreSaleDisposition preSaleDisposition;

    /**
     * 参数详情
     */
    private List<WebClassification> webClassification;


    /**
     * 产品方案询价单
     */
    private List<DiMaterialInquiryFormVo> diMaterialInquiryFormVos;

    /**
     * 履约任务询价
     */
    private List<FulfillmentTask> fulfillmentTaskList;

    /**
     * 非标方案
     */
    private DiPreSaleCustomized diPreSaleCustomized;

    /**
     * 非标方案-控制柜
     */
    private DiPreSaleCustomizedPlc diPreSaleCustomizedPlc;

    /**
     * 非标方案-管路
     */
    private DiPreSaleCustomizedPiping diPreSaleCustomizedPiping;

    /**
     * 非标方案-端板
     */
    private DiPreSaleCustomizedHeader diPreSaleCustomizedHeader;

    /**
     * 非标方案-助燃风机
     */
    private DiPreSaleCustomizedFan diPreSaleCustomizedFan;

    /**
     * 非标方案-燃烧器
     */
    private DiPreSaleCustomizedBurner diPreSaleCustomizedBurner;

    /**
     * 非标方案-热风卢
     */
    private DiPreSaleCustomizedAir diPreSaleCustomizedAir;


    private Boolean permission = true;
    /**
     * 选型物料列表
     */
    private List<PreSaleMaterielSelectionResponse> sectionList;

    /**
     * 行业分类
     */
    private String industryCategories;

    /**
     * 行业分类描述
     */
    private String industryCategoriesName;

    /**
     * 技术难度
     */
    private String difficultyLevel;

    /**
     * 机械难度
     */
    private String mechanicalDifficulty;

    /**
     * 电气难度
     */
    private String electricalDifficulty;

    /**
     * 生产难度
     */
    private String productionDifficulty;

    /**
     * 售后难度
     */
    private String afterSalesDifficulty;


    /**
     * 方案说明
     */
    private String preSaleDesc;

    /**
     * 产品方案售前锁定时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime preSaleLockTime;

    /**
     * 产品方案售前完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime preSaleDoneTime;

    /**
     * 产品方案技术支持复核时间（售中锁定时间）
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime preSaleTechnicalCheckTime;


    /**
     * 商机中期望交付时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date expectedDeliveryTime;

    /**
     * 产品方案放弃理由
     */
    private String preSaleWaiverReasons;

    //todo 这里要增加商机 期望技术支持交付时间

    /**
     * 客户
     */
    private String customerName;

    /**
     * 商机所属销售
     */
    private String salesName;

    /**
     * 销售所属部门
     */
    private String deptName;

    /**
     * 是否显示申请技术支持按钮
     */
    private Boolean isShowTechnical;

    /**
     * 是否可编辑技术支持负责人
     */
    private Boolean isEditTechnical;


    /**
     * 交期复核标识
     */
    private Integer deliveryReviewFlag;

    /**
     * 生产复核,0 未复核，1 无需复核，2已复核
     */
    private Integer deliveryReviewSupplyFlag;
    /**
     * 交付复核 ,0 未复核，1 无需复核，2已复核
     */
    private Integer deliveryReviewDeliveryFlag;

    /**
     * 交期技术支持复核 ,0 未复核，1 无需复核，2已复核
     */
    private Integer deliveryReviewTechSupportFlag;

    /**
     * 是否已报价
     */
    private Integer hasQuoted;

    /**
     * 标签分类
     */
    @Data
    public static class WebClassification {

        /**
         * 标签分类名称
         */
        private String classificationName;

        /**
         * 标签
         */
        private List<Label> labels;
    }

    /**
     * 标签
     */
    @Data
    public static class Label {

        /**
         * 标签id
         */
        private Integer id;

        /**
         * 参数类型  label_param_type
         */
        private String labelParamType;

        /**
         * 标签名称
         */
        private String labelName;

        /**
         * 标签参数固定
         */
        private String labelParam;


        /**
         * 标签参数上限
         */
        private BigDecimal labelParamMax;

        /**
         * 标签参数下限
         */
        private BigDecimal labelParamMin;

        /**
         * 标签单位
         */
        private String labelUnit;

        /**
         * 需求参数---固定值,大于等于，大于小于等一个值的
         */
        private BigDecimal labelParamValue;

        /**
         * 需求参数---最大值
         */
        private BigDecimal labelParamValueMax;

        /**
         * 需求参数---最小值
         */
        private BigDecimal labelParamValueMin;

        /**
         * 传入标签类型值
         */
        private String labelParamValueType;
    }


    /**
     * 费用及各交付周期
     */
    @Data
    public static class Fee extends FeeItem {

        /**
         * 方案编号
         */
        @JsonFormat(shape = JsonFormat.Shape.STRING)
        private Long preSaleId;

        /**
         * 方案版本
         */
        private String preSaleVersion;

        /**
         * 方案状态
         */
        private String preSaleStatus;
        /**
         * 交期复核 技术支持处理状态
         */
        private Integer deliveryReviewTechSupportFlag;


        /**
         * 物料号
         */
        private String materialCode;

        /**
         * 产品方案类型 1:标准品 2:非标准品
         */
        private Integer preSaleType;

        /**
         * 物料名称
         */
        private String productName;

        /**
         * 需求数量
         */
        private Integer useNum;

        /**
         * 产品规格
         */
        private String productStandard;

        /**
         * 物料版本
         */
        private String materialVersion;

        /**
         * 物料版本号对外显示
         */
        private String showMaterialVersion;



        /**
         * 是否有物料包含空价格
         */
        private Boolean hasNullMaterialFee;


        /**
         * 需要售后安装
         */
        private boolean needPostSaleInstall;



        /**
         * 方案清单版本
         */
        private Integer manifestVersion;

        /**
         * 非标方案
         */
        private DiPreSaleCustomized diPreSaleCustomized;

        /**
         * BOM物料清单
         */
        private List<MaterielChecklistNewDTO> materielChecklistNewDTOS;

        /**
         * 是否显示同步U9按钮
         */
        private Boolean isShowSyncU9;

        /**
         * 是否禁用U9同步按钮
         */
        private Boolean isDiabledSyncU9;

        private Integer manifestId;

        /**
         * 实际费用
         */
        private PreSaleRealFeeResponse realFee;

        /**
         * 报价合计
         */
        private BigDecimal quoteTotal;

        /**
         * 单套报价
         */
        private BigDecimal singleSaleQuote;
    }


    @Data
    public static class PreSaleDisposition {

        /**
         * 物料号
         */
        private String materialCode;

        /**
         * 物料名称
         */
        private String materialName;

        /**
         * 岱鼎物料分类
         */
        private String materialClassify;

        /***
         * 物料图片
         */
        private String logoPic;

        /**
         * 标准用量
         */
        private String standardQuantity;


        /**
         * 数量
         */
        private Integer num;

        /**
         * 品牌
         */
        private String brand;

        /**
         * 当前库存
         */
        private Integer stockNum;

        /**
         * 指导工期
         */
        private BigDecimal guideDuration;

        /**
         * 理论成本
         */
        private BigDecimal costFeeTotal;

        /**
         * 报价金额
         */
        private BigDecimal quotedAmount;

        /**
         * 理论成本小计
         */
        private BigDecimal costFee;

        /**
         * 设计&技术支持
         */
        private BigDecimal techSupportDesignDay;

        /**
         * 设计&技术支持费用
         */
        private BigDecimal techSupportDesignFee;

        /**
         * 物料&制作费
         */
        private BigDecimal materialProductFee;

        /**
         * 生产费用
         */
        private BigDecimal productFee;

        /**
         * 杂项小计
         */
        private BigDecimal otherFee;

        /**
         * 设计周期(天)
         */
        private Integer designDay;

        /**
         * 供应周期(天)
         */
        private BigDecimal supplyDay;

        /**
         * 生产周期(天)
         */
        private BigDecimal productDay;

        /**
         * 产品图片
         */
        private MaterielFileDTO materialUrl;

        /**
         * 长度,单位mm
         */
        private String length;

        /**
         * 宽度,单位mm
         */
        private String width;

        /**
         * 高度,单位mm
         */
        private String height;

        /**
         * 重量，单位mm
         */
        private String weight;

        /**
         * 结构树拼装
         */
        private PreSaleTree preSaleTree;

        /**
         * 组件
         */
        private List<PreSaleDisposition> darptMaterials;
    }


    @Data
    public static class PreSaleTree {
        /**
         * id
         */
        private Integer id;

        /**
         * 父id
         */
        private Integer parentId;

        /**
         * 物料号
         */
        private String materialCode;

        /**
         * 物料名称
         */
        private String materialName;

        /**
         * 子树
         */
        private List<PreSaleTree> childList;

        /**
         * 产品库模块配置集合
         */
        private List<PreSaleModule> preSaleModules;
    }


    /**
     * 产品库模块配置
     */
    @Data
    public static class PreSaleModule {

        /**
         * 模块分类 1:整套系统 2:烧嘴 3:电控 4:阀组 5:炉子
         */
        private String moduleType;

        /**
         * 组/部/零件名称
         */
        private String materialName;

        /**
         * 物料号
         */
        private String materialCode;

        /**
         * 同级序号
         */
        private Integer serialNumber;

        /**
         * 物料类型 1:主料 2:替代料
         */
        private String materialType;

        /**
         * 用量
         */
        private Integer useNum;

    }


    @Data
    public static class SceneUrl implements IOssFile {

        /**
         * id
         */
        private Integer id;

        /**
         * 文件key
         */
        private String fileKey;
        /**
         * 文件名
         */
        private String fileName;
        /**
         * 预览url
         */
        private String fileUrl;

        /**
         * 图片 1是图片  0不是
         */
        private Integer isPic;
    }

    /**
     * 履约任务询价
     */
    @Data
    public static class FulfillmentTask {
        //询价ID
        private String inquiryId;

        private String inquiryCode;
        //询价单分类
        private String inquiryType;
        @JsonFormat(shape = JsonFormat.Shape.STRING)
        private Long preSaleId;
        //产品方案ID
        private String preSaleCode;
        private String preSaleStatus;
        //方案版本
        private Integer version;
        //发起人
        private String initiator;
        //发起时间
        @JsonFormat(pattern = "yyyy-MM-dd")
        private Date initiatorTime;
        //询价单状态
        private String inquiryStatus;
        //询价单状态
        private String inquiryStatusStr;
        //售前报价
        private String preSaleQuotation;
        //售前工期预估
        private String preSaleDuration;
        //报价人
        private String quoter;
        //报价时间
        @JsonFormat(pattern = "yyyy-MM-dd")
        private Date quoterTime;
    }
}
