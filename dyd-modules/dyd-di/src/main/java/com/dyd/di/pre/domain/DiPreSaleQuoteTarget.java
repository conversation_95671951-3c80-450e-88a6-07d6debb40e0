package com.dyd.di.pre.domain;

import java.io.Serializable;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 销售目标
 * @TableName di_pre_sale_quote_target
 */
@TableName(value ="di_pre_sale_quote_target")
@Data
public class DiPreSaleQuoteTarget implements Serializable {
    /**
     *
     */
    @TableId
    private Long id;

    /**
     * bu
     */
    private String saleBu;

    /**
     * 销售工号
     */
    private String saleNo;

    /**
     * 销售
     */
    private String saleName;

    /**
     * 年销售目标
     */
    private BigDecimal yearTarget;

    /**
     * 年GP1(年度利润目标）
     */
    private BigDecimal yearGp1;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
