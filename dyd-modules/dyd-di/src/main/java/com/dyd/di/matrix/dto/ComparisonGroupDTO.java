package com.dyd.di.matrix.dto;

import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class ComparisonGroupDTO {
    /**
     * 组名
     */
    private String groupName;
    /**
     * 组id
     */
    private Long groupId;

    /**
     * 组内产品
     * 空    |产品1  | 产品2| 产品3
     * 维度1  |xx1  | xx2| xxxx
     * 维度2  |xx3  | xx4|xxxx
     * 维度x  |xx5  | xx6|xxxx
     */
    private List<List<String>> comparisonData;

}
