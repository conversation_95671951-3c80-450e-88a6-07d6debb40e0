package com.dyd.di.marketing.mapper;

import com.dyd.common.datasource.annotation.Slave;
import com.dyd.di.marketing.domain.DiMarketingDaily;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dyd.di.marketing.domain.dto.MarketingDailyDTO;
import com.dyd.di.marketing.domain.vo.BiPaymentDetailsVo;
import com.dyd.di.marketing.domain.vo.BiSalesWeekAchievementVo;
import com.dyd.di.marketing.domain.vo.BiSignOffDetailsVo;
import com.dyd.di.marketing.domain.vo.JdySalesWeekAchievement;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【di_marketing_daily(销售日报)】的数据库操作Mapper
* @createDate 2024-11-04 10:19:16
* @Entity com.dyd.di.marketing.domain.DiMarketingDaily
*/
public interface DiMarketingDailyMapper extends BaseMapper<DiMarketingDaily> {
    int countLackDailyNums(MarketingDailyDTO daily);

    List<DiMarketingDaily> selectListByParam(MarketingDailyDTO daily);

    /**
     * 销售周业绩信息
     * @param jdySalesWeekAchievement
     * @return
     */
    @Slave
    BiSalesWeekAchievementVo getBiSalesWeekAchievement(JdySalesWeekAchievement jdySalesWeekAchievement);

    /**
     * 回款明细
     * @param jdySalesWeekAchievement
     * @return
     */
    @Slave
    List<BiPaymentDetailsVo> biPaymentDetailsVoList(JdySalesWeekAchievement jdySalesWeekAchievement);

    /**
     * 签单明细
     * @param jdySalesWeekAchievement
     * @return
     */
    @Slave
    List<BiSignOffDetailsVo> biSignOffDetailsVoList(JdySalesWeekAchievement jdySalesWeekAchievement);
}




