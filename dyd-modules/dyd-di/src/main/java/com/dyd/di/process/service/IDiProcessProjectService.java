package com.dyd.di.process.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dyd.common.core.utils.IPage;
import com.dyd.common.core.utils.PageWrapper;
import com.dyd.common.core.web.page.TableDataInfo;
import com.dyd.di.api.model.ProjectProcessSynJdyResponse;
import com.dyd.di.process.domain.DiProcessProject;
import com.dyd.di.process.pojo.dto.*;
import com.dyd.di.process.pojo.vo.*;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Map;

/**
 * 项目Service接口
 *
 * <AUTHOR>
 * @date 2024-06-19
 */
public interface IDiProcessProjectService extends IService<DiProcessProject> {
    /**
     * 查询项目
     *
     * @param id 项目主键
     * @return 项目
     */
    DiProcessProject selectDiProcessProjectById(String id);

    /**
     * 查询项目列表
     *
     * @param diProcessProject 项目
     * @return 项目集合
     */
    List<DiProcessProject> selectDiProcessProjectList(DiProcessProject diProcessProject);

    /**
     * 新增项目
     *
     * @param diProcessProject 项目
     * @return 结果
     */
    int insertDiProcessProject(DiProcessProject diProcessProject);

    /**
     * 修改项目
     *
     * @param diProcessProject 项目
     * @return 结果
     */
    int updateDiProcessProject(DiProcessProject diProcessProject);

    /**
     * 批量删除项目
     *
     * @param ids 需要删除的项目主键集合
     * @return 结果
     */
    int deleteDiProcessProjectByIds(String[] ids);

    /**
     * 删除项目信息
     *
     * @param id 项目主键
     * @return 结果
     */
    int deleteDiProcessProjectById(String id);

    /**
     * 新增项目
     *
     * @param processProjectAddVo
     */
    Long addProcessProject(ProcessProjectAddVO processProjectAddVo);

    /**
     * 项目详情
     *
     * @param infoVO
     * @return
     */
    ProcessProjectInfoDTO processProjectInfo(ProcessProjectInfoVO infoVO);

    /**
     * 项目列表
     *
     * @param projectListVO
     * @return
     */
    TableDataInfo processProjectList(ProcessProjectListVO projectListVO);

    /**
     * 更新项目
     *
     * @param projectUpdateVO
     */
    void updateProcessProject(ProcessProjectUpdateVO projectUpdateVO);

    /**
     * 项目统计
     *
     * @param statisticsVO
     * @return
     */
    ProcessProjectStatisticsDTO projectStatistics(ProcessProjectStatisticsVO statisticsVO);


    /**
     * 卡片列表
     *
     * @param detailVO
     * @return
     */
    TableDataInfo cardDetailList(ProcessProjectCardDetailVO detailVO);

    /**
     * 卡片列表新
     *
     * @param detailVO
     * @return
     */
    IPage<ProcessProjectCardDetailDTO> cardDetailNewList(ProcessProjectCardDetailVO detailVO);

    /**
     * @param detailVO
     */
    ProcessProjectCardTotalDTO cardList(ProcessProjectCardDetailVO detailVO);

    /**
     * @param detailVO
     * @return
     */
    List<ProcessProjectCardNewTotalDTO> cardNewList(ProcessProjectCardDetailVO detailVO);

    /**
     * @param infoVO
     */
    void updateAcceptanceFormStatus(ProcessProjectInfoVO infoVO);

    /**
     * 卡片项目个数统计
     *
     * @param detailVO
     * @return
     */
    List<ProcessProjectCountDTO> projectCount(ProcessProjectCardDetailVO detailVO);

    /**
     * 删除卡片视图
     *
     * @param nicheNo
     */
    void updateCardFlagProcessProject(String nicheNo);

    /**
     * 删除卡片视图
     *
     * @param projectNo
     */
    void updateCardFlagProjectByProjectNo(String projectNo);

    /**
     * 更新项目
     *
     * @param diProcessProject
     */
    void updateProjectByProjectNo(DiProcessProject diProcessProject);

    /**
     * @param projectNo
     * @param customerNo
     */
    void updateProjectCustomerNo(String projectNo, String customerNo);


    PageWrapper<List<ProjectProcessSynJdyResponse>> projectProcessSynJdy(Integer pageNum, Integer pageSize);

    PageWrapper<List<ProjectProcessSynJdyResponse>> getProjects(Integer pageNum, Integer pageSize);

    IPage<LaneCardViewDTO> cardListV4(LaneListRequest detailVO);

    Map<String,ProcessProjectLaneDTO> laneListV4(LaneListRequest detailVO);

    default DiProcessProject selectByNo(String projectNo){
       var list = this.lambdaQuery().eq(DiProcessProject::getProjectNo,projectNo).list();
       if(CollectionUtils.isEmpty(list)){
           return null;
       }
       return list.get(0);
    };

    DiProcessProject getTechSupport(String nicheCode);
}
