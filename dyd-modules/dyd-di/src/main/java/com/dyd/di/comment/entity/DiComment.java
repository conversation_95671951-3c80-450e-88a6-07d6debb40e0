package com.dyd.di.comment.entity;

import com.dyd.common.security.entity.BaseEntity;
import lombok.*;
import com.baomidou.mybatisplus.annotation.*;

/**
 * 星记 DO
 */
@TableName("di_comment")
@KeySequence("di_comment_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DiComment extends BaseEntity {

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 记录当前评论所属的项目阶段，字典:comment_stage
     */
    private String stage;
    /**
     * 阶段ID
     */
    private Long stageId;
    /**
     * 标记评论类型，普通评论:comment;文件:file
     */
    private String type;
    /**
     * 项目号，在创建商机之前不生成项目，此时为空
     */
    private String projectNo;
    /**
     * 商机号，在创建商机之前为空
     */
    private String nicheNo;
    /**
     * 线索号
     */
    private String clueNo;
    /**
     * 是否为重点消息，0否，1是
     */
    private Integer importanceMark;
    /**
     * 额外信息，目前存文件信息，非文件类型为空
     */
    private String otherParams;
    /**
     * 1删除 0 正常
     */
    private Integer delFlag;

    /**
     * 评论人编号
     */
    private Long userId;

    /**
     * 评论人名称
     */
    private String userName;

    /**
     * 评论内容
     */
    private String content;

}