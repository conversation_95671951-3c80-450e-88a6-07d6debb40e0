package com.dyd.di.interceptor;

import com.baomidou.mybatisplus.core.conditions.AbstractWrapper;
import com.dyd.common.core.utils.StringUtils;

import java.util.HashMap;
import java.util.Map;

public class WrapperFieldValueExtractor {

    public static Map<String, Object> extractFieldValues(AbstractWrapper wrapper) {
        Map<String, Object> fieldValueMap = new HashMap<>();

        try {
            // 获取参数映射
            Map<String, Object> paramNameValuePairs = wrapper.getParamNameValuePairs();
            if (paramNameValuePairs == null || paramNameValuePairs.isEmpty()) {
                return fieldValueMap;
            }

            // 获取 SQL 片段
            String sqlSegment = wrapper.getSqlSegment();
            if (!StringUtils.hasText(sqlSegment)) {
                return fieldValueMap;
            }

            // 按 AND 和 OR 分割条件
            String[] conditions = sqlSegment.split("AND|OR");
            for (String condition : conditions) {
                condition = condition.trim();
                if (condition.isEmpty()) continue;
                if (condition.startsWith("("))
                    condition = condition.substring(1);
                if (condition.endsWith(")"))
                    condition = condition.substring(0, condition.length() - 1);
                // 提取字段名和值
                extractFieldAndValue(condition, paramNameValuePairs, fieldValueMap);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return fieldValueMap;
    }

    private static void extractFieldAndValue(String condition,
                                             Map<String, Object> paramNameValuePairs,
                                             Map<String, Object> fieldValueMap) {

        // 处理各种操作符
        String[] operators = {"=", "LIKE", "IN", ">=", "<=", ">", "<"};
        for (String operator : operators) {
            if (condition.contains(operator)) {
                String[] parts = condition.split(operator);
                if (parts.length == 2) {
                    String field = parts[0].trim();
                    String valueExpr = parts[1].trim();

                    // 处理 #{param} 格式的参数
                    if (valueExpr.startsWith("#{") && valueExpr.endsWith("}")) {
                        String paramName = valueExpr.substring(2, valueExpr.length() - 1);
                        paramName = paramName.replace("ew.paramNameValuePairs.", "");
                        Object value = paramNameValuePairs.get(paramName);
                        if (value != null) {
                            fieldValueMap.put(field, value);
                        }
                    }
                }
            }
        }
    }


}
