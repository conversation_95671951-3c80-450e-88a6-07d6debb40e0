package com.dyd.di.comment.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CommentMarkRespVO {
    /**
     * ID
     */
    @NotNull
    private Long id;
    /**
     * 是否为重点消息，0否，1是
     */
    private Integer importanceMark;
    /**
     * 1删除 0 正常
     */
    private Integer delFlag;

}
