package com.dyd.di.comment.vo;

import com.dyd.common.core.utils.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;
import java.util.Objects;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CommentQueryVO {

    /**
     * 唯一确定的项目， key为项目号 value为项目名
     */
    private Map<String, String> project;

    /**
     * 唯一确定的商机号
     */
    private String nicheNo;

    /**
     * 唯一确定的线索号
     */
    private String clueNo;

    /**
     * 唯一确定的客户， key客户号 value客户名
     */
    private Map<String, String> company;

    public Map<String, String> getProject() {
        if (project != null && Objects.isNull(project.get("key"))) {
            project.put("key", "");
        }
        if (project != null && Objects.isNull(project.get("value"))) {
            project.put("value", "");
        }
        return project;
    }

    public Map<String, String> getCompany() {
        if (company != null && Objects.isNull(company.get("key"))) {
            company.put("key", "");
        }
        if (company != null && Objects.isNull(company.get("value"))) {
            company.put("value", "");
        }
        return project;
    }
}
