package com.dyd.di.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dyd.common.core.domain.R;
import com.dyd.common.core.utils.PageWrapper;
import com.dyd.common.core.web.page.TableDataInfo;
import com.dyd.di.api.model.*;
import com.dyd.di.common.DiHelper;
import com.dyd.di.contract.domain.response.ContractListPageResponse;
import com.dyd.di.order.domain.DiOrder;
import com.dyd.di.order.domain.DiOrderBillPeriod;
import com.dyd.di.order.enums.OrderDingTalkEnum;
import com.dyd.di.order.pojo.*;
import com.dyd.di.order.pojo.dto.*;

import java.util.List;

/**
 * 订单Service接口
 *
 * <AUTHOR>
 * @date 2024-06-19
 */
public interface IDiOrderService extends IService<DiOrder> {
    /**
     * 查询订单
     *
     * @param id 订单主键
     * @return 订单
     */
    public DiOrder selectDiOrderById(String id);

    /**
     * 查询订单列表
     *
     * @param diOrder 订单
     * @return 订单集合
     */
    public List<DiOrder> selectDiOrderList(DiOrder diOrder);

    /**
     * 新增订单
     *
     * @param diOrder 订单
     * @return 结果
     */
    public int insertDiOrder(DiOrder diOrder);

    /**
     * 修改订单
     *
     * @param diOrder 订单
     * @return 结果
     */
    public int updateDiOrder(DiOrder diOrder);

    /**
     * 更新是否提前执行
     *
     * @param request
     */
    void updateOrderAdvanceExecution(DiOrderUpdateRequest request);

    /**
     * 批量删除订单
     *
     * @param ids 需要删除的订单主键集合
     * @return 结果
     */
    public int deleteDiOrderByIds(String[] ids);

    /**
     * 删除订单信息
     *
     * @param id 订单主键
     * @return 结果
     */
    public int deleteDiOrderById(String id);

    /**
     * 订单列表
     *
     * @param orderList
     * @return
     */
    TableDataInfo<OrderListDTO> orderList(OrderListVo orderList);

    /**
     * 订单详情
     *
     * @param orderInfoVo
     * @return
     */
    OrderInfoDTO orderInfo(OrderInfoVo orderInfoVo);

    /**
     * 添加订单
     *
     * @param orderAddDTO
     * @return
     */
    Long addOrder(OrderAddDTO orderAddDTO);

    /**
     * 根据项目编号查询订单列表
     *
     * @param projectNoVo
     * @return
     */
    TableDataInfo getOrderListByProjectNoList(OrderListByProjectNoVo projectNoVo);

    /**
     * @param orderDetailVO
     * @return
     */
    OrderDetailDTO orderDetail(OrderDetailVO orderDetailVO);

    /**
     * @param orderUpdateVO
     * @return
     */
    Long orderUpdateVO(OrderUpdateVO orderUpdateVO);

    /**
     * 合同列表
     *
     * @param orderDetailVO
     * @return
     */
    PageWrapper<List<ContractListPageResponse>> contractList(OrderDetailVO orderDetailVO);

    /**
     * 订单账期列表
     *
     * @param orderDetailVO
     * @return
     */
    List<OrderBillPeriodDTO> billPeriodList(OrderDetailVO orderDetailVO);

    /**
     * 更新订单账期
     *
     * @param orderBillPeriodUpdateVO
     * @return
     */
    Long updateBillPeriod(OrderBillPeriodUpdateVO orderBillPeriodUpdateVO);

    /**
     * 发送钉钉消息
     *
     * @param diOrder
     * @param orderDingTalkEnum
     */
    void sendDingDingMessage(DiOrder diOrder, OrderDingTalkEnum orderDingTalkEnum);

    /**
     * 更新账期实际日期
     *
     * @return
     */
    R<BillPeriodResponse> updateOrderPayRealDate(OrderBillPeriodRequest orderBillPeriodRequest);

    PageWrapper<List<DiOrderListPageResponse>> selectOrderList(Integer pageNum, Integer pageSize);

    List<DiOrderListResponse> selectOrderListByTime(DiOrderListRequest request);

    /**
     * 查询排期信息
     *
     * @param orderId
     * @return
     */
    SchedulingInformationResponse querySchedulingInformation(Long orderId);

    /**
     * 保存排期信息
     *
     * @param request
     */
    void saveSchedulingInformation(SaveSchedulingInformationRequest request);

    /**
     * 根据订单编号查询付款周期信息
     *
     * @param orderNo
     * @return
     */
    List<DiOrderBillPeriod> queryBillPeriodByOrderNo(String orderNo);

    String orderDataClean();

    /**
     * 判断订单是否可以运行
     * 设定项目经理 且 （绑款 或提前执行）
     *
     * @param order
     * @return
     */
    boolean canRun(DiOrder order);

    /**
     * 根据商机编号查询订单
     *
     * @param nicheCode
     * @return
     */
    default DiOrder queryByNicheNo(String nicheCode) {
        var list = this.lambdaQuery().eq(DiOrder::getNicheNo, nicheCode).list();
        return DiHelper.first(list);
    }

    String getSystemEngineer(DiOrder order);

    /**
     * 放弃订单
     *
     * @param request
     */
    void giveUp(GiveUpOrderRequest request,String opeatorCode);

    void sendOrderGiveUpDingDing(String orderNo, List<String> userIdList,String operator);

    default DiOrder queryByOrderNo(String orderNo) {
        return DiHelper.first(this.lambdaQuery().eq(DiOrder::getOrderNo, orderNo).list());
    }

    void createOrderTask(SaveSchedulingInformationRequest request);
}
