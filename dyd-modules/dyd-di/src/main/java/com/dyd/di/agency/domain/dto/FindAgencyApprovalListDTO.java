package com.dyd.di.agency.domain.dto;

import com.dyd.common.core.domain.BasePage;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 查询代办审批入参DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FindAgencyApprovalListDTO extends BasePage {

    /**
     * 责任人(目前是当前登陆者)
     */
    private String liabilityBy;

    /**
     * 任务分类CODE
     */
    private String taskTypeCode;

    /**
     * 0：待办  1：已处理审批
     */
    private Integer isDeleted;

}
