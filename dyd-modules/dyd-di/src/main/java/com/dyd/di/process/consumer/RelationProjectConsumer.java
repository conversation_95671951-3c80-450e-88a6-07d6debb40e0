package com.dyd.di.process.consumer;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dyd.di.comment.service.DiCommentService;
import com.dyd.di.process.domain.DiProcessProject;
import com.dyd.di.process.domain.DiProjectRelation;
import com.dyd.di.process.pojo.dto.ProcessRelationMessageDTO;
import com.dyd.di.process.service.IDiProcessProjectService;
import com.dyd.di.process.service.IDiProjectRelationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 进度消费  Tag   process  进度类    relation 关联类
 */
@Component
@RocketMQMessageListener(
        consumerGroup = "${rocketmq.project.relation.consumer.group}",
        topic = "${rocketmq.project.relation.consumer.topic}",
        selectorExpression = "${rocketmq.project.relation.consumer.subExpression}"
)
@Slf4j
@ConditionalOnProperty(prefix = "mq.consumer",name = "enable",havingValue = "true")
public class RelationProjectConsumer implements RocketMQListener<String> {


    @Autowired
    private IDiProjectRelationService projectRelationService;

    @Autowired
    private IDiProcessProjectService processProjectService;

    @Autowired
    private DiCommentService commentService;

    @Override
    public void onMessage(String message) {
        log.info("项目关联消费者-收到消息：{}", message);
        ProcessRelationMessageDTO processRelationDTO = JSONUtil.toBean(message, ProcessRelationMessageDTO.class);
        if (Objects.isNull(processRelationDTO) || StringUtils.isBlank(processRelationDTO.getRelationNo())) {
            log.error("项目关联消费者-消费异常");
            return;
        }
        if (Objects.isNull(processRelationDTO.getProjectId()) && StringUtils.isBlank(processRelationDTO.getProjectNo())) {

            log.error("项目关联消费者-项目ID和项目编号不能同时为空");
            return;
        }
        //查询项目
        DiProcessProject processProject = processProjectService.getOne(
                Wrappers.<DiProcessProject>lambdaQuery()
                        .eq(StringUtils.isNotBlank(processRelationDTO.getProjectId()), DiProcessProject::getId, processRelationDTO.getProjectId())
                        .eq(StringUtils.isNotBlank(processRelationDTO.getProjectNo()), DiProcessProject::getProjectNo, processRelationDTO.getProjectNo()));
        if (Objects.isNull(processProject)) {
            log.error("项目关联消费者-项目不存在");
            return;
        }

        DiProjectRelation projectRelation = projectRelationService.getOne(Wrappers.lambdaQuery(DiProjectRelation.class)
                .eq(DiProjectRelation::getRelationNo, processRelationDTO.getRelationNo())
                .eq(DiProjectRelation::getRelationType, processRelationDTO.getRelationType())
                .eq(DiProjectRelation::getProjectNo, processProject.getProjectNo()));
        DiProjectRelation relation = new DiProjectRelation();
        relation.setRelationNo(processRelationDTO.getRelationNo());
        relation.setRelationType(processRelationDTO.getRelationType());
        relation.setProjectNo(processProject.getProjectNo());
        relation.setDelFlag(processRelationDTO.getStatus());
        if (Objects.isNull(projectRelation)) {
            projectRelationService.save(relation);
        } else {
            relation.setId(projectRelation.getId());
            projectRelationService.updateById(relation);
        }
        commentService.tryUpdate(processRelationDTO.getProjectNo() ,processRelationDTO.getRelationNo());
    }
}
