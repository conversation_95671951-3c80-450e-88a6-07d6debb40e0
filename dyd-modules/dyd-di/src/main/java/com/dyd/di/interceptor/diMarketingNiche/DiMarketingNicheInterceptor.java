package com.dyd.di.interceptor.diMarketingNiche;

import com.baomidou.mybatisplus.core.conditions.AbstractWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.dyd.common.core.utils.AfterCommitExecutor;
import com.dyd.common.core.utils.StringUtils;
import com.dyd.di.interceptor.AbstractInterceptor;
import com.dyd.di.marketing.domain.DiMarketingNiche;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Signature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
@Intercepts({
        @Signature(
                type = Executor.class,
                method = "update",
                args = {MappedStatement.class, Object.class})
})
@Slf4j
public class DiMarketingNicheInterceptor extends AbstractInterceptor {
    @Autowired
    private ApplicationContext applicationContext;

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        MappedStatement ms = (MappedStatement) invocation.getArgs()[0];
        Object parameter = invocation.getArgs()[1];
        if (!ms.getId().contains("DiMarketingNicheMapper")) {
            return invocation.proceed();
        }
        SqlCommandType commandType = ms.getSqlCommandType();
        if (commandType != SqlCommandType.INSERT && commandType != SqlCommandType.UPDATE) {
            return invocation.proceed();
        }
        if (!(parameter instanceof DiMarketingNiche) && !(parameter instanceof UpdateWrapper) && !(parameter instanceof Map)) {
            return invocation.proceed();
        }

        Object proceed = invocation.proceed();

        try {
            if (parameter instanceof Map) {
                Map<?, ?> paramMap = (Map<?, ?>) parameter;
                if (paramMap.containsKey("et")) {
                    Object o = paramMap.get("et");
                    if (Objects.nonNull(o) && o instanceof DiMarketingNiche niche) {
                        if (StringUtils.hasText(niche.getNicheStatus())) {
                            AfterCommitExecutor.submit(() ->
                                    applicationContext.publishEvent(new DiMarketingNicheStatusEvent(this, niche.getId(), niche.getNicheNo())),
                                    null);
                        }
                    }else {
                        o = paramMap.get("ew");
                        if (Objects.nonNull(o) && o instanceof AbstractWrapper updateWrapper) {
                            // 获取 WHERE 条件字段和值
                            Map<String, Object> whereParams = extractWhereParams(updateWrapper);
                            // 获取 SET 更新字段和值
                            Map<String, Object> setParams = extractSetParams(updateWrapper);

                            String nicheStatus = (String) setParams.get("niche_status");
                            if (Objects.nonNull(nicheStatus)) {
                                String id = (String) whereParams.get("id");
                                AfterCommitExecutor.submit(() ->
                                                applicationContext.publishEvent(new DiMarketingNicheStatusEvent(this, id, null)),
                                        null);
                            }
                        }
                    }
                }
            } else if (parameter instanceof DiMarketingNiche niche) {
                if (commandType == SqlCommandType.INSERT || StringUtils.hasText(niche.getNicheStatus())) {
                    AfterCommitExecutor.submit(() ->
                                    applicationContext.publishEvent(new DiMarketingNicheStatusEvent(this, niche.getId(), niche.getNicheNo())),
                            null);
                }
            }else {
                UpdateWrapper<?> updateWrapper = (UpdateWrapper<?>) parameter;
                // 获取 WHERE 条件字段和值
                Map<String, Object> whereParams = extractWhereParams(updateWrapper);
                // 获取 SET 更新字段和值
                Map<String, Object> setParams = extractSetParams(updateWrapper);

                String nicheStatus = (String) setParams.get("niche_status");
                if (Objects.nonNull(nicheStatus)) {
                    String id = (String) whereParams.get("id");
                    AfterCommitExecutor.submit(() ->
                            applicationContext.publishEvent(new DiMarketingNicheStatusEvent(this, id, null)),
                            null);
                }
            }
        }catch (Throwable e) {
            log.error(e.getMessage(), e);
        }
        return proceed;
    }
}
