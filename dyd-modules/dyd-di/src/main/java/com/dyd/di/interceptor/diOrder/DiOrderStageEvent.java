package com.dyd.di.interceptor.diOrder;

import org.springframework.context.ApplicationEvent;

public class DiOrderStageEvent extends ApplicationEvent {

    private Long id;
    private String orderNo;

    public DiOrderStageEvent(Object source, Long id, String orderNo) {
        super(source);
        this.id = id;
        this.orderNo = orderNo;
    }

    public Long getId() {
        return id;
    }

    public String getOrderNo() {
        return orderNo;
    }
}
