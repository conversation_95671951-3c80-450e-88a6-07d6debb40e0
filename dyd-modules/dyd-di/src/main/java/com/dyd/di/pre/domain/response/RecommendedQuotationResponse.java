package com.dyd.di.pre.domain.response;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 推荐报价
 */
@Data
public class RecommendedQuotationResponse {

    /**
     * 单套基准推荐报价
     */
    private BigDecimal recommendedQuotation;

    /**
     * 单套剩余推荐报价
     */
    private BigDecimal remainingRecommendedQuotation;

    /**
     * 单方案报价额最小值
     */
    private BigDecimal minimumQuotationAmount;

    /**
     * 单方案报价额最大值
     */
    private BigDecimal maximumQuotationAmount;

}
