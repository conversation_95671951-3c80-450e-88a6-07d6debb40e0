package com.dyd.di.comment.controller;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dyd.common.core.domain.R;
import com.dyd.common.core.enums.OssSysCodeEnum;
import com.dyd.common.core.utils.DateUtils;
import com.dyd.common.core.utils.StringUtils;
import com.dyd.di.comment.entity.DiComment;
import com.dyd.di.comment.service.DiCommentService;
import com.dyd.di.comment.vo.*;
import com.dyd.di.interceptor.entity.DiProjectStageChange;
import com.dyd.di.interceptor.mapper.DiProjectStageChangeMapper;
import com.dyd.di.marketing.domain.DiMarketingClue;
import com.dyd.di.marketing.domain.DiMarketingCustomer;
import com.dyd.di.marketing.domain.DiMarketingFile;
import com.dyd.di.marketing.domain.DiMarketingNiche;
import com.dyd.di.marketing.mapper.DiMarketingCustomerMapper;
import com.dyd.di.marketing.mapper.DiMarketingNicheMapper;
import com.dyd.di.marketing.service.DiMarketingFileService;
import com.dyd.di.marketing.service.IDiMarketingClueService;
import com.dyd.di.marketing.service.IDiMarketingNicheService;
import com.dyd.di.message.domain.DiMessageList;
import com.dyd.di.message.service.IDiMessageListService;
import com.dyd.di.order.domain.DiOrder;
import com.dyd.di.order.mapper.DiOrderMapper;
import com.dyd.di.oss.FileAndBusinessVo;
import com.dyd.di.oss.OssService;
import com.dyd.di.process.domain.DiProcessProject;
import com.dyd.di.process.domain.DiProjectRelation;
import com.dyd.di.process.mapper.DiProcessProjectMapper;
import com.dyd.di.process.mapper.DiProjectRelationMapper;
import com.dyd.di.project.mapper.SysDictDataMapper;
import com.dyd.system.api.RemoteUserService;
import com.dyd.system.api.domain.SysDictData;
import com.dyd.system.api.domain.SysUser;
import com.dydtec.base.oss.api.dto.response.OssPreviewDTO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;

import javax.annotation.PostConstruct;
import javax.validation.*;
import java.util.*;
import java.util.stream.Collectors;

@Tag(name = "管理后台 - 星记")
@RestController
@RequestMapping("/di/comment")
@Validated
public class CommentController {

    @Autowired
    private DiCommentService commentService;
    @Autowired
    private IDiMarketingNicheService diMarketingNicheService;
    @Autowired
    private IDiMarketingClueService diMarketingClueService;
    @Autowired
    private DiProjectStageChangeMapper diProjectStageChangeMapper;
    @Autowired
    private RemoteUserService remoteUserService;
    @Autowired
    private DiProcessProjectMapper diProcessProjectMapper;
    @Autowired
    private DiProjectRelationMapper diProjectRelationMapper;
    @Autowired
    private DiMarketingNicheMapper diMarketingNicheMapper;
    @Autowired
    private DiMarketingCustomerMapper diMarketingCustomerMapper;
    @Autowired
    private IDiMessageListService diMessageListService;
    @Autowired
    private DiMarketingFileService diMarketingFileService;
    @Autowired
    private OssService ossService;
    @Autowired
    private SysDictDataMapper sysDictDataMapper;
    @Autowired
    private DiOrderMapper diOrderMapper;

    public static Map<String, String> stageNameMap = Maps.newHashMap();

    @PostConstruct
    public void init() {
        List<SysDictData> sysDictDataList =
                sysDictDataMapper.selectList(Wrappers.<SysDictData>lambdaQuery().eq(SysDictData::getDictType, "comment_stage"));
        sysDictDataList.forEach(sysDictData -> {
            stageNameMap.put(sysDictData.getDictValue(), sysDictData.getDictLabel());
        });
    }

    @PostMapping("/stage")
    public R stage(@RequestBody CommentRespVO createReqVO) {
        CommentStageVO commentStageVO =
                commentService.getStage(createReqVO.getProjectNo(), createReqVO.getNicheNo(), createReqVO.getClueNo());
        if (Objects.isNull(commentStageVO)) {
            return R.fail();
        }
        if ("unknown".equalsIgnoreCase(commentStageVO.getStage())) {
            commentService.createStage(createReqVO);
            commentStageVO =
                    commentService.getStage(createReqVO.getProjectNo(), createReqVO.getNicheNo(), createReqVO.getClueNo());
            if (Objects.isNull(commentStageVO)) {
                return R.fail();
            }
        }
        String stage = commentStageVO.getStage();
        if ("clue".equalsIgnoreCase(stage)) {
            DiMarketingClue clue = diMarketingClueService.getOne(Wrappers.<DiMarketingClue>lambdaQuery()
                    .eq(DiMarketingClue::getClueNo, createReqVO.getClueNo()));
            return R.ok(MapUtil.builder().put("type", "clue")
                    .put("refNo", commentStageVO.getClueNo())
                    .put("id", clue.getId())
                    .put("commentId", createReqVO.getCommentId())
                    .build());
        }else if (Lists.newArrayList("niche","plan", "quote", "contract").contains(stage)) {
            DiMarketingNiche niche = diMarketingNicheService.getOne(Wrappers.<DiMarketingNiche>lambdaQuery()
                    .eq(DiMarketingNiche::getNicheNo, createReqVO.getNicheNo()));
            return R.ok(MapUtil.builder().put("type", "niche")
                    .put("refNo", commentStageVO.getNicheNo())
                    .put("id", niche.getId())
                    .put("commentId", createReqVO.getCommentId())
                    .build());
        }else {
            DiOrder diOrder =
                    diOrderMapper.selectOne(Wrappers.<DiOrder>lambdaQuery().eq(DiOrder::getProjectNo, createReqVO.getProjectNo()));
            return R.ok(MapUtil.builder()
                    .put("type", "order")
                    .put("refNo", diOrder.getOrderNo())
                    .put("id", diOrder.getId())
                    .put("commentId", createReqVO.getCommentId())
                    .build());
        }
    }

    @PostMapping("/create")
    @Operation(summary = "创建评论或者文件")
    public R createComment(@Valid @RequestBody CommentRespVO createReqVO) {
        if (StringUtils.isEmpty(createReqVO.getProjectNo()) &&
                StringUtils.isEmpty(createReqVO.getNicheNo()) &&
                StringUtils.isEmpty(createReqVO.getClueNo())) {
            return R.fail("项目号、商机号、线索号不能同时为空");
        }
        CommentStageVO commentStageVO =
                commentService.getStage(createReqVO.getProjectNo(), createReqVO.getNicheNo(), createReqVO.getClueNo());
        if (Objects.isNull(commentStageVO)) {
            return R.fail();
        }
        if ("unknown".equalsIgnoreCase(commentStageVO.getStage())) {
            commentService.createStage(createReqVO);
            commentStageVO =
                    commentService.getStage(createReqVO.getProjectNo(), createReqVO.getNicheNo(), createReqVO.getClueNo());
            if (Objects.isNull(commentStageVO)) {
                return R.fail();
            }
        }
        DiComment diComment = new DiComment();
        BeanUtils.copyProperties(createReqVO, diComment);
        BeanUtils.copyProperties(commentStageVO, diComment);
        SysUser sysUser = remoteUserService.userInfo(createReqVO.getUserName()).getData();
        diComment.setUserName(sysUser.getNickName());
        diComment.setCreateBy(sysUser.getUserName());
        diComment.setUpdateBy(sysUser.getUserName());
        diComment.setUserId(sysUser.getUserId());
        diComment.setCreateTime(new Date());
        diComment.setUpdateTime(new Date());
        doSave(diComment, commentStageVO, sysUser);
        sendComment(createReqVO, sysUser.getNickName());
        return R.ok();
    }

    private void doSave(DiComment diComment, CommentStageVO commentStageVO, SysUser sysUser) {
        if ("file".equalsIgnoreCase(diComment.getType())) {
            DiMarketingFile diMarketingFile = new DiMarketingFile();
            diMarketingFile.setFileKey(diComment.getOtherParams());
            diMarketingFile.setDelFlag(0);
            diMarketingFile.setTraceId("1");
            diMarketingFile.setBelonging("0");
            diMarketingFile.setBelongingId(0L);
            diMarketingFile.setBelongingNo(StringUtils.hasText(commentStageVO.getProjectNo()) ?
                    commentStageVO.getProjectNo() : StringUtils.hasText(commentStageVO.getNicheNo()) ?
                    commentStageVO.getNicheNo() : commentStageVO.getClueNo());
            diMarketingFile.setCreateBy(sysUser.getUserName());
            diMarketingFile.setCreateTime(DateUtils.getNowDate());
            diMarketingFile.setUpdateBy(sysUser.getUserName());
            diMarketingFile.setUpdateTime(DateUtils.getNowDate());
            List<OssPreviewDTO> previewDTOMap =
                    ossService.getOssFileByList(OssSysCodeEnum.MARKETING.getType(), Lists.newArrayList(diComment.getOtherParams()), 0);
            if (!CollectionUtils.isEmpty(previewDTOMap)) {
                OssPreviewDTO ossPreviewDTO = previewDTOMap.get(0);
                FileAndBusinessVo fileVo = new FileAndBusinessVo();
                fileVo.setFileKey(ossPreviewDTO.getOssKey());
                fileVo.setFileName(ossPreviewDTO.getMimeName());
                fileVo.setIsPic(ossPreviewDTO.getMimeType().contains("image") ? 1 : 0);
                fileVo.setFileUrl(ossPreviewDTO.getShowUrl());
                diComment.setOtherParams(JSON.toJSONString(fileVo));
                diComment.setContent(ossPreviewDTO.getMimeName());
            }
            commentService.save(diComment);
            diMarketingFile.setCommentId(diComment.getId());
            diMarketingFileService.save(diMarketingFile);
        }else {
            commentService.save(diComment);
        }
    }

    private void sendComment(CommentRespVO createReqVO,String nickName){
        Optional.ofNullable(createReqVO.getAtUserName()).ifPresent(atUserNames -> {
            atUserNames.stream().distinct().forEach(atUserName -> {
                DiMessageList diMessageList = new DiMessageList();
                diMessageList.setBusinessModule("战略协同");
                diMessageList.setTitle(String.format("%s 给你发了一条新的评论", nickName));
                diMessageList.setSendingTime(new Date());
                diMessageList.setContent(createReqVO.getContent());
                diMessageList.setRemarks(String.format("%s @了你", nickName));
                diMessageList.setSendingUser(atUserName);
                diMessageList.setMessageUrl(createReqVO.getAtLinkUrl());
                diMessageListService.insertDiMessageList(diMessageList);
            });
        });
    }

    @PostMapping("/markOrDel")
    @Operation(summary = "标记/取消标记/删除")
    public R markOrDel(@Valid @RequestBody CommentMarkRespVO markReqVO) {
        DiComment diComment = commentService.getById(markReqVO.getId());
        if ("file".equalsIgnoreCase(diComment.getType()) && Objects.equals(markReqVO.getDelFlag(), 1)) {
            diMarketingFileService.update(Wrappers.<DiMarketingFile>lambdaUpdate()
                    .eq(DiMarketingFile::getCommentId, markReqVO.getId())
                    .set(DiMarketingFile::getDelFlag, 2));
        }
        commentService.update(Wrappers.<DiComment>lambdaUpdate()
                .set(Objects.nonNull(markReqVO.getImportanceMark()), DiComment::getImportanceMark, markReqVO.getImportanceMark())
                .set(Objects.nonNull(markReqVO.getDelFlag()), DiComment::getDelFlag, markReqVO.getDelFlag())
                .eq(DiComment::getId, markReqVO.getId()));
        return R.ok();
    }

    @PostMapping("/tryQuery")
    @Operation(summary = "查询\n点击星记按钮就触发调用一次\n把页面上对应的项目号/商机号/线索号/客户号传入对应字段查询\n" +
            "返回值中queryList 字段有值、不为空，证明目前不可以唯一确认某一个项目,需要继续筛选,筛选的字段就在queryList里\n" +
            "query字段有值、不为空，证明可以唯一确定某一个项目，页面显示评论列表")
    public R<CommentQueryResponse> tryQuery(@RequestBody CommentQueryReqVO queryReqVO) {
        if (StringUtils.isEmpty(queryReqVO.getClueNo()) &&
                StringUtils.isEmpty(queryReqVO.getProjectNo()) &&
                StringUtils.isEmpty(queryReqVO.getNicheNo()) &&
                StringUtils.isEmpty(queryReqVO.getCompanyNo())) {
            Set<String> companyNoSet = Sets.newLinkedHashSet();
            List<Map<String, String>> projectNoList = Lists.newArrayList();
            List<String> projectNos = Lists.newArrayList();
            List<DiProcessProject> diProcessProjects =
                    diProcessProjectMapper.selectList(Wrappers.<DiProcessProject>lambdaQuery().eq(DiProcessProject::getDelFlag, 0L).orderByDesc(DiProcessProject::getCreateTime));
            diProcessProjects.forEach(project -> {
                companyNoSet.add(project.getCustomerNo());
                projectNoList.add(MapUtil.<String,String>builder().put("key", project.getProjectNo()).put("value",  project.getProjectName()).build());
                projectNos.add(project.getProjectNo());
            });
            List<String> nicheNoList = diProjectRelationMapper.selectList(Wrappers.<DiProjectRelation>lambdaQuery()
                            .eq(DiProjectRelation::getRelationType, "business")
                            .in(DiProjectRelation::getProjectNo, projectNos)
                            .orderByDesc(DiProjectRelation::getCreateTime))
                    .stream()
                    .map(DiProjectRelation::getRelationNo)
                    .toList();
            List<String> clueNoList = Lists.newArrayList();
            diMarketingNicheMapper.selectList(Wrappers.<DiMarketingNiche>lambdaQuery()
                    .in(DiMarketingNiche::getNicheNo, nicheNoList)
                            .orderByDesc(DiMarketingNiche::getCreateTime))
                    .forEach(niche -> {
                        clueNoList.add(niche.getClueNo());
                        companyNoSet.add(niche.getCustomerNo());
                    });
            List<Map<String, String>> list = diMarketingCustomerMapper.selectList(Wrappers.<DiMarketingCustomer>lambdaQuery()
                            .in(DiMarketingCustomer::getCustomerNo, companyNoSet))
                    .stream()
                    .map(diMarketingCustomer -> MapUtil.<String,String>builder().put("key",diMarketingCustomer.getCustomerNo()).put("value", diMarketingCustomer.getCompanyName()).build())
                    .toList();
            CommentQueryListVO queryListVO = CommentQueryListVO.builder()
                    .companyList(list)
                    .nicheNoList(nicheNoList)
                    .projectList(projectNoList)
                    .clueNoList(clueNoList)
                    .build();
            return R.ok(CommentQueryResponse.builder().queryList(queryListVO).build());
        }
        if (StringUtils.hasText(queryReqVO.getProjectNo()) ||
                StringUtils.hasText(queryReqVO.getNicheNo()) ||
                StringUtils.hasText(queryReqVO.getClueNo())) {
            return R.ok(queryOne(queryReqVO.getProjectNo(), queryReqVO.getNicheNo(), queryReqVO.getClueNo(), queryReqVO.getImportanceMark(), queryReqVO.getContent(), queryReqVO.getNeedComment()));
        }else {
            List<DiMarketingNiche> nicheList =
                    diMarketingNicheService.selectDiMarketingNicheByNoCustomerNo(queryReqVO.getCompanyNo());
            List<DiMarketingClue> clueList = diMarketingClueService.list(Wrappers.<DiMarketingClue>lambdaQuery()
                    .eq(DiMarketingClue::getCustomerNo, queryReqVO.getCompanyNo()).orderByDesc(DiMarketingClue::getCreateTime));
            if (CollectionUtils.isEmpty(nicheList) && CollectionUtils.isEmpty(clueList)) {
                return R.ok(CommentQueryResponse.builder().commentList(Lists.newArrayList()).query(new CommentQueryVO()).build());
            } else if ((nicheList.size() + clueList.size()) == 1) {
                if (CollectionUtils.isEmpty(nicheList)) {
                    return R.ok(queryOne(null, null, clueList.get(0).getClueNo(), queryReqVO.getImportanceMark(), queryReqVO.getContent(), queryReqVO.getNeedComment()));
                }else {
                    return R.ok(queryOne(null, nicheList.get(0).getNicheNo(), nicheList.get(0).getClueNo(), queryReqVO.getImportanceMark(), queryReqVO.getContent(), queryReqVO.getNeedComment()));
                }
            }else {
                Map<String, String> projectMap = Maps.newLinkedHashMap();
                Set<String> nicheNoSet = Sets.newLinkedHashSet();
                Set<String> clueNoSet = Sets.newLinkedHashSet();
                Map<String, String> companyMap = Maps.newLinkedHashMap();
                nicheList.forEach(niche -> {
                    CommentQueryVO queryVO =
                            commentService.getCommentQuery(null, niche.getNicheNo(), niche.getClueNo());
                    if (Objects.isNull(queryVO)) {
                        return;
                    }
                    if (Objects.nonNull(queryVO.getProject())) {
                        projectMap.putAll(queryVO.getProject());
                    }
                    companyMap.putAll(queryVO.getCompany());
                    nicheNoSet.add(queryVO.getNicheNo());
                    clueNoSet.add(queryVO.getClueNo());
                });
                clueList.forEach(clue -> {
                    CommentQueryVO queryVO =
                            commentService.getCommentQuery(null, null, clue.getClueNo());
                    if (Objects.isNull(queryVO)) {
                        return;
                    }
                    if (Objects.nonNull(queryVO.getProject())) {
                        projectMap.putAll(queryVO.getProject());
                    }
                    companyMap.putAll(queryVO.getCompany());
                    nicheNoSet.add(queryVO.getNicheNo());
                    clueNoSet.add(queryVO.getClueNo());
                });

                CommentQueryListVO queryListVO = CommentQueryListVO.builder()
                        .companyList(companyMap.entrySet().stream().map(entry -> MapUtil.<String,String>builder().put("key", entry.getKey()).put("value" ,entry.getValue()).build()).toList())
                        .nicheNoList(Lists.newArrayList(nicheNoSet))
                        .projectList(projectMap.entrySet().stream().map(entry -> MapUtil.<String,String>builder().put("key", entry.getKey()).put("value" ,entry.getValue()).build()).toList())
                        .clueNoList(Lists.newArrayList(clueNoSet))
                        .build();

                return R.ok(CommentQueryResponse.builder().queryList(queryListVO).build());
            }
        }
    }

    private CommentQueryResponse queryOne(String projectNo, String nicheNo, String clueNo, Integer importanceMark, String content, Integer needComment) {
        CommentQueryVO queryVO =
                commentService.getCommentQuery(projectNo, nicheNo, clueNo);
        if (Objects.isNull(queryVO)) {
            Map<String, String> projectMap = MapUtil.<String, String>builder().put("key", projectNo).put("value", null).build();
            return CommentQueryResponse.builder()
                    .query(CommentQueryVO.builder().project(projectMap).nicheNo(nicheNo).clueNo(clueNo).build())
                    .commentList(Lists.newArrayList())
                    .build();
        }
        if (Objects.nonNull(needComment) && Objects.equals(needComment, 1)) {
            return CommentQueryResponse.builder().query(queryVO).build();
        }
        LambdaQueryWrapper<DiComment> wrapper = Wrappers.<DiComment>lambdaQuery();
        if (Objects.nonNull(queryVO.getProject())) {
            wrapper.eq(StringUtils.hasText(queryVO.getProject().get("key")), DiComment::getProjectNo, queryVO.getProject().get("key"));
        }
        Map<Long, List<DiComment>> stageMapping = commentService.list(wrapper
                        .eq(StringUtils.hasText(queryVO.getNicheNo()), DiComment::getNicheNo, queryVO.getNicheNo())
                        .eq(StringUtils.hasText(queryVO.getClueNo()), DiComment::getClueNo, queryVO.getClueNo())
                        .eq(Objects.nonNull(importanceMark), DiComment::getImportanceMark, importanceMark)
                        .eq(DiComment::getDelFlag, 0)
                        .like(StringUtils.hasText(content), DiComment::getContent, content)
                        .orderByAsc(DiComment::getCreateTime))
                .stream()
                .collect(Collectors.groupingBy(DiComment::getStageId));
        List<String> list =
                Lists.newArrayList(Objects.nonNull(queryVO.getProject()) ? queryVO.getProject().get("key") : null, queryVO.getNicheNo(), queryVO.getClueNo()).stream().filter(StringUtils::hasText).toList();
        List<DiProjectStageChange> diProjectStageChanges =
                diProjectStageChangeMapper.selectList(Wrappers.<DiProjectStageChange>lambdaQuery()
                        .in(DiProjectStageChange::getRefNo, list)
                        .orderByAsc(DiProjectStageChange::getCreateTime));
        if (CollectionUtils.isEmpty(diProjectStageChanges)) {
            commentService.createStage(CommentRespVO.builder().projectNo(projectNo).nicheNo(nicheNo).clueNo(clueNo).build());
            diProjectStageChanges =
                    diProjectStageChangeMapper.selectList(Wrappers.<DiProjectStageChange>lambdaQuery()
                            .in(DiProjectStageChange::getRefNo, list)
                            .orderByAsc(DiProjectStageChange::getCreateTime));
        }
        List<DiCommentStageView> viewList = new ArrayList<>(diProjectStageChanges.stream()
                .map(diProjectStageChange -> DiCommentStageView.builder()
                        .stageId(diProjectStageChange.getId())
                        .stage(diProjectStageChange.getStage())
                        .commentList(CollectionUtils.isEmpty(stageMapping.get(diProjectStageChange.getId())) ?
                                Lists.newArrayList() : stageMapping.get(diProjectStageChange.getId()))
                        .build())
                .toList());

        List<DiComment> unknownStageList = stageMapping.get(0L);
        if (!CollectionUtils.isEmpty(unknownStageList)) {
            viewList.add(DiCommentStageView.builder().stage("unknown").stageId(0L).commentList(unknownStageList).build());
        }
        return CommentQueryResponse.builder().query(queryVO).commentList(viewList).build();
    }

}