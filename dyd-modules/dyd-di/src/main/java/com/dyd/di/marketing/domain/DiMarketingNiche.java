package com.dyd.di.marketing.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.dyd.common.core.annotation.Excel;
import com.dyd.common.security.entity.BaseEntity;
import com.dyd.di.oss.FileVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * 商机对象 di_marketing_niche
 *
 * <AUTHOR>
 * @date 2024-06-19
 */
public class DiMarketingNiche extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    @TableId(value = "id", type = IdType.AUTO)
    private String id;

    /**
     * 商机号
     */
    @Excel(name = "商机号")
    private String nicheNo;

    /**
     * 商机阶段(niche_stage)
     */
    @Excel(name = "商机阶段(niche_stage)")
    private String nicheStage;

    /**
     * 商机状态(niche_status)
     */
    @Excel(name = "商机状态(niche_status)")
    private String nicheStatus;

    private String oldNicheStatus;

    /**
     * 商机来源(niche_source)
     */
    @Excel(name = "商机来源(niche_source)")
    private String nicheSource;

    /**
     * 其他来源
     */
    @Excel(name = "其他来源")
    private String otherSource;

    /**
     * 线索号
     */
    @Excel(name = "线索号")
    private String clueNo;

    /**
     * 客户号
     */
    @Excel(name = "客户号")
    private String customerNo;

    /**
     * 公司名称
     */
    @TableField(exist = false)
    private String companyName;

    /**
     * 询价定性（重要性）（importance）
     */
    @Excel(name = "询价定性", readConverterExp = "重=要性")
    private String importance;

    /**
     * 订单类型（order_type）
     */
    @Excel(name = "订单类型", readConverterExp = "o=rder_type")
    private String orderType;

    /**
     * 行业大类
     */
    @Excel(name = "行业大类")
    private String industryCategories;

    /**
     * 应用大类
     */
    @Excel(name = "应用大类")
    private String applicationCategories;

    /**
     * 应用小类
     */
    @Excel(name = "应用小类")
    private String applicationSubcategories;
    /**
     * 四级分类
     */
    @Excel(name = "四级分类")
    private String fourLevelClassification;

    /**
     * 行业大类其他
     */
    private String industryCategoriesOther;
    /**
     * 应用大类其他
     */
    private String applicationCategoriesOther;
    /**
     * 应用小类其他
     */
    private String applicationSubcategoriesOther;
    /**
     * 四级
     */
    private String fourLevelClassificationOther;
    /**
     * 预计交付日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date expectedDeliveryDate;
    /**
     * 预计成交金额
     */
    private BigDecimal expectedTransactionAmount;
    /**
     * 烧嘴品牌
     */
    private String burnerBrand;
    /**
     * 阀件品牌
     */
    private String valveBrand;
    /**
     * 燃料
     */
    private String fuel;
    /**
     * 现场施工
     */
    private String onSiteConstruction;
    /**
     * 供货范围
     */
    private String scopeOfSupply;
    /**
     * 技术难度->商机类型
     */
    private String technicalDifficulty;
    /**
     * 技术难度
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Integer difficultyLevel;
    /**
     * 机械难度
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Integer mechanicalDifficulty;
    /**
     * 电气难度
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Integer electricalDifficulty;
    /**
     * 生产难度
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Integer productionDifficulty;
    /**
     * 售后难度
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Integer afterSalesDifficulty;
    /**
     * 新增商机备注
     */
    private String nicheRemarks;

    /**
     * 提交方案时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date submitProposalDate;

    /**
     * 可行性验证状态：1.未验证，2.验证中，3.验证失败，4.验证成功，5.已取消
     */
    private String feasibilityVerifyState;
    /**
     * 可行性验证流程表ID
     */
    private String feasibilityVerifyProcessId;

    /**
     * 商机详情
     */
    @Excel(name = "商机详情")
    private String nicheDetails;

    /**
     * 商机备注
     */
    @Excel(name = "商机备注")
    private String remarks;

    /**
     * 归属销售
     */
    @Excel(name = "归属销售")
    private String nicheOwner;

    /**
     * 归属销售名称(用于详情回显)
     */
    @TableField(exist = false)
    private String nicheOwnerName;

    /**
     * 所属销售部门
     */
    @Excel(name = "所属销售部门")
    private String nicheOwnerDept;

    /**
     * 所属销售部门名称(用于详情回显)
     */
    @TableField(exist = false)
    private String nicheOwnerDeptName;

    /**
     * 2删除 0 正常
     */
    private String delFlag;

    /**
     * 链路id
     */
    @Excel(name = "链路id")
    private String traceId;

    /**
     * 商机附件
     */
    @Excel(name = "商机附件")
    private String nicheFile;

    /**
     * 是否立项（num_yes_no）
     */
    @Excel(name = "是否立项")
    private String projectApproved;

    /**
     * 文件列表
     */
    @TableField(exist = false)
    private List<FileVo> fileList;

    /**
     * 共享销售
     */
    private String sharedBy;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 需要售后安装
     */
    private Boolean needPostSaleInstall;

    /**
     * 需要技术支持
     */
    private Boolean needTechSupport;

    /**
     * 申请技术支持
     */
    private String applyForTechnicalSupport;

    private String createBy;

    private String updateBy;

    /**
     * 销售商机需求
     */
    @TableField(exist = false)
    private DiMarketingNicheDemand diMarketingNicheDemand;

    /**
     * 销售商机需求附件
     */
    @TableField(exist = false)
    private List<DiMarketingNicheDemandFile> diMarketingNicheDemandFileList;

    /**
     * 销售商机需求意向
     */
    @TableField(exist = false)
    private List<DiMarketingNicheDemandIntention> diMarketingNicheDemandIntentionList;

    /**
     * 火焰-配件需求
     */
    @TableField(exist = false)
    private List<DiMarketingNicheFlameAccessory> diMarketingNicheFlameAccessoryList;

    /**
     * 商机补充需求-配件需求
     */
    @TableField(exist = false)
    private List<DiMarketingNicheRequirementAccessory> diMarketingNicheRequirementAccessoryList;

    /**
     * 商机补充需求-燃烧器需求
     */
    @TableField(exist = false)
    private List<DiMarketingNicheRequirementCombustor> diMarketingNicheRequirementCombustorList;

    /**
     * 商机补充需求-电控需求
     */
    @TableField(exist = false)
    private List<DiMarketingNicheRequirementElectroll> diMarketingNicheRequirementElectrollList;

    /**
     * 商机补充需求-热风炉需求
     */
    @TableField(exist = false)
    private List<DiMarketingNicheRequirementHotstove> diMarketingNicheRequirementHotstoveList;

    /**
     * 商机补充需求-阀组需求
     */
    @TableField(exist = false)
    private List<DiMarketingNicheRequirementValve> diMarketingNicheRequirementValveList;

    /**
     * 无效理由
     */
    private String invalidReason;

    /**
     * A+商机判断原因(determine_cause)
     */
    private String determineCause;

    /**
     * 是否更新：ture可以编辑,false不允许
     */
    @TableField(exist = false)
    private Boolean isUpdateNiche;

    /**
     * 期望交期（周）
     */
    private Integer expectationDeliveryWeek;

    /**
     * 绑定的流程实例编号
     */
    private String processInstanceId;

    private String approvalStatus;

    private String approvalBys;

    private String approvalUserIdBys;

    public String getApprovalUserIdBys() {
        return approvalUserIdBys;
    }

    public void setApprovalUserIdBys(String approvalUserIdBys) {
        this.approvalUserIdBys = approvalUserIdBys;
    }

    public String getApprovalBys() {
        return approvalBys;
    }

    public void setApprovalBys(String approvalBys) {
        this.approvalBys = approvalBys;
    }

    public String getOldNicheStatus() {
        return oldNicheStatus;
    }

    public void setOldNicheStatus(String oldNicheStatus) {
        this.oldNicheStatus = oldNicheStatus;
    }

    public String getApprovalStatus() {
        return approvalStatus;
    }

    public void setApprovalStatus(String approvalStatus) {
        this.approvalStatus = approvalStatus;
    }

    public String getProcessInstanceId() {
        return processInstanceId;
    }

    public void setProcessInstanceId(String processInstanceId) {
        this.processInstanceId = processInstanceId;
    }

    public Date getSubmitProposalDate() {
        return submitProposalDate;
    }

    public void setSubmitProposalDate(Date submitProposalDate) {
        this.submitProposalDate = submitProposalDate;
    }

    public String getApplyForTechnicalSupport() {
        return applyForTechnicalSupport;
    }

    public void setApplyForTechnicalSupport(String applyForTechnicalSupport) {
        this.applyForTechnicalSupport = applyForTechnicalSupport;
    }

    public Integer getExpectationDeliveryWeek() {
        return expectationDeliveryWeek;
    }

    public void setExpectationDeliveryWeek(Integer expectationDeliveryWeek) {
        this.expectationDeliveryWeek = expectationDeliveryWeek;
    }

    public String getInvalidReason() {
        return invalidReason;
    }

    public void setInvalidReason(String invalidReason) {
        this.invalidReason = invalidReason;
    }

    public String getDetermineCause() {
        return determineCause;
    }

    public void setDetermineCause(String determineCause) {
        this.determineCause = determineCause;
    }

    public Boolean getNeedPostSaleInstall() {
        return needPostSaleInstall;
    }

    public void setNeedPostSaleInstall(Boolean needPostSaleInstall) {
        this.needPostSaleInstall = needPostSaleInstall;
    }

    public Boolean getNeedTechSupport() {
        return needTechSupport;
    }

    public void setNeedTechSupport(Boolean needTechSupport) {
        this.needTechSupport = needTechSupport;
    }

    public DiMarketingNicheDemand getDiMarketingNicheDemand() {
        return diMarketingNicheDemand;
    }

    public void setDiMarketingNicheDemand(DiMarketingNicheDemand diMarketingNicheDemand) {
        this.diMarketingNicheDemand = diMarketingNicheDemand;
    }

    public List<DiMarketingNicheDemandFile> getDiMarketingNicheDemandFileList() {
        return diMarketingNicheDemandFileList;
    }

    public void setDiMarketingNicheDemandFileList(List<DiMarketingNicheDemandFile> diMarketingNicheDemandFileList) {
        this.diMarketingNicheDemandFileList = diMarketingNicheDemandFileList;
    }

    public List<DiMarketingNicheDemandIntention> getDiMarketingNicheDemandIntentionList() {
        return diMarketingNicheDemandIntentionList;
    }

    public void setDiMarketingNicheDemandIntentionList(List<DiMarketingNicheDemandIntention> diMarketingNicheDemandIntentionList) {
        this.diMarketingNicheDemandIntentionList = diMarketingNicheDemandIntentionList;
    }

    public List<DiMarketingNicheFlameAccessory> getDiMarketingNicheFlameAccessoryList() {
        return diMarketingNicheFlameAccessoryList;
    }

    public void setDiMarketingNicheFlameAccessoryList(List<DiMarketingNicheFlameAccessory> diMarketingNicheFlameAccessoryList) {
        this.diMarketingNicheFlameAccessoryList = diMarketingNicheFlameAccessoryList;
    }

    public List<DiMarketingNicheRequirementAccessory> getDiMarketingNicheRequirementAccessoryList() {
        return diMarketingNicheRequirementAccessoryList;
    }

    public void setDiMarketingNicheRequirementAccessoryList(List<DiMarketingNicheRequirementAccessory> diMarketingNicheRequirementAccessoryList) {
        this.diMarketingNicheRequirementAccessoryList = diMarketingNicheRequirementAccessoryList;
    }

    public List<DiMarketingNicheRequirementCombustor> getDiMarketingNicheRequirementCombustorList() {
        return diMarketingNicheRequirementCombustorList;
    }

    public void setDiMarketingNicheRequirementCombustorList(List<DiMarketingNicheRequirementCombustor> diMarketingNicheRequirementCombustorList) {
        this.diMarketingNicheRequirementCombustorList = diMarketingNicheRequirementCombustorList;
    }

    public List<DiMarketingNicheRequirementElectroll> getDiMarketingNicheRequirementElectrollList() {
        return diMarketingNicheRequirementElectrollList;
    }

    public void setDiMarketingNicheRequirementElectrollList(List<DiMarketingNicheRequirementElectroll> diMarketingNicheRequirementElectrollList) {
        this.diMarketingNicheRequirementElectrollList = diMarketingNicheRequirementElectrollList;
    }

    public List<DiMarketingNicheRequirementHotstove> getDiMarketingNicheRequirementHotstoveList() {
        return diMarketingNicheRequirementHotstoveList;
    }

    public void setDiMarketingNicheRequirementHotstoveList(List<DiMarketingNicheRequirementHotstove> diMarketingNicheRequirementHotstoveList) {
        this.diMarketingNicheRequirementHotstoveList = diMarketingNicheRequirementHotstoveList;
    }

    public List<DiMarketingNicheRequirementValve> getDiMarketingNicheRequirementValveList() {
        return diMarketingNicheRequirementValveList;
    }

    public void setDiMarketingNicheRequirementValveList(List<DiMarketingNicheRequirementValve> diMarketingNicheRequirementValveList) {
        this.diMarketingNicheRequirementValveList = diMarketingNicheRequirementValveList;
    }

    public List<FileVo> getFileList() {
        return fileList;
    }

    public void setFileList(List<FileVo> fileList) {
        this.fileList = fileList;
    }

    @TableField(exist = false)
    private DiMarketingCustomer diMarketingCustomer;

    /**
     * 联系人信息
     */
    @TableField(exist = false)
    private List<DiMarketingContacts> diMarketingContactsList;

    public Integer getMechanicalDifficulty() {
        return mechanicalDifficulty;
    }

    public void setMechanicalDifficulty(Integer mechanicalDifficulty) {
        this.mechanicalDifficulty = mechanicalDifficulty;
    }

    public Integer getElectricalDifficulty() {
        return electricalDifficulty;
    }

    public void setElectricalDifficulty(Integer electricalDifficulty) {
        this.electricalDifficulty = electricalDifficulty;
    }

    public Integer getProductionDifficulty() {
        return productionDifficulty;
    }

    public void setProductionDifficulty(Integer productionDifficulty) {
        this.productionDifficulty = productionDifficulty;
    }

    public Integer getAfterSalesDifficulty() {
        return afterSalesDifficulty;
    }

    public void setAfterSalesDifficulty(Integer afterSalesDifficulty) {
        this.afterSalesDifficulty = afterSalesDifficulty;
    }

    public String getNicheRemarks() {
        return nicheRemarks;
    }

    public void setNicheRemarks(String nicheRemarks) {
        this.nicheRemarks = nicheRemarks;
    }

    public String getSharedBy() {
        return sharedBy;
    }

    public void setSharedBy(String sharedBy) {
        this.sharedBy = sharedBy;
    }

    public String getCompanyName() {
        return companyName;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public DiMarketingCustomer getDiMarketingCustomer() {
        return diMarketingCustomer;
    }

    public void setDiMarketingCustomer(DiMarketingCustomer diMarketingCustomer) {
        this.diMarketingCustomer = diMarketingCustomer;
    }

    public String getFourLevelClassification() {
        return fourLevelClassification;
    }

    public void setFourLevelClassification(String fourLevelClassification) {
        this.fourLevelClassification = fourLevelClassification;
    }

    public String getTechnicalDifficulty() {
        return technicalDifficulty;
    }

    public void setTechnicalDifficulty(String technicalDifficulty) {
        this.technicalDifficulty = technicalDifficulty;
    }

    public String getFeasibilityVerifyState() {
        return feasibilityVerifyState;
    }

    public void setFeasibilityVerifyState(String feasibilityVerifyState) {
        this.feasibilityVerifyState = feasibilityVerifyState;
    }

    public String getFeasibilityVerifyProcessId() {
        return feasibilityVerifyProcessId;
    }

    public void setFeasibilityVerifyProcessId(String feasibilityVerifyProcessId) {
        this.feasibilityVerifyProcessId = feasibilityVerifyProcessId;
    }

    public List<DiMarketingContacts> getDiMarketingContactsList() {
        return diMarketingContactsList;
    }

    public void setDiMarketingContactsList(List<DiMarketingContacts> diMarketingContactsList) {
        this.diMarketingContactsList = diMarketingContactsList;
    }

    public String getIndustryCategoriesOther() {
        return industryCategoriesOther;
    }

    public void setIndustryCategoriesOther(String industryCategoriesOther) {
        this.industryCategoriesOther = industryCategoriesOther;
    }

    public String getApplicationCategoriesOther() {
        return applicationCategoriesOther;
    }

    public void setApplicationCategoriesOther(String applicationCategoriesOther) {
        this.applicationCategoriesOther = applicationCategoriesOther;
    }

    public String getFourLevelClassificationOther() {
        return fourLevelClassificationOther;
    }

    public void setFourLevelClassificationOther(String fourLevelClassificationOther) {
        this.fourLevelClassificationOther = fourLevelClassificationOther;
    }

    public String getApplicationSubcategoriesOther() {
        return applicationSubcategoriesOther;
    }

    public void setApplicationSubcategoriesOther(String applicationSubcategoriesOther) {
        this.applicationSubcategoriesOther = applicationSubcategoriesOther;
    }

    public Date getExpectedDeliveryDate() {
        return expectedDeliveryDate;
    }

    public void setExpectedDeliveryDate(Date expectedDeliveryDate) {
        this.expectedDeliveryDate = expectedDeliveryDate;
    }

    public BigDecimal getExpectedTransactionAmount() {
        return expectedTransactionAmount;
    }

    public void setExpectedTransactionAmount(BigDecimal expectedTransactionAmount) {
        this.expectedTransactionAmount = expectedTransactionAmount;
    }

    public String getBurnerBrand() {
        return burnerBrand;
    }

    public void setBurnerBrand(String burnerBrand) {
        this.burnerBrand = burnerBrand;
    }

    public String getValveBrand() {
        return valveBrand;
    }

    public void setValveBrand(String valveBrand) {
        this.valveBrand = valveBrand;
    }

    public String getFuel() {
        return fuel;
    }

    public void setFuel(String fuel) {
        this.fuel = fuel;
    }

    public String getOnSiteConstruction() {
        return onSiteConstruction;
    }

    public void setOnSiteConstruction(String onSiteConstruction) {
        this.onSiteConstruction = onSiteConstruction;
    }

    public String getScopeOfSupply() {
        return scopeOfSupply;
    }

    public void setScopeOfSupply(String scopeOfSupply) {
        this.scopeOfSupply = scopeOfSupply;
    }

    public String getNicheFile() {
        return nicheFile;
    }

    public void setNicheFile(String nicheFile) {
        this.nicheFile = nicheFile;
    }

    public String getProjectApproved() {
        return projectApproved;
    }

    public void setProjectApproved(String projectApproved) {
        this.projectApproved = projectApproved;
    }

    public String getNicheOwnerName() {
        return nicheOwnerName;
    }

    public void setNicheOwnerName(String nicheOwnerName) {
        this.nicheOwnerName = nicheOwnerName;
    }

    public String getNicheOwnerDeptName() {
        return nicheOwnerDeptName;
    }

    public void setNicheOwnerDeptName(String nicheOwnerDeptName) {
        this.nicheOwnerDeptName = nicheOwnerDeptName;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getId() {
        return id;
    }

    public String getCustomerNo() {
        return customerNo;
    }

    public void setCustomerNo(String customerNo) {
        this.customerNo = customerNo;
    }

    public void setNicheNo(String nicheNo) {
        this.nicheNo = nicheNo;
    }

    public String getNicheNo() {
        return nicheNo;
    }

    public void setNicheStatus(String nicheStatus) {
        this.nicheStatus = nicheStatus;
    }

    public String getNicheStatus() {
        return nicheStatus;
    }

    public void setNicheStage(String nicheStage) {
        this.nicheStage = nicheStage;
    }

    public String getNicheStage() {
        return nicheStage;
    }

    public void setNicheSource(String nicheSource) {
        this.nicheSource = nicheSource;
    }

    public String getNicheSource() {
        return nicheSource;
    }

    public void setOtherSource(String otherSource) {
        this.otherSource = otherSource;
    }

    public String getOtherSource() {
        return otherSource;
    }

    public void setClueNo(String clueNo) {
        this.clueNo = clueNo;
    }

    public String getClueNo() {
        return clueNo;
    }

    public void setImportance(String importance) {
        this.importance = importance;
    }

    public String getImportance() {
        return importance;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setIndustryCategories(String industryCategories) {
        this.industryCategories = industryCategories;
    }

    public String getIndustryCategories() {
        return industryCategories;
    }

    public void setApplicationCategories(String applicationCategories) {
        this.applicationCategories = applicationCategories;
    }

    public String getApplicationCategories() {
        return applicationCategories;
    }

    public void setApplicationSubcategories(String applicationSubcategories) {
        this.applicationSubcategories = applicationSubcategories;
    }

    public String getApplicationSubcategories() {
        return applicationSubcategories;
    }

    public void setNicheDetails(String nicheDetails) {
        this.nicheDetails = nicheDetails;
    }

    public String getNicheDetails() {
        return nicheDetails;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setNicheOwner(String nicheOwner) {
        this.nicheOwner = nicheOwner;
    }

    public String getNicheOwner() {
        return nicheOwner;
    }

    public void setNicheOwnerDept(String nicheOwnerDept) {
        this.nicheOwnerDept = nicheOwnerDept;
    }

    public String getNicheOwnerDept() {
        return nicheOwnerDept;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    public String getTraceId() {
        return traceId;
    }

    public void setIsUpdateNiche(Boolean isUpdateNiche) {
        this.isUpdateNiche = isUpdateNiche;
    }

    public Boolean getIsUpdateNiche() {
        return isUpdateNiche;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("nicheNo", getNicheNo())
                .append("nicheStatus", getNicheStatus())
                .append("nicheStage", getNicheStage())
                .append("nicheSource", getNicheSource())
                .append("otherSource", getOtherSource())
                .append("clueNo", getClueNo())
                .append("importance", getImportance())
                .append("orderType", getOrderType())
                .append("industryCategories", getIndustryCategories())
                .append("applicationCategories", getApplicationCategories())
                .append("applicationSubcategories", getApplicationSubcategories())
                .append("nicheDetails", getNicheDetails())
                .append("remarks", getRemarks())
                .append("nicheOwner", getNicheOwner())
                .append("nicheOwnerDept", getNicheOwnerDept())
                .append("delFlag", getDelFlag())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("traceId", getTraceId())
                .toString();
    }

    public Integer getDifficultyLevel() {
        return difficultyLevel;
    }

    public void setDifficultyLevel(Integer difficultyLevel) {
        this.difficultyLevel = difficultyLevel;
    }
}
