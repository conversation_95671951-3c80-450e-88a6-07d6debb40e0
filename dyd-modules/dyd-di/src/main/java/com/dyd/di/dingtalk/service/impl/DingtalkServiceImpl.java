package com.dyd.di.dingtalk.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiAttendanceGetcolumnvalRequest;
import com.dingtalk.api.request.OapiAttendanceGetleavetimebynamesRequest;
import com.dingtalk.api.response.OapiAttendanceGetcolumnvalResponse;
import com.dingtalk.api.response.OapiAttendanceGetleavetimebynamesResponse;
import com.dyd.di.dingtalk.domain.DdStatisticalAttendance;
import com.dyd.di.dingtalk.service.DdStatisticalAttendanceService;
import com.dyd.di.dingtalk.service.IDingtalkService;
import com.dyd.di.home.domain.DdUser;
import com.dyd.di.home.mapper.DdUserMapper;
import com.dyd.di.marketing.domain.DiMarketingNicheDemand;
import com.dyd.di.message.service.IDiMessageListService;
import com.dyd.dingtalk.DingtalkClient;
import com.taobao.api.ApiException;
import com.taobao.api.internal.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DingtalkServiceImpl implements IDingtalkService {

    @Autowired
    private DingtalkClient dingtalkClient;

    @Autowired
    private DdUserMapper ddUserMapper;

    @Autowired
    private IDiMessageListService diMessageListService;

    @Autowired
    private DdStatisticalAttendanceService ddStatisticalAttendanceService;


    @Override
    public void syncAttendanceStatistics() {
        try {

            String dateString = null;

            //岱鼎所有员工
            List<DdUser> list = ddUserMapper.selectList(Wrappers.<DdUser>lambdaQuery()
                    .like(DdUser::getJobNumber, "dyd")
                    .eq(DdUser::getDelFlag, 0));

            //待入库数据
            List<DdStatisticalAttendance> ddStatisticalAttendanceList = new ArrayList<>();
            for (DdUser ddUser : list) {
                // 获取当前日期
                LocalDate today = LocalDate.now();
                // 获取上一个月的年月信息
                YearMonth previousMonth = YearMonth.from(today).minusMonths(1);
                // 获取上一个月的第一天和最后一天
                LocalDate startOfMonth = previousMonth.atDay(1);
                LocalDate endOfMonth = previousMonth.atEndOfMonth();
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");


                // 循环打印上一个月的所有日期
                while (!startOfMonth.isAfter(endOfMonth)) {
                    //统计日期
                    dateString = startOfMonth.format(formatter);
                    //获取统计考勤相关列值
                    Map<String, String> columnValMap = getColumnVal(ddUser.getUserId(),dateString);
                    //获取请假数据
                    Map<String, String> leaveTimeByNamesMap = getLeaveTimeByNames(ddUser.getUserId(),dateString);
                    //todo数据入库
                    DdStatisticalAttendance ddStatisticalAttendance = new DdStatisticalAttendance();
                    ddStatisticalAttendance.setUserId(ddUser.getUserId());
                    ddStatisticalAttendance.setUserNo(ddUser.getJobNumber());
                    ddStatisticalAttendance.setUserName(ddUser.getName());
                    ddStatisticalAttendance.setStatisticalDate(dateString);
                    ddStatisticalAttendance.setClockOutResult(columnValMap.get("10475444"));
                    ddStatisticalAttendance.setClockOutTime(columnValMap.get("10475443"));
                    ddStatisticalAttendance.setClockInResult(columnValMap.get("10475442"));
                    ddStatisticalAttendance.setClockInTime(columnValMap.get("10475441"));
                    ddStatisticalAttendance.setNeglectWork(columnValMap.get("10475431"));
                    ddStatisticalAttendance.setBeLateNeglectWork(columnValMap.get("10475426"));
                    ddStatisticalAttendance.setAttendanceDays(columnValMap.get("10475419"));
                    ddStatisticalAttendance.setAttendanceResults(columnValMap.get("10475439"));
                    ddStatisticalAttendance.setTravelDuration(columnValMap.get("10475432"));
                    ddStatisticalAttendance.setDelayDuration(columnValMap.get("10475423"));
                    ddStatisticalAttendance.setNumberOfLatencies(columnValMap.get("10475422"));
                    ddStatisticalAttendance.setEarlyDepartureDuration(columnValMap.get("10475428"));
                    ddStatisticalAttendance.setNumberOfEarlyDepartures(columnValMap.get("10475427"));
                    ddStatisticalAttendance.setNumberOfCardReplacements(columnValMap.get("*********"));
                    ddStatisticalAttendance.setNumberOfMissedCardsAfterWork(columnValMap.get("10475430"));
                    ddStatisticalAttendance.setNumberOfWorkCardShortages(columnValMap.get("10475429"));
                    ddStatisticalAttendance.setDurationOfOuting(columnValMap.get("10475433"));
                    ddStatisticalAttendance.setSevereLatenessDuration(columnValMap.get("10475425"));
                    ddStatisticalAttendance.setNumberOfSevereLatencies(columnValMap.get("10475424"));
                    ddStatisticalAttendance.setAttendanceDaysRequired(columnValMap.get("10475417"));
                    ddStatisticalAttendance.setAnnualLeave(leaveTimeByNamesMap.get("年假"));
                    ddStatisticalAttendance.setSickLeave(leaveTimeByNamesMap.get("病假"));
                    ddStatisticalAttendance.setLeaveOfAbsence(leaveTimeByNamesMap.get("事假"));
                    ddStatisticalAttendance.setCompensatoryLeave(leaveTimeByNamesMap.get("调休"));
                    ddStatisticalAttendance.setMarriageLeave(leaveTimeByNamesMap.get("婚假"));
                    ddStatisticalAttendance.setMaternityLeave(leaveTimeByNamesMap.get("产假"));
                    ddStatisticalAttendance.setPaternityLeave(leaveTimeByNamesMap.get("陪产假"));
                    ddStatisticalAttendance.setAccompanySickLeave(leaveTimeByNamesMap.get("陪病假"));
                    ddStatisticalAttendance.setHomeLeave(leaveTimeByNamesMap.get("探亲假"));
                    ddStatisticalAttendance.setOthersLeave(leaveTimeByNamesMap.get("其他假"));
                    ddStatisticalAttendanceList.add(ddStatisticalAttendance);
                    // 日期加一天
                    startOfMonth = startOfMonth.plusDays(1);
                }
            }
            //先清空上月数据
            ddStatisticalAttendanceService.remove(
                    Wrappers.<DdStatisticalAttendance>lambdaQuery()
                    .like(DdStatisticalAttendance::getStatisticalDate, dateString.substring(0, dateString.length() - 3))
            );
            //入库
            ddStatisticalAttendanceService.saveBatch(ddStatisticalAttendanceList);
        }catch (Exception e){
            log.info("同步钉钉考勤统计错误{}", e.getMessage());
            //发送失败信息同步到钉钉消息
            diMessageListService.errorPush("syncAttendanceStatistics", "同步钉钉考勤统计定时任务", e.getMessage(),"16625119062366702");
        }
    }

    /**
     * 下班1打卡结果	10475444
     * 下班1打卡时间	10475443
     * 上班1打卡结果	10475442
     * 上班1打卡时间	10475441
     * 旷工天数		10475431
     * 旷工迟到天数	10475426
     * 出勤天数		10475419
     * 考勤结果		10475439
     * 出差时长		10475432
     * 迟到时长		10475423
     * 迟到次数		10475422
     * 早退时长		10475428
     * 早退次数		10475427
     * 补卡次数		*********
     * 下班缺卡次数	10475430
     * 上班缺卡次数	10475429
     * 外出时长		10475433
     * 严重迟到时长	10475425
     * 严重迟到次数	10475424
     * 应出勤天数		10475417
     *
     *
     * 休息天数		10475420
     * 班次			10475440
     * 加班总时长		48176661
     * 休息日加班		10475437
     * 工作日加班		10475436
     * 节假日加班		10475438
     * 工作时长		10475421
     * 关联的审批单	10475453
     * 出勤班次		10475418
     * 休息日（转调休）48176666
     * 工作日（转调休）48176665
     * 节假日（转调休）48176667
     * 加班-审批单统计	10475435
     * 休息日（转加班费）48176663
     * 工作日（转加班费）48176662
     * 节假日（转加班费）48176664
     * 下班2打卡结果	10475448
     * 下班2打卡时间	10475447
     * 上班2打卡结果	10475446
     * 上班2打卡时间	10475445
     * 下班3打卡结果	10475452
     * 下班3打卡时间	10475451
     * 上班3打卡结果	10475450
     * 上班3打卡时间	10475449
     */
    public Map<String,String> getColumnVal(String userId,String date) throws ApiException {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/attendance/getcolumnval");
        OapiAttendanceGetcolumnvalRequest req = new OapiAttendanceGetcolumnvalRequest();
        req.setUserid(userId);
        req.setColumnIdList("10475444,10475443,10475442,10475441,10475431,10475426,10475419,10475439,10475432,10475423,10475422,10475428,10475427,*********,10475430,10475429,10475433,10475425,10475424,10475417");
        req.setFromDate(StringUtils.parseDateTime(date + " 00:00:00"));
        req.setToDate(StringUtils.parseDateTime(date + " 00:00:00"));
        OapiAttendanceGetcolumnvalResponse rsp = client.execute(req, dingtalkClient.getAccessToken());
        log.info("员工id:"+userId+";日期："+date+";请求考勤结果{}", JSON.toJSONString(rsp));
        Map<String, String> keyMap = rsp.getResult().getColumnVals().stream()
                .collect(Collectors.toMap(
                        vo -> String.valueOf(vo.getColumnVo().getId()),
                        vo -> {
                            List<OapiAttendanceGetcolumnvalResponse.ColumnDayAndVal> items = vo.getColumnVals();
                            return items.isEmpty() ? null : items.get(0).getValue();
                        },
                        (existing, replacement) -> existing
                ));
        return keyMap;
    }

    public Map<String,String> getLeaveTimeByNames(String userId,String date) throws ApiException {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/attendance/getleavetimebynames");
        OapiAttendanceGetleavetimebynamesRequest req = new OapiAttendanceGetleavetimebynamesRequest();
        req.setUserid(userId);
        req.setLeaveNames("年假,病假,事假,调休,婚假,产假,陪产假,陪病假,探亲假,其他假");
        req.setFromDate(StringUtils.parseDateTime(date + " 00:00:00"));
        req.setToDate(StringUtils.parseDateTime(date + " 00:00:00"));
        OapiAttendanceGetleavetimebynamesResponse rsp = client.execute(req, dingtalkClient.getAccessToken());
        log.info("员工id:"+userId+";日期："+date+";请求请假结果{}", JSON.toJSONString(rsp));
        Map<String, String> keyMap = rsp.getResult().getColumns().stream()
                .collect(Collectors.toMap(
                        vo -> String.valueOf(vo.getColumnvo().getName()),
                        vo -> {
                            List<OapiAttendanceGetleavetimebynamesResponse.ColumnDayAndVal> items = vo.getColumnvals();
                            return items.isEmpty() ? null : items.get(0).getValue();
                        },
                        (existing, replacement) -> existing
                ));
        return keyMap;
    }
}
