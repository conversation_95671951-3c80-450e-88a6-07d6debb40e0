package com.dyd.di.comment.vo;

import com.dyd.di.comment.enums.CommentStageEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CommentStageVO {

    /**
     * 记录当前评论所属的项目阶段，字典:comment_stage
     */
    private String stage;
    /**
     * 项目号，在创建商机之前不生成项目，此时为空
     */
    private String projectNo;
    /**
     * 商机号，在创建商机之前为空
     */
    private String nicheNo;
    /**
     * 线索号
     */
    private String clueNo;

    /**
     * 阶段ID
     */
    private Long stageId;


    public CommentStageVO(String projectNo, String nicheNo, String clueNo, CommentStageEnum stage) {
        this.stage = stage.getCode();
        this.projectNo = projectNo;
        this.nicheNo = nicheNo;
        this.clueNo = clueNo;
    }
}
