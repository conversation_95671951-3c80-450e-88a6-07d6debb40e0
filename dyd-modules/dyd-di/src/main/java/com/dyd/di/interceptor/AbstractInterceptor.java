package com.dyd.di.interceptor;

import com.baomidou.mybatisplus.core.conditions.AbstractWrapper;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.reflection.SystemMetaObject;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

public abstract class AbstractInterceptor implements Interceptor {


    /**
     * 提取 WHERE 条件的字段和值
     *
     * (id = #{ew.paramNameValuePairs.MPGENVAL2})
     */
    protected Map<String, Object> extractWhereParams(AbstractWrapper updateWrapper) {

        return WrapperFieldValueExtractor.extractFieldValues(updateWrapper);
    }

    /**
     * 提取 SET 更新的字段和值
     */
    protected Map<String, Object> extractSetParams(AbstractWrapper updateWrapper) {
        try {
            // 解析 SET 语句（例如 "name = ?, age = ?"）
            List<String> setClauses = Arrays.stream(updateWrapper.getSqlSet().split(","))
                    .map(String::trim)
                    .filter(clause -> clause.contains("="))
                    .toList();
            // 获取实际的参数值（从 entity 或 paramNameValuePairs）
            Map<String, Object> setParams = new HashMap<>();
            if (updateWrapper.getEntity() != null) {
                // 如果通过 setEntity() 设置值
                MetaObject metaObject = SystemMetaObject.forObject(updateWrapper.getEntity());
                setClauses.forEach(clause -> {
                    String[] parts = clause.split("=");
                    String column = parts[0].trim();
                    Object value = metaObject.getValue(column);
                    setParams.put(column, value);
                });
            } else {
                // 如果通过 set() 设置值（从 paramNameValuePairs 提取）
                Map<String, Object> paramNameValuePairs = updateWrapper.getParamNameValuePairs();
                AtomicInteger index = new AtomicInteger(1);
                setClauses.forEach(clause -> {
                    String[] parts = clause.split("=");
                    String column = parts[0].trim();
                    Object value = paramNameValuePairs.get("MPGENVAL" + index.getAndIncrement());
                    setParams.put(column, value);
                });
            }
            return setParams;
        } catch (Exception e) {
            return Collections.emptyMap();
        }
    }


}
