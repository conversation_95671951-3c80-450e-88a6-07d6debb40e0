package com.dyd.di.pre.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StopWatch;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 性能监控工具类
 * 用于监控售前方案查询的性能指标
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
public class PerformanceMonitor {

    private static final ConcurrentHashMap<String, AtomicLong> COUNTER_MAP = new ConcurrentHashMap<>();
    private static final ConcurrentHashMap<String, AtomicLong> TIME_MAP = new ConcurrentHashMap<>();

    /**
     * 记录方法执行时间
     *
     * @param methodName 方法名
     * @param executeTime 执行时间（毫秒）
     */
    public static void recordExecutionTime(String methodName, long executeTime) {
        COUNTER_MAP.computeIfAbsent(methodName, k -> new AtomicLong(0)).incrementAndGet();
        TIME_MAP.computeIfAbsent(methodName, k -> new AtomicLong(0)).addAndGet(executeTime);
        
        // 记录慢查询（超过1秒）
        if (executeTime > 1000) {
            log.warn("慢查询检测: 方法[{}]执行时间[{}ms]超过阈值", methodName, executeTime);
        }
    }

    /**
     * 获取方法平均执行时间
     *
     * @param methodName 方法名
     * @return 平均执行时间（毫秒）
     */
    public static double getAverageExecutionTime(String methodName) {
        AtomicLong counter = COUNTER_MAP.get(methodName);
        AtomicLong totalTime = TIME_MAP.get(methodName);
        
        if (counter == null || totalTime == null || counter.get() == 0) {
            return 0.0;
        }
        
        return (double) totalTime.get() / counter.get();
    }

    /**
     * 性能监控装饰器
     */
    public static class PerformanceWrapper {
        private final String methodName;
        private final StopWatch stopWatch;

        public PerformanceWrapper(String methodName) {
            this.methodName = methodName;
            this.stopWatch = new StopWatch(methodName);
            this.stopWatch.start();
        }

        /**
         * 结束监控并记录结果
         */
        public void finish() {
            if (stopWatch.isRunning()) {
                stopWatch.stop();
                long executeTime = stopWatch.getTotalTimeMillis();
                recordExecutionTime(methodName, executeTime);
                
                log.debug("方法[{}]执行完成，耗时[{}ms]", methodName, executeTime);
            }
        }
    }

    /**
     * 创建性能监控包装器
     *
     * @param methodName 方法名
     * @return 性能监控包装器
     */
    public static PerformanceWrapper startMonitoring(String methodName) {
        return new PerformanceWrapper(methodName);
    }

    /**
     * 监控方法执行并返回结果
     *
     * @param methodName 方法名
     * @param supplier 要执行的方法
     * @param <T> 返回类型
     * @return 方法执行结果
     */
    public static <T> T monitor(String methodName, java.util.function.Supplier<T> supplier) {
        PerformanceWrapper wrapper = startMonitoring(methodName);
        try {
            return supplier.get();
        } finally {
            wrapper.finish();
        }
    }

    /**
     * 打印性能统计信息
     */
    public static void printStatistics() {
        log.info("=== 性能统计信息 ===");
        COUNTER_MAP.forEach((methodName, counter) -> {
            double avgTime = getAverageExecutionTime(methodName);
            AtomicLong totalTime = TIME_MAP.get(methodName);
            long total = totalTime != null ? totalTime.get() : 0;
            
            log.info("方法: {} | 调用次数: {} | 总时间: {}ms | 平均时间: {:.2f}ms", 
                    methodName, counter.get(), total, avgTime);
        });
        log.info("===================");
    }

    /**
     * 清空统计信息
     */
    public static void clearStatistics() {
        COUNTER_MAP.clear();
        TIME_MAP.clear();
        log.info("性能统计信息已清空");
    }
}
