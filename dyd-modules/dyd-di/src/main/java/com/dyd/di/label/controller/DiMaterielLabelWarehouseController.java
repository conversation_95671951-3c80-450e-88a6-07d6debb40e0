package com.dyd.di.label.controller;

import com.dyd.common.core.annotation.AutoTrimString;
import com.dyd.common.core.domain.R;
import com.dyd.common.core.utils.PageWrapper;
import com.dyd.common.core.utils.poi.ExcelUtil;
import com.dyd.common.core.web.controller.BaseController;
import com.dyd.common.core.web.domain.AjaxResult;
import com.dyd.common.core.web.page.TableDataInfo;
import com.dyd.common.log.annotation.Log;
import com.dyd.common.log.enums.BusinessType;
import com.dyd.di.label.domain.DiMaterielLabelWarehouse;
import com.dyd.di.label.pojo.dto.LabelMaterielWarehouseListDTO;
import com.dyd.di.label.pojo.vo.*;
import com.dyd.di.label.service.IDiMaterielLabelWarehouseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 物料标签
 *
 * <AUTHOR>
 * @date 2024-07-17
 */
@RestController
@RequestMapping("/materiel/warehouse")
public class DiMaterielLabelWarehouseController extends BaseController {
    @Autowired
    private IDiMaterielLabelWarehouseService diMaterielLabelWarehouseService;
//
//    /**
//     * 查询物料标签列表
//     */
//    @GetMapping("/list")
//    public TableDataInfo list(DiMaterielLabelWarehouse diMaterielLabelWarehouse) {
//        startPage();
//        List<DiMaterielLabelWarehouse> list = diMaterielLabelWarehouseService.selectDiMaterielLabelWarehouseList(diMaterielLabelWarehouse);
//        return getDataTable(list);
//    }
//
//    /**
//     * 导出物料标签列表
//     */
//    @Log(title = "物料标签", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, DiMaterielLabelWarehouse diMaterielLabelWarehouse) {
//        List<DiMaterielLabelWarehouse> list = diMaterielLabelWarehouseService.selectDiMaterielLabelWarehouseList(diMaterielLabelWarehouse);
//        ExcelUtil<DiMaterielLabelWarehouse> util = new ExcelUtil<DiMaterielLabelWarehouse>(DiMaterielLabelWarehouse.class);
//        util.exportExcel(response, list, "物料标签数据");
//    }
//
//    /**
//     * 获取物料标签详细信息
//     */
//    @GetMapping(value = "/{id}")
//    public AjaxResult getInfo(@PathVariable("id") String id) {
//        return AjaxResult.success(diMaterielLabelWarehouseService.selectDiMaterielLabelWarehouseById(id));
//    }
//
//    /**
//     * 新增物料标签
//     */
//    @Log(title = "物料标签", businessType = BusinessType.INSERT)
//    @PostMapping
//    public AjaxResult add(@RequestBody DiMaterielLabelWarehouse diMaterielLabelWarehouse) {
//        return toAjax(diMaterielLabelWarehouseService.insertDiMaterielLabelWarehouse(diMaterielLabelWarehouse));
//    }
//
//    /**
//     * 修改物料标签
//     */
//    @Log(title = "物料标签", businessType = BusinessType.UPDATE)
//    @PutMapping
//    public AjaxResult edit(@RequestBody DiMaterielLabelWarehouse diMaterielLabelWarehouse) {
//        return toAjax(diMaterielLabelWarehouseService.updateDiMaterielLabelWarehouse(diMaterielLabelWarehouse));
//    }
//
//    /**
//     * 删除物料标签
//     */
//    @Log(title = "物料标签", businessType = BusinessType.DELETE)
//    @DeleteMapping("/{ids}")
//    public AjaxResult remove(@PathVariable String[] ids) {
//        return toAjax(diMaterielLabelWarehouseService.deleteDiMaterielLabelWarehouseByIds(ids));
//    }


    /**
     * 查询物料标签
     */
    @GetMapping("/materielLabelList")
    public R<PageWrapper<LabelMaterielWarehouseListDTO>> materielLabelList(LabelMaterielWarehouseListVO materielWarehouseListVO) {
        return R.ok(diMaterielLabelWarehouseService.materielLabelList(materielWarehouseListVO));
    }

    /**
     * 添加物料标签
     *
     * @param warehouseAddVO
     * @return
     */
    @AutoTrimString
    @PostMapping("/addMaterielLabel")
    public R<String> addMaterielLabel(@Valid @RequestBody LabelMaterielWarehouseAddVO warehouseAddVO) {
        return R.ok(String.valueOf(diMaterielLabelWarehouseService.addMaterielLabel(warehouseAddVO)));
    }

    /**
     * 通过标签代码查询标签物料
     *
     * @param vo
     * @return
     */
    @AutoTrimString
    @PostMapping("/getLabelMaterielLimitationByLabelCode")
    public R<Map<String,Object>> getLabelMaterielLimitationByLabelCode(@Valid @RequestBody LabelMaterielLimitationByLabelCodeVo vo) {
        return R.ok(diMaterielLabelWarehouseService.getLabelMaterielLimitationByLabelCode(vo.getLabelCode()));
    }

    /**
     * 更新物料标签
     *
     * @param updateVO
     * @return
     */
    @AutoTrimString
    @PostMapping("/updateMaterielLabel")
    public R<String> updateLabelWarehouse(@Valid @RequestBody LabelMaterielWarehouseUpdateVO updateVO) {
        return R.ok(String.valueOf(diMaterielLabelWarehouseService.updateLabelWarehouse(updateVO)));
    }

    /**
     * 删除物料标签
     *
     * @param updateVO
     * @return
     */
    @PostMapping("/batchDelMaterielLabel")
    public R<String> batchDelMaterielLabel(@Valid @RequestBody LabelMaterielWarehouseBatchUpdateVO updateVO) {
        diMaterielLabelWarehouseService.batchDelMaterielLabel(updateVO);
        return R.ok();
    }

    /**
     * 固定参数标签列表
     *
     * @param materielWarehouseListVO
     * @return
     */
    @GetMapping("/materielLabelFixParam")
    public R<PageWrapper<LabelMaterielWarehouseListDTO>> materielLabelFixParam(LabelMaterielWarehouseListVO materielWarehouseListVO) {
        return R.ok(diMaterielLabelWarehouseService.materielLabelList(materielWarehouseListVO));
    }


    /**
     * 查询物料标签
     */
    @GetMapping("/materielLabel")
    public R<PageWrapper<LabelMaterielWarehouseListDTO>> materielLabel(LabelMaterielWarehouseListVO materielWarehouseListVO) {
        return R.ok(diMaterielLabelWarehouseService.materielLabel(materielWarehouseListVO));
    }

}
