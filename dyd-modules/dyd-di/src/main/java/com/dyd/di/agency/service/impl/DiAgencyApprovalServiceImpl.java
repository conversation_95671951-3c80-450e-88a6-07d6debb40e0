package com.dyd.di.agency.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dyd.common.core.exception.ServiceException;
import com.dyd.common.core.utils.PageHelp;
import com.dyd.common.core.utils.PageWrapper;
import com.dyd.common.security.utils.SecurityUtils;
import com.dyd.di.agency.constants.AgencyConstants;
import com.dyd.di.agency.domain.dto.ChangeLiabilityByDTO;
import com.dyd.di.agency.domain.dto.CommonDTO;
import com.dyd.di.agency.domain.dto.FindAgencyApprovalListDTO;
import com.dyd.di.agency.domain.vo.AgencyApprovalListVO;
import com.dyd.di.agency.entity.DiAgencyApproval;
import com.dyd.di.agency.entity.DiAgencyChangeRecord;
import com.dyd.di.agency.entity.DiAgencyTask;
import com.dyd.di.agency.enums.ApprovalTypeEnum;
import com.dyd.di.agency.events.ApprovalTaskLiabilityByChangeEvent;
import com.dyd.di.agency.mapper.DiAgencyApprovalMapper;
import com.dyd.di.agency.mapper.DiAgencyChangeRecordMapper;
import com.dyd.di.agency.service.DiAgencyApprovalService;
import com.dyd.di.eventbus.utils.EventBusUtils;
import com.dyd.di.marketing.domain.DiAnnualContract;
import com.dyd.di.marketing.domain.DiMarketingNiche;
import com.dyd.di.marketing.service.DiAnnualContractService;
import com.dyd.di.material.service.CommonService;
import com.dyd.di.process.domain.DiProjectRelationUser;
import com.dyd.di.process.service.DiProjectRelationUserService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.json.Json;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 代办审批Service业务层处理
 */
@Service
@Slf4j
public class DiAgencyApprovalServiceImpl extends ServiceImpl<DiAgencyApprovalMapper, DiAgencyApproval> implements DiAgencyApprovalService {

    @Resource
    private DiAgencyApprovalMapper diAgencyApprovalMapper;

    @Resource
    private CommonService commonService;

    @Resource
    private DiAnnualContractService diAnnualContractService;

    @Autowired
    private DiAgencyChangeRecordMapper agencyChangeRecordMapper;

    @Autowired
    private DiProjectRelationUserService diProjectRelationUserService;

    /**
     * 查询代办审批列表(无分页)
     *
     * @param dto 查询条件
     * @return 结果
     */
    @Override
    public List<AgencyApprovalListVO> findAgencyApprovalList(FindAgencyApprovalListDTO dto) {
        //根据当前登陆者查询
        dto.setLiabilityBy(SecurityUtils.getUsername());
        List<AgencyApprovalListVO> voList = diAgencyApprovalMapper.findAgencyApprovalList(dto);
        if (CollectionUtil.isEmpty(voList)) {
            return voList;
        }
        //构建列表员工姓名
        formListName(voList);
        return voList;
    }

    /**
     * 查询代办审批列表(分页)
     *
     * @param dto 查询条件
     * @return 结果
     */
    @Override
    public PageWrapper<List<AgencyApprovalListVO>> findAgencyApprovalListPage(FindAgencyApprovalListDTO dto) {
        //根据当前登陆者查询
        dto.setLiabilityBy(SecurityUtils.getUsername());
        Page<Object> page = PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        List<AgencyApprovalListVO> voList = diAgencyApprovalMapper.findAgencyApprovalList(dto);
        if (CollectionUtil.isEmpty(voList)) {
            return PageHelp.render(page, voList);
        }
        //构建列表员工姓名
        formListName(voList);
//        //年框合同审批特殊处理
//        annualContractHandle(voList);
        //可行性验证审批特殊处理
        frHandle(voList);
        //已审批的按更新时间倒序
        if(dto.getIsDeleted() == 1){
            voList = voList.stream().sorted(Comparator.comparing(AgencyApprovalListVO::getUpdateTime).reversed()).collect(Collectors.toList());
        }
        return PageHelp.render(page, voList);
    }

    /**
     * 构建列表员工姓名
     *
     * @param voList 列表信息
     */
    private void formListName(List<AgencyApprovalListVO> voList) {
        //获取员工姓名
        Map<String, String> nameMap = commonService.getUserNameByJob(voList.stream().map(AgencyApprovalListVO::getLaunchBy).filter(StringUtils::isNotBlank).toList(),
                voList.stream().map(AgencyApprovalListVO::getLiabilityBy).filter(StringUtils::isNotBlank).toList());
        if (CollectionUtil.isEmpty(nameMap)) {
            log.info("代办审批列表查询---根据员工工号未获取到员工姓名");
            return;
        }
        voList.forEach(vo -> {
            if (nameMap.containsKey(vo.getLaunchBy())) {
                vo.setLaunchByName(nameMap.get(vo.getLaunchBy()));
            }
            if (nameMap.containsKey(vo.getLiabilityBy())) {
                vo.setLiabilityByName(nameMap.get(vo.getLiabilityBy()));
            }
        });
    }

    /**
     * 年框合同这里存的是主键ID，返回时需要返回客户号
     *
     * @param voList 列表信息
     */
    private void annualContractHandle(List<AgencyApprovalListVO> voList) {
        List<AgencyApprovalListVO> filterList = voList.stream().filter(vo -> ApprovalTypeEnum.ANNUAL_CONTRACT_APPROVAL.getTypeCode().equals(vo.getApprovalTypeCode())).toList();
        if (CollectionUtil.isEmpty(filterList)) {
            return;
        }
        List<String> annualContractIdList = filterList.stream().map(AgencyApprovalListVO::getBusinessKey).toList();
        if (CollectionUtil.isEmpty(annualContractIdList)) {
            log.error("DiAgencyApprovalServiceImpl---annualContractHandle()---数据不正确，过滤后的数据：{}", JSONUtil.toJsonStr(filterList));
            return;
        }
        List<DiAnnualContract> annualContractList = diAnnualContractService.listByIds(annualContractIdList);
        if (CollectionUtil.isEmpty(annualContractList)) {
            log.error("DiAgencyApprovalServiceImpl---annualContractHandle()---根据年框合同主键ID未获取到年框合同：主键ID：{}", JSONUtil.toJsonStr(annualContractIdList));
            return;
        }
        Map<String, DiAnnualContract> annualContractMap = annualContractList.stream().collect(Collectors.toMap(DiAnnualContract::getId, Function.identity()));
        voList.forEach(vo -> {
            if (annualContractMap.containsKey(vo.getBusinessKey())) {
                vo.setBusinessKey(annualContractMap.get(vo.getBusinessKey()).getCustomerNo());
            }
        });
    }

    /**
     * 可行性验证由于是并行流程，这里审批类型统一
     *
     * @param voList 列表信息
     */
    private void frHandle(List<AgencyApprovalListVO> voList) {
        voList.forEach(vo -> {
            if (vo.getApprovalTypeCode().equals(ApprovalTypeEnum.FR_BUSINESS_APPROVAL.getTypeCode())
                    || vo.getApprovalTypeCode().equals(ApprovalTypeEnum.FR_TECHNIQUE_APPROVAL.getTypeCode())) {
                vo.setApprovalType("可行性验证");
            }
        });
    }

    /**
     * 更新是否已读
     *
     * @param dto 更新参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateIsReadById(CommonDTO dto) {
        //获取代办审批数据
        DiAgencyApproval agencyApproval = diAgencyApprovalMapper.selectById(dto.getId());
        if (null == agencyApproval) {
            throw new ServiceException("代办审批不存在");
        }
        //将未读改为已读
        LambdaUpdateChainWrapper<DiAgencyApproval> updateWrapper = new LambdaUpdateChainWrapper<>(diAgencyApprovalMapper);
        updateWrapper.eq(DiAgencyApproval::getId, dto.getId())
                .set(DiAgencyApproval::getIsRead, AgencyConstants.STR_TWO)
                .update();
    }

    /**
     * 删除代办审批
     *
     * @param dto 删除参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteAgencyApproval(CommonDTO dto) {
        //获取代办审批数据
        DiAgencyApproval agencyApproval = diAgencyApprovalMapper.selectById(dto.getId());
        if (null == agencyApproval) {
            throw new ServiceException("代办审批不存在");
        }
        LambdaUpdateChainWrapper<DiAgencyApproval> updateWrapper = new LambdaUpdateChainWrapper<>(diAgencyApprovalMapper);
        updateWrapper.eq(DiAgencyApproval::getId, dto.getId())
                .set(DiAgencyApproval::getIsDeleted, AgencyConstants.STR_ONE)
                .update();
    }

    @Override
    public void deleteAgencyApproval(DiMarketingNiche diMarketingNiche, String status) {
        LambdaUpdateWrapper<DiAgencyApproval> eq = Wrappers.<DiAgencyApproval>lambdaUpdate()
                .set(DiAgencyApproval::getIsDeleted, "1")
                .eq(DiAgencyApproval::getBusinessKey, diMarketingNiche.getNicheNo())
                .eq(DiAgencyApproval::getApprovalTypeCode, ApprovalTypeEnum.NiCHE_LOST_AND_ABANDON_APPROVAL.getTypeCode());
        if (Objects.equals(status, "1")) {
            eq.eq(DiAgencyApproval::getLiabilityBy, SecurityUtils.getUsername());
        }
        diAgencyApprovalMapper.update(eq);
    }

    /**
     * 获取当前登录者的未读代办任务数量
     *
     * @return 数量
     */
    @Override
    public Integer getUnReadCountByLogin() {
        Long num = diAgencyApprovalMapper.selectCount(new LambdaQueryWrapper<DiAgencyApproval>()
                .eq(DiAgencyApproval::getLiabilityBy, SecurityUtils.getUsername())
                .eq(DiAgencyApproval::getIsRead, AgencyConstants.STR_ONE)
                .eq(DiAgencyApproval::getIsDeleted, AgencyConstants.STR_ZERO));
        return num.intValue();
    }

    /**
     * 变更代办任务责任人
     *
     * @param dto 变更参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String changeLiabilityBy(ChangeLiabilityByDTO dto) {
        //获取代办任务数据
        DiAgencyApproval agencyTask = this.getById(dto.getId());
        if (null == agencyTask) {
            return "代办任务不存在";
        }
        if (dto.getNewLiabilityBy().equals(agencyTask.getLiabilityBy())) {
            return "责任人相同";
        }
        //新增代办变更记录
        DiAgencyChangeRecord agencyChangeRecord = new DiAgencyChangeRecord();
        agencyChangeRecord.setAgencyType(AgencyConstants.TWO);
        agencyChangeRecord.setAgencyId(agencyTask.getId());
        agencyChangeRecord.setOldLiabilityBy(agencyTask.getLiabilityBy());
        agencyChangeRecord.setNewLiabilityBy(dto.getNewLiabilityBy());
        agencyChangeRecord.setCreateBy(SecurityUtils.getUsername());
        agencyChangeRecord.setUpdateBy(SecurityUtils.getUsername());

        List<DiAgencyApproval> newUserOldTaskList = this.lambdaQuery()
                .eq(DiAgencyApproval::getLiabilityBy, dto.getNewLiabilityBy())
                .eq(DiAgencyApproval::getApprovalTypeCode, agencyTask.getApprovalTypeCode())
                .eq(DiAgencyApproval::getBusinessKey, agencyTask.getBusinessKey())
                .eq(DiAgencyApproval::getIsDeleted, AgencyConstants.STR_ZERO)
                .list();

        if (CollectionUtil.isNotEmpty(newUserOldTaskList)) {
            //已有代办的情况下，插入转交记录，消除当前代办
            agencyChangeRecord.setRemark("用户已经有代办:" + newUserOldTaskList.get(0).getId());
            agencyChangeRecordMapper.insert(agencyChangeRecord);
            LambdaUpdateChainWrapper<DiAgencyApproval> updateWrapper = new LambdaUpdateChainWrapper<>(this.baseMapper);
            updateWrapper.eq(DiAgencyApproval::getId, dto.getId())
                    .set(DiAgencyApproval::getIsDeleted, AgencyConstants.STR_ONE)
                    .update();
            return null;
        }
        agencyChangeRecordMapper.insert(agencyChangeRecord);

        //原记录更改为已处理
        agencyTask.setIsDeleted("1");
        agencyTask.setIsRead("2");
        agencyTask.setUpdateTime(new Date());
        diAgencyApprovalMapper.updateById(agencyTask);

        //新增负责人记录
        agencyTask.setId(null);
        agencyTask.setIsDeleted("0");
        agencyTask.setLiabilityBy(dto.getNewLiabilityBy());
        agencyTask.setIsRead(AgencyConstants.STR_ONE);
        agencyTask.setCreateTime(new Date());
        agencyTask.setUpdateTime(new Date());
        diAgencyApprovalMapper.insert(agencyTask);

        //变更负责人
        /*LambdaUpdateChainWrapper<DiAgencyApproval> updateWrapper = new LambdaUpdateChainWrapper<>(this.baseMapper);
        updateWrapper.eq(DiAgencyApproval::getId, dto.getId())
                .set(DiAgencyApproval::getLiabilityBy, dto.getNewLiabilityBy())
                .set(DiAgencyApproval::getIsRead, AgencyConstants.STR_ONE)
                .update();*/
        //将变更后的负责人加入项目关联用户表中
        if (StringUtils.isNotBlank(agencyTask.getContext())) {
            JSON json = JSONUtil.parse(agencyTask.getContext());
            String projectNo = (String) json.getByPath("$.projectNo");
            if (StringUtils.isNotBlank(projectNo)) {
                diProjectRelationUserService.addProjectRelationUser(projectNo, dto.getNewLiabilityBy());

            }
        }
        //发送消息通知
        ApprovalTaskLiabilityByChangeEvent event = new ApprovalTaskLiabilityByChangeEvent(
                agencyTask.getLiabilityBy(), this.getById(dto.getId())
        );
        EventBusUtils.publishEvent(event);
        return null;
    }

}
