package com.dyd.di.interceptor.diMarketingClue;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dyd.di.comment.enums.CommentStageEnum;
import com.dyd.di.interceptor.entity.DiProjectStageChange;
import com.dyd.di.interceptor.mapper.DiProjectStageChangeMapper;
import com.dyd.di.marketing.domain.DiMarketingClue;
import com.dyd.di.marketing.mapper.DiMarketingClueMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Date;

@Component
public class DiMarketingClueCreateListener {
    @Autowired
    private DiMarketingClueMapper clueMapper;
    @Autowired
    private DiProjectStageChangeMapper projectStageChangeMapper;

    @Async
    @EventListener
    public void handleDataChange(DiMarketingClueCreateEvent event) {
        handleDataChange0(event);
    }

    public void handleDataChange0(DiMarketingClueCreateEvent event) {
        String clueNo = event.getClueNo();
        if (clueMapper.exists(Wrappers.<DiMarketingClue>lambdaQuery().eq(DiMarketingClue::getClueNo, clueNo))) {
            projectStageChangeMapper.insert(DiProjectStageChange.builder()
                    .stage(CommentStageEnum.clue.getCode())
                    .refNo(event.getClueNo())
                    .createTime(new Date())
                    .build());
        }
    }

}
