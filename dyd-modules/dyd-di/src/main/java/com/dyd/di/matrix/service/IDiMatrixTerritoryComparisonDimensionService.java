package com.dyd.di.matrix.service;

import com.dyd.di.matrix.entity.DiMatrixTerritoryComparisonDimension;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <p>
 * 业务版图对比维度 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
public interface IDiMatrixTerritoryComparisonDimensionService extends IService<DiMatrixTerritoryComparisonDimension> {

    default List<DiMatrixTerritoryComparisonDimension> queryByTerritoryId(Long territoryId, List<Long> groupIdList) {

        return this.lambdaQuery().in(DiMatrixTerritoryComparisonDimension::getGroupId, groupIdList)
                .eq(DiMatrixTerritoryComparisonDimension::getDelFlag, 0)
                .orderByAsc(DiMatrixTerritoryComparisonDimension::getWeight)
                .list();
    }

    default void removeByGroupId(List<Long> groupIds) {
        if (CollectionUtils.isEmpty(groupIds)) {
            return;
        }
        this.lambdaUpdate().in(DiMatrixTerritoryComparisonDimension::getGroupId, groupIds)
                .eq(DiMatrixTerritoryComparisonDimension::getDelFlag, 0).remove();
    }
}
