package com.dyd.di.pre.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dyd.di.contract.mapper.DydBaseMapper;
import com.dyd.di.pre.domain.response.OrderListDataResponse;
import com.dyd.di.pre.entity.DiPreSaleQuoteDetail;

import java.math.BigDecimal;
import java.util.List;

public interface DiPreSaleQuoteDetailMapper extends DydBaseMapper<DiPreSaleQuoteDetail> {

    List<OrderListDataResponse> findQuoteNoByCondition(List<String> industryCategoriesList, List<String> techSupportOwnerCodeList, List<String> preSaleQuoteCodeList);

    List<BigDecimal> getQuoteDetailByCustomerNo(String customerNo);
}
