package com.dyd.di.order.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dyd.common.core.exception.ServiceException;
import com.dyd.common.core.utils.AfterCommitExecutor;
import com.dyd.common.security.utils.SecurityUtils;
import com.dyd.di.agency.entity.DiAgencyApproval;
import com.dyd.di.agency.enums.ApprovalTypeEnum;
import com.dyd.di.agency.service.DiAgencyApprovalService;
import com.dyd.di.contract.constants.ContractConstants;
import com.dyd.di.marketing.domain.DiMarketingNiche;
import com.dyd.di.marketing.service.IDiMarketingNicheService;
import com.dyd.di.material.service.CommonService;
import com.dyd.di.order.convert.OrderConvert;
import com.dyd.di.order.domain.*;
import com.dyd.di.order.enums.OrderDingTalkEnum;
import com.dyd.di.order.mapper.*;
import com.dyd.di.order.pojo.OrderDeliveryChangeVO;
import com.dyd.di.order.pojo.dto.*;
import com.dyd.di.order.service.DiOrderDeliveryChangeService;
import com.dyd.di.order.service.OrderCommonService;
import com.dyd.di.pre.entity.DiPreSale;
import com.dyd.di.pre.entity.DiPreSaleQuote;
import com.dyd.di.pre.entity.DiPreSaleQuoteDetail;
import com.dyd.di.pre.mapper.DiPreSaleQuoteDetailMapper;
import com.dyd.di.pre.mapper.DiPreSaleQuoteMapper;
import com.dyd.di.pre.service.IDiPreSaleService;
import com.dyd.system.api.RemoteUserService;
import com.dydtec.base.camunda.api.domain.dto.ApprovalTaskDTO;
import com.dydtec.base.camunda.api.domain.dto.CheckUserPermissionDTO;
import com.dydtec.base.camunda.api.domain.dto.StartProcessDTO;
import com.dydtec.base.camunda.api.feign.IProcessClient;
import com.dydtec.base.camunda.api.feign.IProcessTaskClient;
import com.dydtec.infras.core.base.bean.BaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DiOrderDeliveryChangeServiceImpl extends ServiceImpl<DiOrderDeliveryChangeMapper, DiOrderDeliveryChange> implements DiOrderDeliveryChangeService {

    @Resource
    private DiOrderDeliveryChangeMapper diOrderDeliveryChangeMapper;

    @Resource
    private DiOrderMapper diOrderMapper;

    @Resource
    private DiOrderMachineDesignMapper diOrderMachineDesignMapper;

    @Resource
    private DiOrderElectricDesignMapper diOrderElectricDesignMapper;

    @Resource
    private DiOrderMaterielPlanMapper diOrderMaterielPlanMapper;

    @Resource
    private DiOrderProduceMapper diOrderProduceMapper;

    @Resource
    private DiOrderQualityMapper diOrderQualityMapper;

    @Resource
    private DiPreSaleQuoteMapper diPreSaleQuoteMapper;

    @Resource
    private DiPreSaleQuoteDetailMapper diPreSaleQuoteDetailMapper;

    @Resource
    private DiOrderWarningMapper diOrderWarningMapper;

    @Resource
    private DiAgencyApprovalService diAgencyApprovalService;

    @Resource
    private IDiPreSaleService diPreSaleService;

    @Resource
    private IProcessClient processClient;

    @Resource
    private RemoteUserService remoteUserService;

    @Resource
    private IProcessTaskClient processTaskClient;

    @Resource
    private CommonService commonService;

    @Resource
    private OrderCommonService orderCommonService;

    @Value("${camunda.deliver.manage.role.id}")
    private String deliverManageRoleId;

    @Autowired
    private IDiMarketingNicheService nicheService;

    @Override
    public List<OrderProductPlanDetailDTO> findOrderProductPlanList(DeliveryChangeApprovalProcessDTO dto) {
        DiOrder diOrder = diOrderMapper.selectById(dto.getOrderId());
        if (null == diOrder) {
            throw new ServiceException("订单不存在");
        }
        if (StringUtils.isBlank(diOrder.getPreSaleQuoteNo())) {
            throw new ServiceException("订单未绑定销售报价单");
        }
        //获取销售报价单数据
        DiPreSaleQuote preSaleQuote = diPreSaleQuoteMapper.selectOne(new LambdaQueryWrapper<DiPreSaleQuote>().eq(DiPreSaleQuote::getPreSaleQuoteCode, diOrder.getPreSaleQuoteNo()));
        if (null == preSaleQuote) {
            throw new ServiceException("销售报价单不存在");
        }
        //查询销售报价单明细数据
        List<DiPreSaleQuoteDetail> preSaleQuoteDetailList = diPreSaleQuoteDetailMapper.selectList(new LambdaQueryWrapper<DiPreSaleQuoteDetail>().eq(DiPreSaleQuoteDetail::getPreSaleQuoteId, preSaleQuote.getId()));
        if (CollectionUtils.isEmpty(preSaleQuoteDetailList)) {
            throw new ServiceException("销售报价单数据不正确");
        }
        //查询报价单明细绑定的产品方案数据
        List<Long> preSaleIdsList = preSaleQuoteDetailList.stream().map(DiPreSaleQuoteDetail::getPreSaleId).distinct().toList();
        List<DiPreSale> preSaleList = diPreSaleService.queryDiPreSaleByIds(preSaleIdsList);
        if (CollectionUtils.isEmpty(preSaleList) || preSaleIdsList.size() != preSaleList.size()) {
            throw new ServiceException("销售报价单对应的产品方案数据不正确");
        }
        return structurePlanData(diOrder.getOrderNo(), preSaleList);
    }

    private List<OrderProductPlanDetailDTO> structurePlanData(String orderNo, List<DiPreSale> preSaleList) {
        List<Long> preSaleIdList = preSaleList.stream().map(DiPreSale::getId).toList();
        //获取机械设计任务数据
        List<DiOrderMachineDesign> orderMachineDesignList = diOrderMachineDesignMapper.selectList(Wrappers.lambdaQuery(DiOrderMachineDesign.class)
                .eq(DiOrderMachineDesign::getOrderNo, orderNo)
                .eq(DiOrderMachineDesign::getDelFlag, 0)
                .in(DiOrderMachineDesign::getPreSaleId, preSaleIdList));
        Map<String, List<DiOrderMachineDesign>> orderMachineDesignMap = orderMachineDesignList.stream().collect(Collectors.groupingBy(DiOrderMachineDesign::getPreSaleCode));
        //获取电气设计任务数据
        List<DiOrderElectricDesign> orderElectricDesignList = diOrderElectricDesignMapper.selectList(Wrappers.lambdaQuery(DiOrderElectricDesign.class)
                .eq(DiOrderElectricDesign::getOrderNo, orderNo)
                .eq(DiOrderElectricDesign::getDelFlag, 0)
                .in(DiOrderElectricDesign::getPreSaleId, preSaleIdList));
        Map<String, List<DiOrderElectricDesign>> orderElectricDesignMap = orderElectricDesignList.stream().collect(Collectors.groupingBy(DiOrderElectricDesign::getPreSaleCode));
        //获取物料计划任务数据
        List<DiOrderMaterielPlan> diOrderMaterielPlanList = diOrderMaterielPlanMapper.selectList(Wrappers.lambdaQuery(DiOrderMaterielPlan.class)
                .eq(DiOrderMaterielPlan::getOrderNo, orderNo)
                .in(DiOrderMaterielPlan::getPreSaleId, preSaleIdList)
                .eq(DiOrderMaterielPlan::getDelFlag, 0));
        Map<String, List<DiOrderMaterielPlan>> orderMaterielPlanMap = diOrderMaterielPlanList.stream().collect(Collectors.groupingBy(DiOrderMaterielPlan::getPreSaleCode));
        //获取生产计划任务数据
        List<DiOrderProduce> diOrderProduceList = diOrderProduceMapper.selectList(Wrappers.lambdaQuery(DiOrderProduce.class)
                .eq(DiOrderProduce::getOrderNo, orderNo)
                .eq(DiOrderProduce::getDelFlag, 0)
                .in(DiOrderProduce::getPreSaleId, preSaleIdList));
        Map<String, List<DiOrderProduce>> orderProduceMap = diOrderProduceList.stream().collect(Collectors.groupingBy(DiOrderProduce::getPreSaleCode));
        //获取生产质检任务数据
        List<DiOrderQuality> diOrderQualityList = diOrderQualityMapper.selectList(Wrappers.lambdaQuery(DiOrderQuality.class)
                .eq(DiOrderQuality::getOrderNo, orderNo)
                .eq(DiOrderQuality::getDelFlag, 0)
                .in(DiOrderQuality::getPreSaleId, preSaleIdList));
        Map<String, List<DiOrderQuality>> orderQualityMap = diOrderQualityList.stream().collect(Collectors.groupingBy(DiOrderQuality::getPreSaleCode));
        //构建返回数据
        List<OrderProductPlanDetailDTO> list = new ArrayList<>();
        preSaleList.forEach(preSale -> {
            OrderProductPlanDetailDTO orderProductPlan = new OrderProductPlanDetailDTO();
            orderProductPlan.setPreSaleId(preSale.getId());
            orderProductPlan.setPreSaleCode(preSale.getPreSaleCode());
            orderProductPlan.setPreSaleName(preSale.getPreSaleName());
            //机械设计数据
            Optional.ofNullable(orderMachineDesignMap.get(preSale.getPreSaleCode())).flatMap(diOrderMachineDesigns -> diOrderMachineDesigns.stream().findFirst()).ifPresent(diOrderMachineDesign -> {
                OrderPlanDetailInfoDTO orderPlanDetailInfoDTO = new OrderPlanDetailInfoDTO();
                orderPlanDetailInfoDTO.setId(diOrderMachineDesign.getId());
                orderPlanDetailInfoDTO.setOrderPlanType("machineDesign");
                orderPlanDetailInfoDTO.setOrderPlanTypeName("机械设计");
                orderPlanDetailInfoDTO.setInstallStartTime(diOrderMachineDesign.getExpectStartTime());
                orderPlanDetailInfoDTO.setInstallEndTime(diOrderMachineDesign.getExpectEndTime());
                orderPlanDetailInfoDTO.setEnforceUserId(diOrderMachineDesign.getMachineDesignUserId());
                orderProductPlan.getOrderPlanDTOList().add(orderPlanDetailInfoDTO);
            });
            //电气设计数据
            Optional.ofNullable(orderElectricDesignMap.get(preSale.getPreSaleCode())).flatMap(diOrderElectricDesigns -> diOrderElectricDesigns.stream().findFirst()).ifPresent(diOrderElectricDesign -> {
                OrderPlanDetailInfoDTO orderPlanDetailInfoDTO = new OrderPlanDetailInfoDTO();
                orderPlanDetailInfoDTO.setId(diOrderElectricDesign.getId());
                orderPlanDetailInfoDTO.setOrderPlanType("electricDesign");
                orderPlanDetailInfoDTO.setOrderPlanTypeName("电气设计");
                orderPlanDetailInfoDTO.setInstallStartTime(diOrderElectricDesign.getExpectStartTime());
                orderPlanDetailInfoDTO.setInstallEndTime(diOrderElectricDesign.getExpectEndTime());
                orderPlanDetailInfoDTO.setEnforceUserId(diOrderElectricDesign.getElectricDesignUserId());
                orderProductPlan.getOrderPlanDTOList().add(orderPlanDetailInfoDTO);
            });
            //物料计划数据
            Optional.ofNullable(orderMaterielPlanMap.get(preSale.getPreSaleCode())).flatMap(diOrderMaterielPlans -> diOrderMaterielPlans.stream().findFirst()).ifPresent(diOrderMaterielPlan -> {
                OrderPlanDetailInfoDTO orderPlanDetailInfoDTO = new OrderPlanDetailInfoDTO();
                orderPlanDetailInfoDTO.setId(diOrderMaterielPlan.getId());
                orderPlanDetailInfoDTO.setOrderPlanType("materielPlan");
                orderPlanDetailInfoDTO.setOrderPlanTypeName("物料计划");
                orderPlanDetailInfoDTO.setInstallStartTime(diOrderMaterielPlan.getExpectStartTime());
                orderPlanDetailInfoDTO.setInstallEndTime(diOrderMaterielPlan.getExpectEndTime());
                orderPlanDetailInfoDTO.setEnforceUserId(diOrderMaterielPlan.getPurchaseUserId());
                orderProductPlan.getOrderPlanDTOList().add(orderPlanDetailInfoDTO);
            });
            //生产计划数据
            Optional.ofNullable(orderProduceMap.get(preSale.getPreSaleCode())).flatMap(diOrderProduces -> diOrderProduces.stream().findFirst()).ifPresent(diOrderProduce -> {
                OrderPlanDetailInfoDTO orderPlanDetailInfoDTO = new OrderPlanDetailInfoDTO();
                orderPlanDetailInfoDTO.setId(diOrderProduce.getId());
                orderPlanDetailInfoDTO.setOrderPlanType("produce");
                orderPlanDetailInfoDTO.setOrderPlanTypeName("生产任务");
                orderPlanDetailInfoDTO.setInstallStartTime(diOrderProduce.getProduceStartTime());
                orderPlanDetailInfoDTO.setInstallEndTime(diOrderProduce.getProduceEndTime());
                orderPlanDetailInfoDTO.setEnforceUserId(diOrderProduce.getProduceUserId());
                orderProductPlan.getOrderPlanDTOList().add(orderPlanDetailInfoDTO);
            });
            //生产质检数据
            Optional.ofNullable(orderQualityMap.get(preSale.getPreSaleCode())).flatMap(diOrderQualities -> diOrderQualities.stream().findFirst()).ifPresent(diOrderQuality -> {
                OrderPlanDetailInfoDTO orderPlanDetailInfoDTO = new OrderPlanDetailInfoDTO();
                orderPlanDetailInfoDTO.setId(diOrderQuality.getId());
                orderPlanDetailInfoDTO.setOrderPlanType("quality");
                orderPlanDetailInfoDTO.setOrderPlanTypeName("生产质检");
                orderPlanDetailInfoDTO.setInstallStartTime(diOrderQuality.getCheckStartTime());
                orderPlanDetailInfoDTO.setInstallEndTime(diOrderQuality.getCheckEndTime());
                orderPlanDetailInfoDTO.setEnforceUserId(diOrderQuality.getQualityUserId());
                orderProductPlan.getOrderPlanDTOList().add(orderPlanDetailInfoDTO);
            });
            list.add(orderProductPlan);
        });
        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void startDeliveryChangeApproval(StartDeliveryChangeApprovalDTO dto) {
        if (StringUtils.isBlank(SecurityUtils.getUsername()) || null == SecurityUtils.getLoginUser().getSysUser()) {
            throw new ServiceException("用户信息不正确");
        }
        //唯一校验
        Long count = diOrderDeliveryChangeMapper.selectCount(new LambdaQueryWrapper<DiOrderDeliveryChange>()
                .eq(DiOrderDeliveryChange::getOrderId, dto.getOrderId())
                .eq(DiOrderDeliveryChange::getIsDeleted, 0));
        if (0 < count) {
            throw new ServiceException("已提交变更申请，不允许再次申请");
        }
        DiOrder order = diOrderMapper.selectById(dto.getOrderId());
        if (StringUtils.isBlank(order.getSystemsEngineer())) {
            throw new ServiceException("请先设置系统工程师");
        }
        DiOrderDeliveryChange orderDeliveryChange = OrderConvert.INSTANCE.changeApprovalDTOTOEntity(dto);
        diOrderDeliveryChangeMapper.insert(orderDeliveryChange);
        //事务提交后执行
        AfterCommitExecutor.submit(() -> {
            //构建启动参数
            StartProcessDTO changeProcessDto = getStartProcessDTO(orderDeliveryChange, Arrays.asList(order.getSystemsEngineer()));
            //启动审批流程
            BaseResponse<String> changeResponse = processClient.startProcess(changeProcessDto);
            if (null == changeResponse || !changeResponse.isSuccess()) {
                throw new ServiceException("创建订单项目交付日期变更审批流失败");
            }
            LambdaUpdateChainWrapper<DiOrderDeliveryChange> recordUpdate = new LambdaUpdateChainWrapper<>(diOrderDeliveryChangeMapper);
            recordUpdate.eq(DiOrderDeliveryChange::getId, orderDeliveryChange.getId());
            recordUpdate.set(DiOrderDeliveryChange::getApprovalProcessId, changeResponse.getData());
            recordUpdate.update();
            LambdaUpdateChainWrapper<DiOrder> orderUpdate = new LambdaUpdateChainWrapper<>(diOrderMapper);
            orderUpdate.eq(DiOrder::getId, dto.getOrderId());
            orderUpdate.set(DiOrder::getIsChangeDeliveryDate, 1);
            orderUpdate.update();
        }, (e) -> log.error("启动订单项目交付日期变更流程失败", e));
    }

    /**
     * 构建启动审批流程需要参数
     *
     * @return 启动参数
     */
    private StartProcessDTO getStartProcessDTO(DiOrderDeliveryChange orderDeliveryChange, List<String> approvalUserList) {
        StartProcessDTO changeProcessDto = new StartProcessDTO();
        changeProcessDto.setProcessDefinitionKey(ContractConstants.ORDER_CHANGE_DELIVERY_APPROVAL_PROCESS);
        changeProcessDto.setBusinessKey(orderDeliveryChange.getId().toString());
        Map<String, Object> changeMap = new HashMap<>(3);
        //发起人
        changeMap.put(ContractConstants.LAUNCH_BY, SecurityUtils.getUsername());
        String launchByName = "";
        if (StringUtils.isNotBlank(SecurityUtils.getLoginUser().getSysUser().getNickName())) {
            launchByName = SecurityUtils.getLoginUser().getSysUser().getNickName();
        }
        changeMap.put(ContractConstants.LAUNCH_BY_NAME, launchByName);
        //交付主管审批角色
        changeMap.put(ContractConstants.DELIVER_MANAGE_ROLE, commonService.encodeLiabilityUserList(approvalUserList));
        changeProcessDto.setVariableMap(changeMap);
        return changeProcessDto;
    }

    @Override
    public boolean checkApprovalBy(DeliveryChangeApprovalProcessDTO dto) {
        if (null == dto.getOrderId() && StringUtils.isBlank(dto.getOrderNo())) {
            throw new ServiceException("必填项缺失");
        }
        //获取合同信息
        DiOrder order = diOrderMapper.selectDiOrderById(null != dto.getOrderId() ? dto.getOrderId().toString() : null, dto.getOrderNo());
        if (null == order) {
            throw new ServiceException("订单不存在");
        }
        List<DiOrderDeliveryChange> changeList = diOrderDeliveryChangeMapper.selectList(new LambdaQueryWrapper<DiOrderDeliveryChange>()
                .eq(DiOrderDeliveryChange::getOrderId, order.getId())
                .eq(DiOrderDeliveryChange::getIsDeleted, 0));
        if (CollectionUtil.isEmpty(changeList)) {
            log.error("DiOrderDeliveryChangeServiceImpl---checkApprovalBy()---订单下无变更记录，订单ID：{}", order.getId());
            return false;
        }
        if (changeList.size() > 1) {
            throw new ServiceException("变更记录不正确");
        }
        //获取当前登录者
        if (null == SecurityUtils.getLoginUser() || null == SecurityUtils.getLoginUser().getSysUser()) {
            throw new ServiceException("用户信息不正确");
        }
        return diAgencyApprovalService.checkHasTask(order.getOrderNo(), ApprovalTypeEnum.POD_CHANGE_APPROVAL, SecurityUtils.getUsername());

//        String userId = SecurityUtils.getLoginUser().getSysUser().getUserId().toString();
//        //获取登录用户角色权限
//        List<Long> roleIdList = remoteUserService.getUserRoleId(userId);
//        if (CollectionUtil.isEmpty(roleIdList)) {
//            log.error("DiOrderDeliveryChangeServiceImpl---checkApprovalBy()---当前用户未绑定角色，用户ID：{}", userId);
//            return false;
//        }
//        log.info("DiOrderDeliveryChangeServiceImpl---checkApprovalBy()---当前用户绑定的角色ID：{}", JSONUtil.toJsonStr(roleIdList));
//        //构建校验用户审批权限入参DTO
//        List<String> roleIdStrList = roleIdList.stream().map(Object::toString).distinct().toList();
//        CheckUserPermissionDTO checkDto = new CheckUserPermissionDTO();
//        checkDto.setBusinessKey(changeList.get(0).getId().toString());
//        checkDto.setProcessInstanceId(changeList.get(0).getApprovalProcessId());
//        checkDto.setApprovalBy(userId);
//        checkDto.setApprovalRoleId(roleIdStrList);
//        checkDto.setApprovalByEmployeeNo(SecurityUtils.getLoginUser().getUsername());
//        BaseResponse<Boolean> response = processTaskClient.checkUserPermission(checkDto);
//        if (null == response || !response.isSuccess()) {
//            log.info("DiOrderDeliveryChangeServiceImpl---checkApprovalBy()---校验用户审批权限失败");
//            return false;
//        }
//        return response.getData();
    }

    @Override
    public OrderDeliveryChangeVO getOrderDeliveryChange(GetOrderDeliveryChangeDTO dto) {
        DiOrderDeliveryChange orderDeliveryChange = diOrderDeliveryChangeMapper.selectById(dto.getDeliveryChangeId());
        if (null == orderDeliveryChange || orderDeliveryChange.getIsDeleted().equals(1)) {
            throw new ServiceException("订单项目交付日期变更记录不存在");
        }
        OrderDeliveryChangeVO vo = OrderConvert.INSTANCE.entityTOOrderDeliveryChangeVO(orderDeliveryChange);
        //构建中文名
        if (StringUtils.isBlank(vo.getCreateBy())) {
            return vo;
        }
        Map<String, String> nameMap = commonService.getUserNameByJob(Collections.singletonList(vo.getCreateBy()), null);
        if (CollectionUtil.isEmpty(nameMap)) {
            return vo;
        }
        vo.setCreateByName(nameMap.get(vo.getCreateBy()));
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deliveryChangeApproval(DeliveryChangeApprovalDTO dto) {
        //获取变更记录
        DiOrderDeliveryChange orderDeliveryChange = diOrderDeliveryChangeMapper.selectById(dto.getDeliveryChangeId());
        //数据校验并返回审批角色ID
        String approvalRoleId = checkDeliveryChangeApproval(dto, orderDeliveryChange);
        //更新变更记录
        List<String> preSaleIdList = dto.getUpdateOrderPlanDateDtoList().stream().map(orderPlan -> orderPlan.getPreSaleId().toString()).distinct().toList();
        orderDeliveryChange.setPreSaleIdList(preSaleIdList);
        diOrderDeliveryChangeMapper.updateById(orderDeliveryChange);
        //更新订单数据
        LambdaUpdateChainWrapper<DiOrder> orderUpdate = new LambdaUpdateChainWrapper<>(diOrderMapper);
        orderUpdate.eq(DiOrder::getId, orderDeliveryChange.getOrderId());
        orderUpdate.set(DiOrder::getIsChangeDeliveryDate, 0);
        orderUpdate.set(DiOrder::getUpdateBy, SecurityUtils.getUsername());
        if (dto.getApprovalResult()) {
            //更新项目交付日期
            orderUpdate.set(DiOrder::getOrderDeliveryDate, orderDeliveryChange.getChangeDeliveryDate());
            orderUpdate.update();
            //钉钉消息发送人
            List<String> sendUserIdList = new ArrayList<>();
            //获取订单信息
            DiOrder diOrder = diOrderMapper.selectDiOrderById(String.valueOf(orderDeliveryChange.getOrderId()), null);
            DiMarketingNiche niche = nicheService.selectDiMarketingNicheByNo(diOrder.getNicheNo());

            if (niche != null) {
                sendUserIdList.add(niche.getNicheOwner());
            }
//            if (Objects.nonNull(diOrder)) {
//                if (StringUtils.isNotBlank(diOrder.getMachineDesignUserId())) {
//                    sendUserIdList.add(diOrder.getMachineDesignUserId());
//                }
//                if (StringUtils.isNotBlank(diOrder.getElectricDesignUserId())) {
//                    sendUserIdList.add(diOrder.getElectricDesignUserId());
//                }
//                if (StringUtils.isNotBlank(diOrder.getPurchaseUserId())) {
//                    sendUserIdList.add(diOrder.getElectricDesignUserId());
//                }
//                if (StringUtils.isNotBlank(diOrder.getProduceUserId())) {
//                    sendUserIdList.addAll(List.of(diOrder.getProduceUserId().split(",")));
//                }
//                if (StringUtils.isNotBlank(diOrder.getProduceQualityUserId())) {
//                    sendUserIdList.addAll(List.of(diOrder.getProduceQualityUserId().split(",")));
//                }
//            }
            if (CollectionUtil.isNotEmpty(dto.getUpdateOrderPlanDateDtoList())) {
                dto.getUpdateOrderPlanDateDtoList().forEach(updateOrderPlanDateDto -> {
                    //判断是否修改了机械设计
                    if (null != updateOrderPlanDateDto.getMachineDesignId()) {
                        LambdaUpdateChainWrapper<DiOrderMachineDesign> machineUpdate = new LambdaUpdateChainWrapper<>(diOrderMachineDesignMapper);
                        machineUpdate.eq(DiOrderMachineDesign::getId, updateOrderPlanDateDto.getMachineDesignId());
                        machineUpdate.set(DiOrderMachineDesign::getExpectStartTime, updateOrderPlanDateDto.getMachineExpectStartTime());
                        machineUpdate.set(DiOrderMachineDesign::getExpectEndTime, updateOrderPlanDateDto.getMachineExpectEndTime());
                        machineUpdate.set(DiOrderMachineDesign::getUpdateBy, SecurityUtils.getUsername());
                        machineUpdate.update();
                        //记录负责人
                        if (StringUtils.isNotBlank(updateOrderPlanDateDto.getMachineDesignUserId())) {
                            sendUserIdList.add(updateOrderPlanDateDto.getMachineDesignUserId());
                        }
                    }
                    //判断是否修改了电气设计
                    if (null != updateOrderPlanDateDto.getElectricDesignId()) {
                        LambdaUpdateChainWrapper<DiOrderElectricDesign> electricUpdate = new LambdaUpdateChainWrapper<>(diOrderElectricDesignMapper);
                        electricUpdate.eq(DiOrderElectricDesign::getId, updateOrderPlanDateDto.getElectricDesignId());
                        electricUpdate.set(DiOrderElectricDesign::getExpectStartTime, updateOrderPlanDateDto.getElectricExpectStartTime());
                        electricUpdate.set(DiOrderElectricDesign::getExpectEndTime, updateOrderPlanDateDto.getElectricExpectEndTime());
                        electricUpdate.set(DiOrderElectricDesign::getUpdateBy, SecurityUtils.getUsername());
                        electricUpdate.update();
                        //记录负责人
                        if (StringUtils.isNotBlank(updateOrderPlanDateDto.getElectricDesignUserId())) {
                            sendUserIdList.add(updateOrderPlanDateDto.getElectricDesignUserId());
                        }
                    }
                    //判断是否修改了物料计划
                    if (null != updateOrderPlanDateDto.getMaterielPlanId()) {
                        LambdaUpdateChainWrapper<DiOrderMaterielPlan> materielPlanUpdate = new LambdaUpdateChainWrapper<>(diOrderMaterielPlanMapper);
                        materielPlanUpdate.eq(DiOrderMaterielPlan::getId, updateOrderPlanDateDto.getMaterielPlanId());
                        materielPlanUpdate.set(DiOrderMaterielPlan::getExpectStartTime, updateOrderPlanDateDto.getMaterielPlanExpectStartTime());
                        materielPlanUpdate.set(DiOrderMaterielPlan::getExpectEndTime, updateOrderPlanDateDto.getMaterielPlanExpectEndTime());
                        materielPlanUpdate.set(DiOrderMaterielPlan::getUpdateBy, SecurityUtils.getUsername());
                        materielPlanUpdate.update();
                        //记录负责人
                        if (StringUtils.isNotBlank(updateOrderPlanDateDto.getMaterielPlanUserId())) {
                            sendUserIdList.add(updateOrderPlanDateDto.getMaterielPlanUserId());
                        }
                    }
                    //判断是否修改了生产计划
                    if (null != updateOrderPlanDateDto.getProduceId()) {
                        LambdaUpdateChainWrapper<DiOrderProduce> produceUpdate = new LambdaUpdateChainWrapper<>(diOrderProduceMapper);
                        produceUpdate.eq(DiOrderProduce::getId, updateOrderPlanDateDto.getProduceId());
                        produceUpdate.set(DiOrderProduce::getProduceStartTime, updateOrderPlanDateDto.getProduceExpectStartTime());
                        produceUpdate.set(DiOrderProduce::getProduceEndTime, updateOrderPlanDateDto.getProduceExpectEndTime());
                        produceUpdate.set(DiOrderProduce::getUpdateBy, SecurityUtils.getUsername());
                        produceUpdate.update();
                        if (StringUtils.isNotBlank(updateOrderPlanDateDto.getProduceUserId())) {
                            sendUserIdList.addAll(List.of(updateOrderPlanDateDto.getProduceUserId().split(",")));
                        }
                    }
                    //判断是否修改了生产质检
                    if (null != updateOrderPlanDateDto.getQualityId()) {
                        LambdaUpdateChainWrapper<DiOrderQuality> qualityUpdate = new LambdaUpdateChainWrapper<>(diOrderQualityMapper);
                        qualityUpdate.eq(DiOrderQuality::getId, updateOrderPlanDateDto.getQualityId());
                        qualityUpdate.set(DiOrderQuality::getCheckStartTime, updateOrderPlanDateDto.getQualityExpectStartTime());
                        qualityUpdate.set(DiOrderQuality::getCheckEndTime, updateOrderPlanDateDto.getQualityExpectEndTime());
                        qualityUpdate.set(DiOrderQuality::getUpdateBy, SecurityUtils.getUsername());
                        qualityUpdate.update();
                        if (StringUtils.isNotBlank(updateOrderPlanDateDto.getQualityUserId())) {
                            sendUserIdList.addAll(List.of(updateOrderPlanDateDto.getQualityUserId().split(",")));
                        }
                    }
                });
                //预警消息处理
                updateOrderWarning(dto);
            }
            if (CollectionUtil.isNotEmpty(sendUserIdList)) {
                //发送钉钉消息
                String title = String.format(OrderDingTalkEnum.ORDER_DELIVERY_CHANGE_APPROVAL_AGREE_NOTICE.getTitle(), diOrder.getProjectNo());
                String content = String.format(OrderDingTalkEnum.ORDER_DELIVERY_CHANGE_APPROVAL_AGREE_NOTICE.getMessage(), diOrder.getProjectNo(), diOrder.getOrderNo());
                orderCommonService.sendDingTalkMessage("订单", title, content, sendUserIdList.stream().distinct().toList(), null);
            }
        } else {
            orderUpdate.update();
        }
        //构建审批任务入参DTO
        ApprovalTaskDTO approvalTaskDto = new ApprovalTaskDTO();
        approvalTaskDto.setTaskId(orderDeliveryChange.getCamundaTaskId());
        approvalTaskDto.setApprovalResult(dto.getApprovalResult());
        approvalTaskDto.setRejectReason("");
        approvalTaskDto.setApprovalBy(SecurityUtils.getUsername());
        approvalTaskDto.setApprovalRoleId(approvalRoleId);
        //调用审批流
        BaseResponse<Void> response = processTaskClient.approvalTask(approvalTaskDto);
        //判断是否成功
        if (null == response) {
            throw new ServiceException("订单项目交付日期变更审批失败");
        }
        if (!response.isSuccess()) {
            throw new ServiceException(response.getMsg());
        }
    }

    private String checkDeliveryChangeApproval(DeliveryChangeApprovalDTO dto, DiOrderDeliveryChange orderDeliveryChange) {
        if (null == orderDeliveryChange) {
            throw new ServiceException("订单项目交付日期变更记录不存在");
        }
        if (StringUtils.isBlank(orderDeliveryChange.getCamundaTaskId())) {
            throw new ServiceException("变更记录信息有误");
        }
        if (orderDeliveryChange.getIsDeleted().equals(1)) {
            throw new ServiceException("订单项目交付日期变更已被审批，请刷新界面");
        }
        if (CollectionUtil.isNotEmpty(dto.getUpdateOrderPlanDateDtoList())) {
            //入参校验
            dto.getUpdateOrderPlanDateDtoList().forEach(updateOrderPlanDateDto -> {
                if (null != updateOrderPlanDateDto.getMachineDesignId()) {
                    if (null == updateOrderPlanDateDto.getMachineExpectStartTime() || null == updateOrderPlanDateDto.getMachineExpectEndTime()) {
                        throw new ServiceException("机械设计时间不正确");
                    }
                }
                if (null != updateOrderPlanDateDto.getElectricDesignId()) {
                    if (null == updateOrderPlanDateDto.getElectricExpectStartTime() || null == updateOrderPlanDateDto.getElectricExpectEndTime()) {
                        throw new ServiceException("电气设计时间不正确");
                    }
                }
                if (null != updateOrderPlanDateDto.getProduceId()) {
                    if (null == updateOrderPlanDateDto.getProduceExpectStartTime() || null == updateOrderPlanDateDto.getProduceExpectEndTime()) {
                        throw new ServiceException("生产计划时间不正确");
                    }
                }
                if (null != updateOrderPlanDateDto.getQualityId()) {
                    if (null == updateOrderPlanDateDto.getQualityExpectStartTime() || null == updateOrderPlanDateDto.getQualityExpectEndTime()) {
                        throw new ServiceException("生产质检时间不正确");
                    }
                }
            });
        }
        //根据变更记录存储的任务ID获取任务信息
        List<DiAgencyApproval> taskList = diAgencyApprovalService.list(new LambdaQueryWrapper<DiAgencyApproval>().eq(DiAgencyApproval::getCamundaTaskId, orderDeliveryChange.getCamundaTaskId()));
        if (CollectionUtil.isEmpty(taskList)) {
            throw new ServiceException("未获取到待审批任务");
        }
        if (taskList.stream().filter(x -> x.getLiabilityBy().equals(SecurityUtils.getUsername())).count() <= 0) {
            throw new ServiceException("当前登录者无权限");
        }

        //获取审批角色ID
//        List<String> approvalRoleId = taskList.stream().map(DiAgencyApproval::getLiabilityRoleId).distinct().collect(Collectors.toList());
        //获取当前登录用户所有角色
//        List<Long> roleIdList = remoteUserService.getUserRoleId(SecurityUtils.getLoginUser().getSysUser().getUserId().toString());
//        if (CollectionUtil.isEmpty(roleIdList)) {
//            throw new ServiceException("当前登录者无权限");
//        }
        String assignee = taskList.get(ContractConstants.ZERO).getLiabilityRoleId();
        //校验登录者是否有权限审批
//        List<String> roleIdStrList = roleIdList.stream().map(Object::toString).distinct().toList();
//        if (!roleIdStrList.contains(assignee) && !commonService.decodeLiabilityUserList(assignee).contains(SecurityUtils.getUsername())) {
//            throw new ServiceException("无审批权限");
//        }
        return assignee;
    }

    private void updateOrderWarning(DeliveryChangeApprovalDTO dto) {
        if (CollectionUtil.isEmpty(dto.getUpdateOrderPlanDateDtoList())) {
            return;
        }
        List<Long> preSaleIdList = dto.getUpdateOrderPlanDateDtoList().stream().map(UpdateOrderPlanDateDTO::getPreSaleId).distinct().toList();
        List<DiOrderWarning> orderWarningList = diOrderWarningMapper.selectList(new LambdaQueryWrapper<DiOrderWarning>()
                .eq(DiOrderWarning::getOrderNo, dto.getOrderNo())
                .in(DiOrderWarning::getPreSaleId, preSaleIdList)
                .eq(DiOrderWarning::getIsSolve, "0"));
        if (CollectionUtil.isEmpty(orderWarningList)) {
            return;
        }
        String timeString = LocalDate.now() + " 00:00:00";
        Date dateTime = DateUtil.parse(timeString);
        Map<String, List<DiOrderWarning>> orderWarningMap = orderWarningList.stream()
                .collect(Collectors.groupingBy(orderWarning -> orderWarning.getOrderNo() + "-" + orderWarning.getPreSaleId() + "-" + orderWarning.getBusinessId()));
        dto.getUpdateOrderPlanDateDtoList().forEach(updateOrderPlanDateDto -> {
            if (null != updateOrderPlanDateDto.getMachineDesignId() &&
                    orderWarningMap.containsKey(dto.getOrderNo() + "-" + updateOrderPlanDateDto.getPreSaleId() + "-" + updateOrderPlanDateDto.getMachineDesignId())) {
                List<DiOrderWarning> warningList = orderWarningMap.get(dto.getOrderNo() + "-" + updateOrderPlanDateDto.getPreSaleId() + "-" + updateOrderPlanDateDto.getMachineDesignId());
                warningList.forEach(warning -> {
                    if ("1".equals(warning.getWarningLevel()) && !warning.getEstimateTime().equals(updateOrderPlanDateDto.getMachineExpectStartTime())) {
                        //修改了开始时间,重新判断时间
                        if (updateOrderPlanDateDto.getMachineExpectStartTime().compareTo(dateTime) > 0) {
                            solveOrderWarning(warning);
                        }
                    }
                    if ("2".equals(warning.getWarningLevel()) && !warning.getEstimateTime().equals(updateOrderPlanDateDto.getMachineExpectEndTime())) {
                        //修改了结束时间
                        if (updateOrderPlanDateDto.getMachineExpectEndTime().compareTo(dateTime) < 0) {
                            //计算超出预计时间天数
                            Integer exceedDay = Integer.valueOf(String.valueOf(DateUtil.betweenDay(updateOrderPlanDateDto.getMachineExpectEndTime(), new Date(), true)));
                            updateWarning(warning, exceedDay);
                        } else {
                            solveOrderWarning(warning);
                        }
                    }
                });
            }
            if (null != updateOrderPlanDateDto.getElectricDesignId() &&
                    orderWarningMap.containsKey(dto.getOrderNo() + "-" + updateOrderPlanDateDto.getPreSaleId() + "-" + updateOrderPlanDateDto.getElectricDesignId())) {
                List<DiOrderWarning> warningList = orderWarningMap.get(dto.getOrderNo() + "-" + updateOrderPlanDateDto.getPreSaleId() + "-" + updateOrderPlanDateDto.getElectricDesignId());
                warningList.forEach(warning -> {
                    if ("1".equals(warning.getWarningLevel()) && !warning.getEstimateTime().equals(updateOrderPlanDateDto.getElectricExpectStartTime())) {
                        //修改了开始时间,重新判断时间
                        if (updateOrderPlanDateDto.getElectricExpectStartTime().compareTo(dateTime) > 0) {
                            solveOrderWarning(warning);
                        }
                    }
                    if ("2".equals(warning.getWarningLevel()) && !warning.getEstimateTime().equals(updateOrderPlanDateDto.getElectricExpectEndTime())) {
                        //修改了结束时间
                        if (updateOrderPlanDateDto.getElectricExpectEndTime().compareTo(dateTime) < 0) {
                            //计算超出预计时间天数
                            Integer exceedDay = Integer.valueOf(String.valueOf(DateUtil.betweenDay(updateOrderPlanDateDto.getElectricExpectEndTime(), new Date(), true)));
                            updateWarning(warning, exceedDay);
                        } else {
                            solveOrderWarning(warning);
                        }
                    }
                });
            }
            if (null != updateOrderPlanDateDto.getProduceId() &&
                    orderWarningMap.containsKey(dto.getOrderNo() + "-" + updateOrderPlanDateDto.getPreSaleId() + "-" + updateOrderPlanDateDto.getProduceId())) {
                List<DiOrderWarning> warningList = orderWarningMap.get(dto.getOrderNo() + "-" + updateOrderPlanDateDto.getPreSaleId() + "-" + updateOrderPlanDateDto.getProduceId());
                warningList.forEach(warning -> {
                    if ("1".equals(warning.getWarningLevel()) && !warning.getEstimateTime().equals(updateOrderPlanDateDto.getProduceExpectStartTime())) {
                        //修改了开始时间,重新判断时间
                        if (updateOrderPlanDateDto.getProduceExpectStartTime().compareTo(dateTime) > 0) {
                            solveOrderWarning(warning);
                        }
                    }
                    if ("2".equals(warning.getWarningLevel()) && !warning.getEstimateTime().equals(updateOrderPlanDateDto.getProduceExpectEndTime())) {
                        //修改了结束时间
                        if (updateOrderPlanDateDto.getProduceExpectEndTime().compareTo(dateTime) < 0) {
                            //计算超出预计时间天数
                            Integer exceedDay = Integer.valueOf(String.valueOf(DateUtil.betweenDay(updateOrderPlanDateDto.getProduceExpectEndTime(), new Date(), true)));
                            updateWarning(warning, exceedDay);
                        } else {
                            solveOrderWarning(warning);
                        }
                    }
                });
            }
            if (null != updateOrderPlanDateDto.getQualityId() &&
                    orderWarningMap.containsKey(dto.getOrderNo() + "-" + updateOrderPlanDateDto.getPreSaleId() + "-" + updateOrderPlanDateDto.getQualityId())) {
                List<DiOrderWarning> warningList = orderWarningMap.get(dto.getOrderNo() + "-" + updateOrderPlanDateDto.getPreSaleId() + "-" + updateOrderPlanDateDto.getQualityId());
                warningList.forEach(warning -> {
                    if ("1".equals(warning.getWarningLevel()) && !warning.getEstimateTime().equals(updateOrderPlanDateDto.getQualityExpectStartTime())) {
                        //修改了开始时间,重新判断时间
                        if (updateOrderPlanDateDto.getQualityExpectStartTime().compareTo(dateTime) > 0) {
                            solveOrderWarning(warning);
                        }
                    }
                    if ("2".equals(warning.getWarningLevel()) && !warning.getEstimateTime().equals(updateOrderPlanDateDto.getQualityExpectEndTime())) {
                        //修改了结束时间
                        if (updateOrderPlanDateDto.getQualityExpectEndTime().compareTo(dateTime) < 0) {
                            //计算超出预计时间天数
                            Integer exceedDay = Integer.valueOf(String.valueOf(DateUtil.betweenDay(updateOrderPlanDateDto.getQualityExpectEndTime(), new Date(), true)));
                            updateWarning(warning, exceedDay);
                        } else {
                            solveOrderWarning(warning);
                        }
                    }
                });
            }
        });
    }

    private void solveOrderWarning(DiOrderWarning warning) {
        LambdaUpdateWrapper<DiOrderWarning> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(DiOrderWarning::getId, warning.getId());
        updateWrapper.set(DiOrderWarning::getIsSolve, "1");
        updateWrapper.set(DiOrderWarning::getSolveMode, 1);
        updateWrapper.set(DiOrderWarning::getSolveDescribe, "项目交付日期变更解决预警");
        updateWrapper.set(DiOrderWarning::getWarningSolveTime, new Date());
        diOrderWarningMapper.update(updateWrapper);
    }

    public void updateWarning(DiOrderWarning warning, Integer exceedDay) {
        LambdaUpdateWrapper<DiOrderWarning> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(DiOrderWarning::getId, warning.getId());
        updateWrapper.set(DiOrderWarning::getExceedDay, exceedDay);
        String warningContent = warning.getWarningContent().substring(0, warning.getWarningContent().lastIndexOf("，")) + "，已超时" + exceedDay + "天";
        updateWrapper.set(DiOrderWarning::getWarningContent, warningContent);
        diOrderWarningMapper.update(updateWrapper);
    }


}
