package com.dyd.di.comment.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CommentQueryResponse {

    /**
     * queryList 字段有值、不为空，证明目前不可以唯一确认某一个项目
     */
    private CommentQueryListVO queryList;

    /**
     * query字段有值、不为空，证明可以唯一确定某一个项目，页面显示评论列表
     */
    private CommentQueryVO query;

    /**
     * 阶段——评论 列表 按照阶段时间倒序
     */
    private List<DiCommentStageView> commentList;

}
