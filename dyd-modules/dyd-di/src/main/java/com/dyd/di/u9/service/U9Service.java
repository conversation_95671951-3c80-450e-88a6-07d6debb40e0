package com.dyd.di.u9.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dyd.common.core.domain.R;
import com.dyd.common.core.utils.StringUtils;
import com.dyd.dbc.api.RemoteDbcService;
import com.dyd.dbc.api.model.DdUserDTO;
import com.dyd.dbc.api.model.QueryDdUserDTO;
import com.dyd.di.contract.entity.DiContract;
import com.dyd.di.contract.mapper.DiContractMapper;
import com.dyd.di.fincompany.entity.DiFinCompany;
import com.dyd.di.fincompany.mapper.DiFinCompanyMapper;
import com.dyd.di.marketing.domain.DiMarketingCustomer;
import com.dyd.di.marketing.domain.DiMarketingNiche;
import com.dyd.di.marketing.mapper.DiMarketingCustomerMapper;
import com.dyd.di.marketing.mapper.DiMarketingNicheMapper;
import com.dyd.di.materiel.domain.DiMateriel;
import com.dyd.di.materiel.mapper.DiMaterielMapper;
import com.dyd.di.pre.entity.DiPreSale;
import com.dyd.di.pre.entity.DiPreSaleManifest;
import com.dyd.di.pre.entity.DiPreSaleQuote;
import com.dyd.di.pre.entity.DiPreSaleQuoteDetail;
import com.dyd.di.pre.mapper.DiPreSaleManifestMapper;
import com.dyd.di.pre.mapper.DiPreSaleMapper;
import com.dyd.di.pre.mapper.DiPreSaleQuoteDetailMapper;
import com.dyd.di.pre.mapper.DiPreSaleQuoteMapper;
import com.dyd.di.pre.entity.*;
import com.dyd.di.pre.mapper.*;
import com.dyd.di.process.domain.DiProcessProject;
import com.dyd.di.process.domain.DiProjectRelation;
import com.dyd.di.process.mapper.DiProcessProjectMapper;
import com.dyd.di.process.mapper.DiProjectRelationMapper;
import com.dyd.di.process.pojo.dto.RelationTypeDTO;
import com.dyd.di.u9.config.DingtalkU9Config;
import com.dyd.di.u9.config.U9IgnoreConfig;
import com.dyd.di.u9.domain.CreateU9SoResult;
import com.dyd.di.u9.domain.DiSynErpLog;
import com.dyd.di.u9.domain.OrgCodeEnum;
import com.dyd.di.u9.mapper.DiSynErpLogMapper;
import com.dyd.dingtalk.DingtalkClient;
import com.dyd.erp.ErpClient;
import com.dyd.erp.ErpConfig;
import com.dyd.erp.bean.bill.CreateProjectRecBillRequest;
import com.dyd.erp.bean.bill.CreateProjectRecBillResponse;
import com.dyd.erp.bean.bill.CreateU9SoRequest;
import com.dyd.erp.bean.bill.CreateU9SoResponse;
import com.dyd.erp.bean.customer.ErpCreateCustRequest;
import com.dyd.erp.bean.customer.ErpCreateCustResponse;
import com.dyd.erp.bean.project.*;
import com.dyd.exchange.RemoteExchangeService;
import com.dyd.exchange.model.BaseDefineValueResponse;
import com.dyd.exchange.model.ErpSaleResponse;
import com.dyd.exchange.model.JdyCustomerExchange;
import com.dyd.exchange.model.MaterielBomSynRequest;
import com.dyd.system.api.RemoteDictDataService;
import com.dyd.system.api.domain.SysDictData;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class U9Service {

    @Autowired
    private ErpConfig erpConfig;


    @Autowired
    private DiContractMapper diContractMapper;

    @Autowired
    private DiPreSaleMapper diPreSaleMapper;


    @Autowired
    private DiPreSaleManifestMapper diPreSaleManifestMapper;

    @Autowired
    private DiPreSaleQuoteDetailMapper diPreSaleQuoteDetailMapper;

    @Autowired
    private DiPreSaleQuoteMapper diPreSaleQuoteMapper;

    @Autowired
    private DiPreSaleQuotePeriodMapper diPreSaleQuotePeriodMapper;

    @Autowired
    private DiMaterielMapper diMaterielMapper;

    @Autowired
    private RemoteExchangeService remoteExchangeService;

    @Autowired
    private DiProcessProjectMapper diProcessProjectMapper;

    @Autowired
    private RemoteDbcService remoteDbcService;

    @Autowired
    private ErpClient erpClient;

    @Autowired
    private RemoteDictDataService remoteDictDataService;

    @Autowired
    private DiMarketingNicheMapper diMarketingNicheMapper;

    @Autowired
    private DiMarketingCustomerMapper diMarketingCustomerMapper;

    @Autowired
    private DiProjectRelationMapper diProjectRelationMapper;

    @Autowired
    private DiFinCompanyMapper diFinCompanyMapper;

    @Autowired
    private DiSynErpLogMapper diSynErpLogMapper;

    @Autowired
    private DiPreSaleQuoteShareMapper diPreSaleQuoteShareMapper;

    @Autowired
    private DingtalkClient dingtalkClient;

    @Autowired
    private DingtalkU9Config dingtalkU9Config;

    @Autowired
    private U9IgnoreConfig u9IgnoreConfig;


    /**
     * 创建标准销售
     * @param contractId
     */
    public CreateU9SoResult createSoU9(Long contractId) {

        CreateU9SoResult createU9SoResult = new CreateU9SoResult();

        DiContract diContract = diContractMapper.selectById(contractId);

        log.info("同步u9开始,合同号{}",diContract.getContractNo());

        //项目号
        String projectNo = diContract.getProjectNo();
        //订单
        R<String> docNoR = remoteExchangeService.selectDocNoByProjectCode(projectNo);
        if(docNoR.isSuccess() && StringUtils.isNotEmpty(docNoR.getData())){
            createU9SoResult.setStatus("1");
            return createU9SoResult;
        }



        RelationTypeDTO relationTypeDTO = new RelationTypeDTO();
        relationTypeDTO.setProjectNo(diContract.getProjectNo());
        //查询项目关联信息
        List<DiProjectRelation> relationList = diProjectRelationMapper.selectList(Wrappers.<DiProjectRelation>lambdaQuery().eq(DiProjectRelation::getProjectNo, diContract.getProjectNo()));

        if (CollectionUtil.isEmpty(relationList)) {
            throw new RuntimeException("查询项目关联信息错误");
        }

        //商机号
        String nicheCode = diContract.getNicheNo();

        com.dyd.erp.bean.bill.CreateU9SoRequest.Context context = com.dyd.erp.bean.bill.CreateU9SoRequest.Context.builder()
                .CultureName("zh-CN")
                .EntCode(erpConfig.getEntCode())
                .UserCode("admin")
                .build();

        com.dyd.erp.bean.bill.CreateU9SoRequest.CreateU9So createU9So = new CreateU9SoRequest.CreateU9So();
        //询价单号--商机
        createU9So.setInquiryCode(nicheCode);
        //商机-订单类型
        List<DiMarketingNiche> diMarketingNiches = diMarketingNicheMapper.selectList(Wrappers.<DiMarketingNiche>lambdaQuery().eq(DiMarketingNiche::getNicheNo, nicheCode).eq(DiMarketingNiche::getDelFlag, 0));
        if(CollectionUtil.isEmpty(diMarketingNiches)){
            throw new RuntimeException("商机为空");
        }
        String orderType = diMarketingNiches.get(0).getOrderType();
        //合同号
        String contractCode = diContract.getContractNo();

        //销售合同条款
        if (StringUtils.isEmpty(diContract.getSaleContractClause())) {
            throw new RuntimeException("查询项目关联信息错误--销售合同条款为空");
        }

        //销售合同条款
        createU9So.setOrderTerm(diContract.getSaleContractClause());

        DiFinCompany diFinCompany = diFinCompanyMapper.selectById(diContract.getSecondPartyName());

        //签单公司
        String org = OrgCodeEnum.getType(diFinCompany.getCompanyName());

        List<String> orgs = Arrays.asList("01","02","03","04","07");

        /*if(StringUtils.isEmpty(OrgCodeEnum.getType(secondPartyName))){
            throw new RuntimeException("签单公司无法匹配");
        }

        String org = OrgCodeEnum.getType(secondPartyName);*/

        //主体
        context.setOrgCode(org);
        createU9So.setOrgCode(org);

        long day = 0;
        if(Objects.nonNull(diContract.getContractDeliveryCycleWeek())) {
            //交期(周)
            day = diContract.getContractDeliveryCycleWeek().setScale(0,RoundingMode.UP).intValue();
            if(day > 22){
                day = 22;
            }
            if(day == 0){
                day = 1;
            }
        }else{
            day = 7;
        }
        //交期(周)
        R<BaseDefineValueResponse> baseDefineValueResponsesT = remoteExchangeService.selectBaseDefineValue(1002002070009355L, day+"周");

        if(baseDefineValueResponsesT.isSuccess() && Objects.isNull(baseDefineValueResponsesT)){
            createU9So.setLeadTime("");
        }else{
            createU9So.setLeadTime(baseDefineValueResponsesT.getData().getCode());
        }
        //交期
        createU9So.setDeliveryDate(LocalDateTime.now().plusDays(day*7).minusDays(3).toString());

        //合同号
        createU9So.setBusinessContractCode(contractCode);

        DiMarketingNiche diMarketingNiche = diMarketingNicheMapper.selectDiMarketingNicheById(nicheCode);
        if(Objects.isNull(diMarketingNiche)){
            throw new RuntimeException("无效商机"+nicheCode);
        }



        //客户
        DiMarketingCustomer diMarketingCustomer = diMarketingCustomerMapper.selectDiMarketingCustomerById(diMarketingNiche.getCustomerNo());
        if(Objects.isNull(diMarketingCustomer)){
            throw new RuntimeException("客户不存在");
        }

        //校验打开且是更新测试，过滤不同步
        if(1 == u9IgnoreConfig.getFlag() && diMarketingCustomer.getCustomerNo().equals(u9IgnoreConfig.getCus())){
            createU9SoResult.setStatus("1");
            return createU9SoResult;
        }

        String customerName  = getCustomerName(diMarketingCustomer);

        /*if(StringUtils.isEmpty(customerName)){
            throw new RuntimeException("查询/创建客户失败，请检查");
        }*/


        DiPreSaleQuote diPreSaleQuoteT = diPreSaleQuoteMapper.selectOne(Wrappers.<DiPreSaleQuote>lambdaQuery().eq(DiPreSaleQuote::getPreSaleQuoteCode, diContract.getPreSaleQuoteNo()));

        //分配清单---待修改
        List<DiPreSaleQuoteShare> diPreSaleQuoteShares = diPreSaleQuoteShareMapper.selectList(Wrappers.<DiPreSaleQuoteShare>lambdaQuery().eq(DiPreSaleQuoteShare::getPreSaleQuoteId, diPreSaleQuoteT.getId()).eq(DiPreSaleQuoteShare::getDelFlag,0));


        Map<String, String> userNameMap = Maps.newHashMap();
        List<String> list = new ArrayList<>();
        list.add(diMarketingNiche.getCreateBy());

        if(CollectionUtil.isNotEmpty(diPreSaleQuoteShares)){
            list.addAll(diPreSaleQuoteShares.stream().map(DiPreSaleQuoteShare::getEmployee).collect(Collectors.toList()));
        }


        if(StringUtils.isNotBlank(diMarketingNiche.getNicheOwner())){
            list.add(diMarketingNiche.getNicheOwner());
        }

        R<List<DdUserDTO>> userListResult = remoteDbcService.queryDdUserList(QueryDdUserDTO.builder().jobNumberList(new ArrayList<>(list)).build());
        if (userListResult.isSuccess()) {
            userNameMap = userListResult.getData().stream().collect(Collectors.toMap(DdUserDTO::getJobNumber, DdUserDTO::getName));
            diMarketingNiche.setNicheOwnerName(userNameMap.get(diMarketingNiche.getNicheOwner()));
        }

        BigDecimal shareAmount = BigDecimal.ZERO;
        List<CreateU9SoRequest.BusinessSaleDTO> shareSaleList = new ArrayList<>();
        Map<String, List<DiPreSaleQuoteShare>> preSaleQutoMap = new HashMap<>();
        if(CollectionUtil.isNotEmpty(diPreSaleQuoteShares)){
            preSaleQutoMap = diPreSaleQuoteShares.stream().filter(diPreSaleQuoteShare -> StringUtils.isNotEmpty(diPreSaleQuoteShare.getEmployee())).collect(Collectors.groupingBy(DiPreSaleQuoteShare::getEmployee));
        }

        StringBuilder sb = new StringBuilder();

        Set<String> deptCodes = new HashSet<>();
        int i = 1;
        for(DiPreSaleQuoteShare diPreSaleQuoteShareT:diPreSaleQuoteShares){


            if(i > 3){
                break;
            }
            List<DiPreSaleQuoteShare> diPreSaleQuoteSharesT = preSaleQutoMap.get(diPreSaleQuoteShareT.getEmployee());


            String name = userNameMap.get(diPreSaleQuoteSharesT.get(0).getEmployee());

            //查询业务员
            R<List<ErpSaleResponse>> saleR = remoteExchangeService.selectSale(name);
            if(saleR.isSuccess()){
                if(CollectionUtil.isEmpty(saleR.getData())){
                    sb.append(name).append(",");
                }else{
                    deptCodes.add(saleR.getData().get(0).getDeptCode());
                }
            }

            CreateU9SoRequest.BusinessSaleDTO businessSaleDTO = new CreateU9SoRequest.BusinessSaleDTO();
            businessSaleDTO.setBusinessSale(name.split("-")[0]);
            //businessSaleDTO.setBusinessSale("汪光成");
            BigDecimal amount = BigDecimal.ZERO;
            for(DiPreSaleQuoteShare diPreSaleQuoteShare:diPreSaleQuoteSharesT){
                amount = amount.add(diContract.getTotalAmount().multiply(diPreSaleQuoteShare.getSplitRatio().divide(new BigDecimal("100")))) ;
            }

            shareAmount = shareAmount.add(amount);
            businessSaleDTO.setBusinessYj(String.valueOf(amount));
            shareSaleList.add(businessSaleDTO);
            i++;
        }

        if(shareAmount.compareTo(diContract.getTotalAmount()) < 0){
            int k = 0;
            for(CreateU9SoRequest.BusinessSaleDTO businessSaleDTO:shareSaleList){
                if(k == 0){
                    businessSaleDTO.setBusinessYj(String.valueOf(new BigDecimal(businessSaleDTO.getBusinessYj()).add(diContract.getTotalAmount().subtract(shareAmount))));
                    break;
                }
                k++;
            }
        }

        if(CollectionUtil.isEmpty(shareSaleList)){
            CreateU9SoRequest.BusinessSaleDTO businessSaleDTO = new CreateU9SoRequest.BusinessSaleDTO();
            businessSaleDTO.setBusinessSale(diMarketingNiche.getNicheOwnerName().split("-")[0]);
            //businessSaleDTO.setBusinessSale("汪光成");
            businessSaleDTO.setBusinessYj(String.valueOf(diContract.getTotalAmount()));
            shareSaleList.add(businessSaleDTO);
        }

        //有业务员不存在或者业务员分属不同部门
        if(StringUtils.isNotEmpty(sb.toString()) || deptCodes.size() > 1 ){
            createU9So.setBusinessSaleList(null);
        }else{
            createU9So.setBusinessSaleList(shareSaleList);
        }



        //产品方案编码
        List<DiPreSaleQuoteDetail> preSaleQuoteDetails = diPreSaleQuoteDetailMapper.selectList(Wrappers.<DiPreSaleQuoteDetail>lambdaQuery().eq(DiPreSaleQuoteDetail::getPreSaleQuoteId, diPreSaleQuoteT.getId()));
        List<String> preCodes = preSaleQuoteDetails.stream().map(DiPreSaleQuoteDetail::getPreSaleCode).collect(Collectors.toList());

        //报价单方案map
        Map<String, DiPreSaleQuoteDetail> diPreSaleQuoteDetailMap = preSaleQuoteDetails.stream().collect(Collectors.toMap(DiPreSaleQuoteDetail::getPreSaleCode, Function.identity()));
        List<DiPreSale> diPreSales = new ArrayList<>();
        String preSaleType = "";
        //过滤贸易类
        List<DiPreSale> diPreSalesM = diPreSaleMapper.selectList(Wrappers.<DiPreSale>lambdaQuery().in(DiPreSale::getPreSaleCode, preCodes));
        List<DiPreSale> diPreSalesFilter = diPreSalesM.stream().filter(diPreSale -> diPreSale.getPreSaleType() == 3).collect(Collectors.toList());
        //全是贸易类
        if(CollectionUtil.isNotEmpty(diPreSalesFilter) && diPreSalesFilter.size() == diPreSalesM.size()){
            preSaleType = "3";
        }

        for(String preCode:preCodes) {
            List<DiPreSale> diPreSalesT = diPreSaleMapper.selectList(Wrappers.<DiPreSale>lambdaQuery().in(DiPreSale::getPreSaleCode, preCode));
            if(CollectionUtil.isEmpty(diPreSalesT)){
                throw new RuntimeException("无效产品方案数据");
            }

            List<Integer> preSaleTypes = diPreSalesT.stream().map(DiPreSale::getPreSaleType).collect(Collectors.toList());
            /*if(preSaleTypes.contains(3)){
                preSaleType = "3";
            }
           */
            //出口类订单但是产品方案包含标品或非标，就是项目类
            if("2".equals(orderType) && (preSaleTypes.contains(1) || preSaleTypes.contains(2) || preSaleTypes.contains(4))){
                orderType = "0";
            }
            diPreSales.add(diPreSalesT.stream().sorted(Comparator.comparing(DiPreSale::getVersion).reversed()).collect(Collectors.toList()).get(0));
        }

        if("1".equals(orderType) ){
            if(!orgs.contains(org)){
                throw new RuntimeException("散件类不能在"+org+"建单");
            }

        }else if("2".equals(orderType)){
            if(!"01".equals(org)){
                throw new RuntimeException("出口类不能在"+org+"建单");
            }

        }else if("3".equals(orderType)){


            if(!orgs.contains(org)){
                throw new RuntimeException("备件类不能在"+org+"建单");
            }

        }else if("4".equals(orderType)){
            if(!orgs.contains(org)){
                throw new RuntimeException("服务类不能在"+org+"建单");
            }

        }

        //物料
        List<DiPreSaleManifest> diPreSaleManifests = diPreSaleManifestMapper.selectList(Wrappers.<DiPreSaleManifest>lambdaQuery().in(DiPreSaleManifest::getDiPreSaleId, diPreSales.stream().map(DiPreSale::getId).collect(Collectors.toList())).eq(DiPreSaleManifest::getDelFlag,0));


        //报价单
        DiPreSaleQuote diPreSaleQuote = diPreSaleQuoteMapper.selectById(diContract.getPreSaleQuoteId());
        //料品
        List<CreateU9SoRequest.ItemInfo> itemInfos = new ArrayList<>();

        //物料号为key
        Map<Long, DiMateriel> materielMap = diMaterielMapper.selectList(Wrappers.<DiMateriel>lambdaQuery().in(DiMateriel::getId, diPreSaleManifests.stream().map(DiPreSaleManifest::getMaterialVersion).collect(Collectors.toList()))).stream().collect(Collectors.toMap(DiMateriel::getId, Function.identity()));

        Map<Long, DiPreSale> preSaleMap = diPreSales.stream().collect(Collectors.toMap(DiPreSale::getId, Function.identity()));

        //贸易类订单处理
        Map<Long,List<DiPreSaleManifest>> tradeOrderMap = new HashMap();
        diPreSaleManifests.stream().collect(Collectors.groupingBy(DiPreSaleManifest::getDiPreSaleId)).forEach((key,value) ->{
            List<DiPreSaleManifest> diPreSaleManifestList = value.stream().filter(diPreSaleManifest -> Objects.nonNull(diPreSaleManifest.getUseNum())).collect(Collectors.toList());
            if(CollectionUtil.isNotEmpty(diPreSaleManifestList)) {
                tradeOrderMap.put(key, diPreSaleManifestList);
            }
        });

        //贸易类订单处理
        Map<Long,Integer> tradeOrderOldMap = new HashMap();
        diPreSaleManifests.stream().collect(Collectors.groupingBy(DiPreSaleManifest::getDiPreSaleId)).forEach((key,value) ->{
            List<DiPreSaleManifest> diPreSaleManifestList = value.stream().filter(diPreSaleManifest -> Objects.nonNull(diPreSaleManifest.getUseNum())).collect(Collectors.toList());
            if(CollectionUtil.isNotEmpty(diPreSaleManifestList)) {
                Integer count = diPreSaleManifestList.stream().map(DiPreSaleManifest::getUseNum).reduce(Integer::sum).get();
                tradeOrderOldMap.put(key, count);
            }
        });
        for(DiPreSaleManifest diPreSaleManifest:diPreSaleManifests){
            DiPreSale diPreSale = preSaleMap.get(diPreSaleManifest.getDiPreSaleId());
            CreateU9SoRequest.ItemInfo itemInfo = new CreateU9SoRequest.ItemInfo();

            //出口的贸易类订单取值方案清单
            if( diPreSale.getPreSaleType() == 3){
                itemInfo.setItemQty(String.valueOf(diPreSaleManifest.getUseNum()));
                if(Objects.nonNull(diPreSaleManifest.getQuoteTotal()) && BigDecimal.ZERO.compareTo(new BigDecimal(itemInfo.getItemQty())) != 0){
                    itemInfo.setItemPrice(String.valueOf(diPreSaleManifest.getQuoteTotal().divide(new BigDecimal(itemInfo.getItemQty()),4,RoundingMode.HALF_UP).setScale(4,RoundingMode.HALF_UP)));
                }else{
                    DiPreSaleQuoteDetail diPreSaleQuoteDetail = diPreSaleQuoteDetailMap.get(diPreSale.getPreSaleCode());
                    if(Objects.nonNull(diPreSaleQuoteDetail.getSaleQuote()) && Objects.nonNull(tradeOrderMap.get(diPreSale.getId())) && BigDecimal.ZERO.compareTo(new BigDecimal(tradeOrderOldMap.get(diPreSale.getId()))) != 0){
                        itemInfo.setItemPrice(String.valueOf(diPreSaleQuoteDetail.getSaleQuote().divide(new BigDecimal(tradeOrderOldMap.get(diPreSale.getId())),4,RoundingMode.HALF_UP).setScale(4,RoundingMode.HALF_UP)));
                    }
                }

            }else{
                itemInfo.setItemQty(String.valueOf(diPreSale.getNum()));
                DiPreSaleQuoteDetail diPreSaleQuoteDetail = diPreSaleQuoteDetailMap.get(diPreSale.getPreSaleCode());
                if(Objects.nonNull(diPreSale.getNum()) && diPreSale.getNum() != 0 && Objects.nonNull(diPreSaleQuoteDetail.getSaleQuote())) {
                    itemInfo.setItemPrice(String.valueOf(diPreSaleQuoteDetail.getSaleQuote().divide(new BigDecimal(itemInfo.getItemQty()),4,RoundingMode.HALF_UP).setScale(4,RoundingMode.HALF_UP)));
                }
            }


            itemInfo.setCustShipToAddress(diPreSaleQuoteT.getCountryName()+diPreSaleQuoteT.getProvinceName()+diPreSaleQuoteT.getCityName()+diPreSaleQuoteT.getAreaName()+diPreSaleQuoteT.getCommonDeliveryAddress());
            itemInfo.setCustShipToLinker(diPreSaleQuote.getContactsName()+diPreSaleQuote.getContactsPhone());
            if(Objects.nonNull(materielMap.get(diPreSaleManifest.getMaterialVersion()))) {
                itemInfo.setRemark(materielMap.get(diPreSaleManifest.getMaterialVersion()).getPatternNo());
                itemInfo.setItemCode(materielMap.get(diPreSaleManifest.getMaterialVersion()).getMaterielNo());
            }

            itemInfo.setMaterielVersionId(diPreSaleManifest.getMaterialVersion());

            itemInfos.add(itemInfo);
        }
        //料品信息
        createU9So.setItemInfoList(itemInfos);

        //供应来源非01组织
        // 0 供应商
        //1	内部组织
        //2	关系企业
        //3	转生产制造
        //4	当前组织

        //供应类型
        // 0 转当前组织采购
        //1	跨组织出货
        //2	转跨组织采购
        //3	转制令
        //4	当前组织出货
        //5	转外协
        if(!org.equals(OrgCodeEnum.DAIDING_GONGYE.type())){
            //内部组织
            createU9So.setSupplySource("1");
            createU9So.setSupplyType("1");
        }else{
            createU9So.setSupplySource("4");
            createU9So.setSupplyType("4");
        }
        //组织ID
        createU9So.setShiperOrg("1001910080000367");
        //供应组织
        createU9So.setSupplyOrg("01");

        //客户
        createU9So.setCustName(customerName);
        createU9So.setCustTitle(createU9So.getCustName());
        //createU9So.setCustName("山东碧海包装材料有限公司");


        if("1".equals(orderType)){

            //单据类型
            createU9So.setOrderType("散件类销售订单");
            createU9So.setDemandType(2314);
            //订单分类I级
            createU9So.setOrderCateOne("06");
            //订单分类II级
            createU9So.setOrderCateTwo("0607");

            /*if(Objects.nonNull(items) && CollectionUtil.isNotEmpty(items.getData())){


                jdyErpService.getCustomerName(contractDetail.getFirstPartyName(),createU9So.getOrgCode());

            }*/

        }else if("2".equals(orderType)){
            createU9So.setOrderType("出口类销售订单");
            createU9So.setDemandType(2843);
            //订单分类I级
            createU9So.setOrderCateOne("07");
            //订单分类II级
            createU9So.setOrderCateTwo("07");
            //美元
            //createU9So.setDollar(dataList.get(0).get_widget_1651569306430());

            //客户
            createU9So.setCustName("上海外综服国际贸易有限公司");

            //国家
            //createU9So.setCountry(customerDataList.get(0).get_widget_1647244406261());


        }else if("3".equals(orderType)){


            createU9So.setDemandType(2315);
            createU9So.setOrderType("备件类销售订单");

            //订单分类I级
            createU9So.setOrderCateOne("04");

        }else if("4".equals(orderType)){
            //单据类型
            createU9So.setOrderType("服务类销售订单");
            createU9So.setDemandType(3776);
            //订单分类I级
            createU9So.setOrderCateOne("05");
            //订单分类II级
            createU9So.setOrderCateTwo("0501");

        } else if("0".equals(orderType) || "5".equals(orderType) || "6".equals(orderType)){
            //查询项目主档是否存在
            R<Integer> conutR = remoteExchangeService.selectProjMaster(projectNo);

            //是否创建主档,不创建就只销售立项
            boolean flag = false;
            if(conutR.isSuccess() && conutR.getData() == 0) {
                flag = true;
            }
            //不存在创建主档
            ErpProjMasterRequest.Context contextT = ErpProjMasterRequest.Context.builder()
                    .CultureName("zh-CN")
                    .EntCode(erpConfig.getEntCode())
                    .OrgCode("01")
                    .UserCode("admin")
                    .build();

            //项目经理
            String projectManeger = "";
            //查询项目
            DiProcessProject diProcessProject = diProcessProjectMapper.selectOne(Wrappers.<DiProcessProject>lambdaQuery().eq(DiProcessProject::getProjectNo, projectNo));
            if(Objects.nonNull(diProcessProject) && StringUtils.isNotEmpty(diProcessProject.getProjectManagerUserId())){
                QueryDdUserDTO queryDdUserDTO = QueryDdUserDTO.builder().jobNumberList(Arrays.asList(diProcessProject.getProjectManagerUserId())).build();
                R<List<DdUserDTO>> ddUserListR = remoteDbcService.queryDdUserList(queryDdUserDTO);
                if(ddUserListR.isSuccess() && CollectionUtil.isNotEmpty(ddUserListR.getData())){
                    projectManeger = ddUserListR.getData().get(0).getName();
                }
            }

            if(StringUtils.isEmpty(projectManeger)){
                projectManeger = "赵国顺";
            }
            if(StringUtils.isEmpty(projectManeger)){
                throw new RuntimeException("无项目经理");
            }
            ErpProjMasterRequest.RequestDTO requestDTO = ErpProjMasterRequest.RequestDTO.builder().ProjectCode(projectNo)
                    .ProjectManager(projectManeger).OrgCodeList(Arrays.asList("00","001","02","03","04","05","06","07","08","09","10"))
                    .OrgCode("01").build();

            ErpProjMasterRequest projMasterRequest = ErpProjMasterRequest.builder().context(contextT).requestDTO(requestDTO).build();

            if(flag) {
                DiSynErpLog diSynErpLog = new DiSynErpLog();
                diSynErpLog.setRequestParam(JSON.toJSONString(projMasterRequest));
                //创建主档，不执行销售立项
                ErpProjMasterResponse erpProjMasterResponse = erpClient.erpCall(projMasterRequest);
                diSynErpLog.setResponseParam(JSON.toJSONString(erpProjMasterResponse));
                if (!erpProjMasterResponse.getD().getFlag()) {
                    diSynErpLog.setStatus(1);
                    diSynErpLogMapper.insert(diSynErpLog);
                    throw new RuntimeException("项目主档创建失败:" + erpProjMasterResponse.getD().getErrMsg());
                }
                diSynErpLogMapper.insert(diSynErpLog);
            }

            //单据类型
            createU9So.setOrderType("项目类销售订单");

            //项目编码
            createU9So.setProjectCode(projectNo);


           // String eValue = "";

            //需求分类
            R<String> demandType = remoteExchangeService.selectExtEnum("需求分类", projectNo,null);
            if(demandType.isSuccess() && StringUtils.isNotEmpty(demandType.getData())){
                createU9So.setDemandType(Integer.parseInt(demandType.getData()));
               // eValue = demandType.getData();
            }else{

                //查询目前最大的一个
                R<String> eValueMax = remoteExchangeService.selectExtEnum("需求分类", "", "");
                int eValuePara = Integer.parseInt(eValueMax.getData())+1;
                createU9So.setEValue(eValuePara);
                //eValue = String.valueOf(eValuePara);
                ErpCreateDemandTypeRequest.Context demandTypeContext = ErpCreateDemandTypeRequest.Context.builder()
                        .CultureName("zh-CN")
                        .EntCode(erpConfig.getEntCode())
                        .OrgCode(org)
                        .UserCode("admin")
                        .build();

                ErpCreateDemandTypeRequest.RequestDTO requestD = ErpCreateDemandTypeRequest.RequestDTO.builder().ProjectCode(projectNo).EValue(eValuePara).build();
                DiSynErpLog diSynErpLog = new DiSynErpLog();
                diSynErpLog.setRequestParam(JSON.toJSONString(ErpCreateDemandTypeRequest.builder().context(demandTypeContext).requestDTO(requestD).build()));
                ErpCreateDemandTypeResponse erpCreateDemandTypeResponse = erpClient.erpCall(ErpCreateDemandTypeRequest.builder().context(demandTypeContext).requestDTO(requestD).build());
                diSynErpLog.setResponseParam(JSON.toJSONString(erpCreateDemandTypeResponse));
                if(!erpCreateDemandTypeResponse.getD().getFlag()){
                    diSynErpLog.setStatus(1);
                    diSynErpLogMapper.insert(diSynErpLog);
                    throw new RuntimeException("失败：创建需求分类报错:"+erpCreateDemandTypeResponse.getD().getMsg());
                }
                diSynErpLogMapper.insert(diSynErpLog);
                createU9So.setDemandType(eValuePara);
            }




            if(!checkEvalueIsCreate(projectNo,0)){
                throw new RuntimeException("失败：需求分类未创建报错");
            }
        }

        for(CreateU9SoRequest.ItemInfo itemInfo:itemInfos){
            try {
                //查询物料u9是否存在
                R<Integer> itemCount = remoteExchangeService.selectItemCount(itemInfo.getItemCode());

                //是否存在bom
                boolean isExistBom = true;
                //查询bom
                R<Integer> bomCount = remoteExchangeService.queryBomMaster(itemInfo.getItemCode());
                if(bomCount.isSuccess() && bomCount.getData() == 0){
                    isExistBom = false;
                }
                MaterielBomSynRequest materielBomVersionMqRequest = new MaterielBomSynRequest();
                materielBomVersionMqRequest.setMaterielVersionId(itemInfo.getMaterielVersionId());
                materielBomVersionMqRequest.setMaterielNo(itemInfo.getItemCode());
                   /* if(itemInfo.getItemCode().startsWith("901")){
                        materielBomVersionMqRequest.setProjectCode(projectNo);
                    }*/
                //母料不存在
                if (itemCount.isSuccess() && itemCount.getData() == 0) {
                    materielBomVersionMqRequest.setCreateBom(true);
                    materielBomVersionMqRequest.setCreateItem(true);
                    remoteExchangeService.synItemAndBomToU9(materielBomVersionMqRequest);
                }else if(!isExistBom && !itemInfo.getItemCode().startsWith("9")){ //母料存在，bom不存在且不是非标
                    materielBomVersionMqRequest.setCreateBom(true);
                    remoteExchangeService.synItemAndBomToU9(materielBomVersionMqRequest);
                }
            }catch (Exception e){
                log.error("同步u9物料和bom报错，不影响建单{}",e.getMessage());
            }
        }

        DiSynErpLog diSynErpLog = new DiSynErpLog();
        diSynErpLog.setRequestParam(JSON.toJSONString(CreateU9SoRequest.builder().context(context).requestDTO(createU9So).build()));
        CreateU9SoResponse createU9SoResponse = erpClient.erpCall(CreateU9SoRequest.builder().context(context).requestDTO(createU9So).build());
        diSynErpLog.setResponseParam(JSON.toJSONString(createU9SoResponse));
        if(!createU9SoResponse.getD().getFlag()){
            diSynErpLog.setStatus(1);
            diSynErpLogMapper.insert(diSynErpLog);
            createU9SoResult.setStatus("0");
            dingtalkClient.sendText(Arrays.asList("16853222162771202"),"建单失败:合同号:"+contractCode);

            return createU9SoResult;
        }
        diSynErpLogMapper.insert(diSynErpLog);

        String docNo = "";
        //根据项目号查询
        docNo = checkOrderIsCreate(diContract.getContractNo(), 1);
        if (StringUtils.isEmpty(docNo)) {
            throw new RuntimeException("失败：建单报错:订单返回成功，但未创建"+diContract.getContractNo());
        }

        //有业务员不存在或者业务员分属不同部门
        if(StringUtils.isNotEmpty(sb.toString()) || deptCodes.size() > 1 ){

            String value = "";

            if(StringUtils.isNotEmpty(sb.toString())){
                value = sb.toString()+"查不到业务员,请到u9手动补充销售人员业绩;";
            }
            if(deptCodes.size() > 1){
                value = value +" 业务员不属于同一个部门,请到u9手动补充销售人员业绩;";
            }
            dingtalkClient.sendGroupText("建单出现问题,单号:"+docNo,"建单出现问题,单号:"+docNo+","+value,"cidgqr4/ENLi3nFqwV0ZhR08w==",dingtalkU9Config.getAppKey(),dingtalkU9Config.getAppSecret());

        }

        log.info("同步u9结束,合同号{},单号{}",diContract.getContractNo(),docNo);


        if(!Arrays.asList("0","5","6").contains(orderType)) {
            createU9SoResult.setStatus("1");
            return createU9SoResult;
        }

        //不存在创建主档
        ErpCreateProjectSoSvRequest.Context contextTT = ErpCreateProjectSoSvRequest.Context.builder()
                .CultureName("zh-CN")
                .EntCode(erpConfig.getEntCode())
                .OrgCode("01")
                .UserCode("admin")
                .build();

        ErpCreateProjectSoSvRequest.RequestDTO requestT = ErpCreateProjectSoSvRequest.RequestDTO.builder().ProjectCode(projectNo)
                .SoOrderNo(docNo)
                .OrgCode("01").build();

        ErpCreateProjectSoSvRequest projectSoRequest = ErpCreateProjectSoSvRequest.builder().context(contextTT).requestDTO(requestT).build();
        DiSynErpLog diSynErpLogTT = new DiSynErpLog();
        diSynErpLogTT.setRequestParam(JSON.toJSONString(projectSoRequest));
        //执行销售立项
        ErpCreateProjectSoSvResponse erpCreateProjectSoSvResponse = erpClient.erpCall(projectSoRequest);
        diSynErpLogTT.setResponseParam(JSON.toJSONString(erpCreateProjectSoSvResponse));
        if(!erpCreateProjectSoSvResponse.getD().getFlag()){
            diSynErpLogTT.setStatus(1);
            diSynErpLogMapper.insert(diSynErpLogTT);
            createU9SoResult.setOrderNo(docNo);
            createU9SoResult.setStatus("0");
            createU9SoResult.setMsg("项目主档创建失败:"+erpCreateProjectSoSvResponse.getD().getErrMsg());
            return createU9SoResult;
        }
        diSynErpLogMapper.insert(diSynErpLogTT);
        /*else{
            R<String> orderNo = remoteExchangeService.selectSmSoOne(null,contractCode);
            docNo = orderNo.getData();
        }*/


        // 创建标准销售--分期收款计划
        // 公共参数
        CreateProjectRecBillRequest.Context contextRecBill = CreateProjectRecBillRequest.Context.builder()
                .CultureName("zh-CN")
                .EntCode(erpConfig.getEntCode())
                .OrgCode(org)
                .UserCode("admin")
                .build();

        SysDictData sysDictData = new SysDictData();
        sysDictData.setDictType("sale_contract_clause");
        sysDictData.setDictValue(createU9So.getOrderTerm());
        //标签值
        String dictLabel = remoteDictDataService.selectDictLabel(sysDictData);

        //查询收款计划模版
        R<String> templateR = remoteExchangeService.selectInstalmentRecScheduleTemplate(dictLabel);
        if(templateR.isError() || StringUtils.isEmpty(templateR.getData())){
            throw new RuntimeException("收款计划模版未配置,模版名称:"+dictLabel);
        }


        List<DiPreSaleQuotePeriod> diPreSaleQuotePeriods = diPreSaleQuotePeriodMapper.selectList(Wrappers.<DiPreSaleQuotePeriod>lambdaQuery().eq(DiPreSaleQuotePeriod::getDiPreSaleQuoteId, diPreSaleQuote.getId()));
        List<CreateProjectRecBillRequest.RecBillPlanDate> recBillPlanDates = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(diPreSaleQuotePeriods)){
            for(DiPreSaleQuotePeriod diPreSaleQuotePeriod:diPreSaleQuotePeriods){
                CreateProjectRecBillRequest.RecBillPlanDate recBillPlanDate = new CreateProjectRecBillRequest.RecBillPlanDate();
                recBillPlanDate.setInstalmentType(diPreSaleQuotePeriod.getRemark());
                if(Objects.nonNull(diPreSaleQuotePeriod.getPlanPayDate())){
                    recBillPlanDate.setPlanDate(diPreSaleQuotePeriod.getPlanPayDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                }else{
                    recBillPlanDate.setPlanDate(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                }
                recBillPlanDates.add(recBillPlanDate);
            }
        }



        CreateProjectRecBillRequest.CreateProjectRecBill recBill = CreateProjectRecBillRequest.CreateProjectRecBill.builder().SODocNo(docNo).OrgCode(org).TemplateCode(templateR.getData()).ProjectCode(projectNo).RecBillPlanDate(recBillPlanDates).build();

        CreateProjectRecBillRequest recBillRequest = CreateProjectRecBillRequest.builder().context(contextRecBill).requestDTO(recBill).build();

        DiSynErpLog diSynErpLogT = new DiSynErpLog();
        diSynErpLogT.setRequestParam(JSON.toJSONString(recBillRequest));
        CreateProjectRecBillResponse createProjectRecBillResponse = erpClient.erpCall(recBillRequest);
        diSynErpLogT.setResponseParam(JSON.toJSONString(createProjectRecBillResponse));
        diSynErpLogT.setProjectNo(recBillRequest.getRequestDTO().getSODocNo());
        if(!createProjectRecBillResponse.getD().getFlag()){
            diSynErpLogT.setStatus(1);
            diSynErpLogMapper.insert(diSynErpLogT);
            createU9SoResult.setOrderNo(docNo);
            createU9SoResult.setMsg("失败:分期收款计划:"+createProjectRecBillResponse.getD().getErrmsg());
            return createU9SoResult;
        }
        diSynErpLogMapper.insert(diSynErpLogT);



        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        //订单id
        R<String> orderIdR = remoteExchangeService.selectSSId(docNo);
        //提交订单
        ErpSoTransferRequest.Context contextSoTransfer = ErpSoTransferRequest.Context.builder()
                .CultureName("zh-CN")
                .EntCode(erpConfig.getEntCode())
                .OrgCode(org)
                .UserCode("admin")
                .build();
        ErpSoTransferRequest.RequestDTO build = ErpSoTransferRequest.RequestDTO.builder().SoId(orderIdR.getData()).build();

        DiSynErpLog diSynErpLogTmp = new DiSynErpLog();
        diSynErpLogTmp.setRequestParam(JSON.toJSONString(build));
        ErpSoTransferResponse erpSoTransferResponse = erpClient.erpCall(ErpSoTransferRequest.builder().context(contextSoTransfer).requestDTO(build).build());
        diSynErpLogTmp.setResponseParam(JSON.toJSONString(erpSoTransferResponse));
        diSynErpLogTmp.setStatus(0);
        if(!erpSoTransferResponse.getD().getFlag()){
            diSynErpLogTmp.setStatus(1);
            diSynErpLogMapper.insert(diSynErpLogTmp);
            createU9SoResult.setOrderNo(docNo);
            createU9SoResult.setStatus("失败");
            createU9SoResult.setMsg("订单提交失败:"+erpSoTransferResponse.getD().getMsg());
            return createU9SoResult;
        }
        diSynErpLogMapper.insert(diSynErpLogTmp);
        createU9SoResult.setStatus("1");
         return createU9SoResult;
    }


    /**
     * 生成客户编码
     * @return
     */
    public String createCustCode(){
        int s = (int)((Math.random()*9+1)*10000);
        String uCode = "CU-"+s;
        R<JdyCustomerExchange> cunstomerByCode = remoteExchangeService.getCunstomerByCode(uCode);
        if(cunstomerByCode.isSuccess() && Objects.nonNull(cunstomerByCode.getData())){
            createCustCode();
        }
        return uCode;
    }



    public void synCusToU9(String customerCode){

        if(StringUtils.isNotEmpty(customerCode)){
            DiMarketingCustomer diMarketingCustomer = diMarketingCustomerMapper.selectOne(Wrappers.<DiMarketingCustomer>lambdaQuery().eq(DiMarketingCustomer::getCustomerNo, customerCode));
            if(Objects.nonNull(diMarketingCustomer)){
                getCustomerName(diMarketingCustomer);
            }
        }else{
            List<DiMarketingCustomer> diMarketingCustomers = diMarketingCustomerMapper.selectList(Wrappers.<DiMarketingCustomer>lambdaQuery().eq(DiMarketingCustomer::getDelFlag, 0));
            if(CollectionUtil.isNotEmpty(diMarketingCustomers)){
                for(DiMarketingCustomer diMarketingCustomer:diMarketingCustomers){
                    getCustomerName(diMarketingCustomer);
                }
            }
        }

    }

    public String getCustomerName(DiMarketingCustomer diMarketingCustomer){
        //查询客户是否存在
        //获取客户编号
        R<JdyCustomerExchange> cunstomerByCode = remoteExchangeService.getCunstomerByCode(diMarketingCustomer.getCustomerNo());
        String uName = "";
        if (cunstomerByCode.isSuccess() && Objects.nonNull(cunstomerByCode.getData())) {
            uName = cunstomerByCode.getData().getUName();
        } else {

            SysDictData sysDictData = new SysDictData();
            sysDictData.setDictType("industry_classification");
            sysDictData.setDictValue(StringUtils.isNotEmpty(diMarketingCustomer.getIndustryClassification())?diMarketingCustomer.getIndustryClassification().split(",")[0]:"0");
            //标签值
            String dictLabel = remoteDictDataService.selectDictLabel(sysDictData);
            //获取行业性质code
            R<String> tradeCategoryValueR = remoteExchangeService.selectExtEnum("行业",null,dictLabel);
            String tradeCategoryValue = null;
            if(tradeCategoryValueR.isSuccess() && StringUtils.isNotEmpty(tradeCategoryValueR.getData())){
                tradeCategoryValue = tradeCategoryValueR.getData();
            }

            //获取客户性质code
            String descFlexFieldCode = "";
            SysDictData sysDictDataT = new SysDictData();
            sysDictDataT.setDictType("customer_nature");
            sysDictDataT.setDictValue(StringUtils.isNotEmpty(diMarketingCustomer.getIndustryClassification())?diMarketingCustomer.getIndustryClassification().split(",")[0]:"燃烧行业");
            //标签值
            String dictLabelT = remoteDictDataService.selectDictLabel(sysDictDataT);
            R<BaseDefineValueResponse> baseDefineValueResponseR = remoteExchangeService.selectBaseDefineValue(1001910140118270L, dictLabelT);
            if(baseDefineValueResponseR.isSuccess() && Objects.nonNull(baseDefineValueResponseR.getData())){
                descFlexFieldCode = baseDefineValueResponseR.getData().getCode();
            }
            //客户编码
            String custCode = createCustCode();
            //公共参数
            ErpCreateCustRequest.Context context = ErpCreateCustRequest.Context.builder()
                    .CultureName("zh-CN")
                    .EntCode(erpConfig.getEntCode())
                    .OrgCode("00")
                    .UserCode("admin")
                    .build();

            ErpCreateCustRequest.RequestDTO requestDTO = ErpCreateCustRequest.RequestDTO.builder()
                    .CustCode(custCode)
                    .CustName(diMarketingCustomer.getCompanyName())
                    .CustShortName(diMarketingCustomer.getCompanyName())
                    .SearchCode(diMarketingCustomer.getCustomerNo())
                    .Category("0201")
                    .OrgCode("00")
                    .TradeCategory(StringUtils.isNotEmpty(tradeCategoryValue)?Integer.valueOf(tradeCategoryValue):0)
                    .PrivateDescSeg2(descFlexFieldCode)
                    .OrgCodeList(Arrays.asList( "01", "02", "03", "04", "05", "06", "07", "08", "09", "10"))
                    .EffiveDate(LocalDate.now().minusMonths(1).withDayOfMonth(1).toString()).build();

            ErpCreateCustRequest erpCreateCustRequest = ErpCreateCustRequest.builder().context(context).requestDTO(requestDTO).build();
            ErpCreateCustResponse erpCreateCustResponse = erpClient.erpCall(erpCreateCustRequest);
            if(Objects.isNull(erpCreateCustResponse) || !erpCreateCustResponse.getD().getFlag()){
                return null;
            }


            //保存成功之后在用crm中的客户code去u9取ucode
            return checkCustomerCreate(diMarketingCustomer.getCustomerNo(),1);
        }
        return uName;
    }

    /**
     * 校验订单是否创建
     * @param contactCode
     * @param i
     * @return
     */
    public String checkOrderIsCreate(String contactCode,Integer i){
        if(i > 5){
            return "";
        }
        //根据项目号查询
        R<String> docNo = remoteExchangeService.selectSmSoOne(null,contactCode);
        if(docNo.isSuccess() && StringUtils.isEmpty(docNo.getData())){
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            i++;
            checkOrderIsCreate(contactCode,i);
        }
        return docNo.getData();
    }

    /**
     * 是否创建需求分类
     * @return
     */
    public Boolean checkEvalueIsCreate(String projectCode,int i){
        if(i > 3){
            return false;
        }
        R<String> eValue = remoteExchangeService.selectExtEnum("需求分类", projectCode, "");
        if(eValue.isSuccess() && StringUtils.isEmpty(eValue.getData())){
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            i++;
            checkEvalueIsCreate(projectCode,i);
        }
        return true;
    }

    /**
     * 校验u9是否创建好项目主档
     * @param projectCode
     * @param i
     * @return
     */
    public Boolean checkProjectIsCreate(String projectCode,int i){

        if(i > 3){
            return false;
        }
        //查询项目主档是否存在
        R<Integer> countT = remoteExchangeService.selectProjMaster(projectCode);
        //u9还没保存
        if(countT.isSuccess() && countT.getData() == 0){
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            i++;
            checkProjectIsCreate(projectCode,i);
        }
        return true;
    }

    /*public String getCustomerName(String customerCode,String org){
        //查询客户是否存在
        //获取客户编号
        R<JdyCustomerExchange> jdyCustomer = remoteExchangeService.getCunstomerByCode(customerCode);
        String uName = "";
        if (jdyCustomer.isSuccess() && Objects.nonNull(jdyCustomer.getData())) {
            uName = jdyCustomer.getData().getUName();
        } else {

            R<List<SysDictData>> industryClassification = remoteDictDataService.dictTypeNew("industry_classification");
            Map<String, SysDictData> industryClassificationMap = new HashMap<>();
            if( industryClassification.isSuccess() && Objects.nonNull(industryClassification.getData())){
                industryClassificationMap = industryClassification.getData().stream().collect(Collectors.toMap(SysDictData::getDictLabel, Function.identity()));
            }

            //获取行业性质code
            R<String> tradeCategoryValue = remoteExchangeService.selectExtEnum("行业",null,industryClassificationMap.get());
            //获取客户性质code
            String descFlexFieldCode = "";
            List<BaseDefineValueResponse> baseDefineValueResponses = erpMapper.selectBaseDefineValue("1001910140118270", customerDataResponse.getData().get(0).get_widget_1630862543415());
            if(CollectionUtil.isNotEmpty(baseDefineValueResponses)){
                descFlexFieldCode = baseDefineValueResponses.get(0).getCode();
            }
            //客户编码
            String custCode = createCustCode();
            //公共参数
            ErpCreateCustRequest.Context context = ErpCreateCustRequest.Context.builder()
                    .CultureName("zh-CN")
                    .EntCode(erpConfig.getEntCode())
                    .OrgCode("00")
                    .UserCode("admin")
                    .build();

            ErpCreateCustRequest.RequestDTO requestDTO = ErpCreateCustRequest.RequestDTO.builder()
                    .CustCode(custCode)
                    .CustName(customerDataResponse.getData().get(0).getAccount_name())
                    .CustShortName(customerDataResponse.getData().get(0).getAccount_name().substring(0, 5))
                    .SearchCode(customerDataResponse.getData().get(0).getAccount_no())
                    .Category("0201")
                    .OrgCode("00")
                    .TradeCategory(StringUtils.isNotEmpty(tradeCategoryValue)?Integer.valueOf(tradeCategoryValue):0)
                    .PrivateDescSeg2(descFlexFieldCode)
                    .OrgCodeList(Arrays.asList( "01", "02", "03", "04", "05", "06", "07", "08", "09", "10"))
                    .EffiveDate(LocalDate.now().minusMonths(1).withDayOfMonth(1).toString()).build();

            ErpCreateCustRequest erpCreateCustRequest = ErpCreateCustRequest.builder().context(context).requestDTO(requestDTO).build();
            ErpCreateCustResponse erpCreateCustResponse = erpClient.erpCall(erpCreateCustRequest);
            if(Objects.isNull(erpCreateCustResponse) || !erpCreateCustResponse.getD().getFlag()){
                return null;
            }


            //保存成功之后在用crm中的客户code去u9取ucode
            return checkCustomerCreate(customerCode,1);
        }
        return uName;
    }*/


    public String checkCustomerCreate(String customerCode,int i){

        if(i > 5){
            return null;
        }
        //保存成功之后在用crm中的客户code去u9取ucode
        R<JdyCustomerExchange> jdyCustomerNew = remoteExchangeService.getCunstomerByCode(customerCode);
        if(Objects.isNull(jdyCustomerNew)){
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            i++;
            checkCustomerCreate(customerCode,i);
        }
        return jdyCustomerNew.getData().getUName();
    }
}
