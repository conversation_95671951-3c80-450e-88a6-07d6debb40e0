package com.dyd.di.agency.controller;

import com.dyd.common.core.domain.R;
import com.dyd.common.core.exception.ServiceException;
import com.dyd.common.core.utils.PageWrapper;
import com.dyd.di.agency.constants.AgencyConstants;
import com.dyd.di.agency.domain.dto.ChangeLiabilityByDTO;
import com.dyd.di.agency.domain.dto.CommonDTO;
import com.dyd.di.agency.domain.dto.FindAgencyApprovalListDTO;
import com.dyd.di.agency.domain.vo.AgencyApprovalListVO;
import com.dyd.di.agency.service.DiAgencyApprovalService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 代办审批
 */
@RestController
@RequestMapping("/agencyApproval")
public class DiAgencyApprovalController {

    @Resource
    private DiAgencyApprovalService diAgencyApprovalService;

    /**
     * 查询代办审批列表(无分页)
     *
     * @param dto 查询条件
     * @return 结果
     */
    @PostMapping("/findAgencyApprovalList")
    public R<List<AgencyApprovalListVO>> findAgencyApprovalList(@RequestBody FindAgencyApprovalListDTO dto) {
        if(Objects.isNull(dto.getIsDeleted())){
            dto.setIsDeleted(0);
        }
        return R.ok(diAgencyApprovalService.findAgencyApprovalList(dto));
    }

    /**
     * 查询代办审批列表(分页)
     *
     * @param dto 查询条件
     * @return 结果
     */
    @PostMapping("/findAgencyApprovalListPage")
    public R<PageWrapper<List<AgencyApprovalListVO>>> findAgencyApprovalListPage(@RequestBody FindAgencyApprovalListDTO dto) {

        if(Objects.isNull(dto.getIsDeleted())){
            dto.setIsDeleted(0);
        }
        return R.ok(diAgencyApprovalService.findAgencyApprovalListPage(dto));
    }

    /**
     * 更新是否已读
     *
     * @param dto 更新参数
     */
    @PostMapping("/updateIsReadById")
    public R<Void> updateIsReadById(@Validated @RequestBody CommonDTO dto) {
        diAgencyApprovalService.updateIsReadById(dto);
        return R.ok();
    }

    /**
     * 删除代办审批
     *
     * @param dto 删除参数
     */
    @PostMapping("/deleteAgencyApproval")
    public R<Void> deleteAgencyApproval(@Validated @RequestBody CommonDTO dto) {
        diAgencyApprovalService.deleteAgencyApproval(dto);
        return R.ok();
    }


    /**
     * 变更审批责任人
     *
     * @param dto 变更参数
     */
    @PostMapping("/changeLiabilityBy")
    public R<String> changeLiabilityBy(@Validated @RequestBody ChangeLiabilityByDTO dto) {

        String result = diAgencyApprovalService.changeLiabilityBy(dto);
        if (StringUtils.isNotBlank(result)) {
            return R.fail(result);
        }
        return R.ok();

    }

}
