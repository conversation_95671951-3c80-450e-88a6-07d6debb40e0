package com.dyd.di.matrix.service.impl;

import com.dyd.di.matrix.dto.ComparisonGroupDTO;
import com.dyd.di.matrix.entity.DiMatrixTerritoryComparisonDetail;
import com.dyd.di.matrix.entity.DiMatrixTerritoryComparisonDimension;
import com.dyd.di.matrix.entity.DiMatrixTerritoryComparisonGroup;
import com.dyd.di.matrix.entity.DiMatrixTerritoryComparisonProduct;
import com.dyd.di.matrix.mapper.DiMatrixTerritoryComparisonGroupMapper;
import com.dyd.di.matrix.service.IDiMatrixTerritoryComparisonDetailService;
import com.dyd.di.matrix.service.IDiMatrixTerritoryComparisonDimensionService;
import com.dyd.di.matrix.service.IDiMatrixTerritoryComparisonGroupService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dyd.di.matrix.service.IDiMatrixTerritoryComparisonProductService;
import com.dydtec.infras.core.base.exception.BizValidException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * <p>
 * 业务版图对比分组 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
@Service
public class DiMatrixTerritoryComparisonGroupServiceImpl extends ServiceImpl<DiMatrixTerritoryComparisonGroupMapper, DiMatrixTerritoryComparisonGroup> implements IDiMatrixTerritoryComparisonGroupService {


    @Autowired
    private IDiMatrixTerritoryComparisonProductService productService;
    @Autowired
    private IDiMatrixTerritoryComparisonDimensionService dimensionService;
    @Autowired
    private IDiMatrixTerritoryComparisonDetailService detailService;

    /**
     * 保存对比分组
     *
     * @param territoryId
     * @param groupList
     */
    @Override
    public void saveComparison(Long territoryId, List<ComparisonGroupDTO> groupList) {

        if (CollectionUtils.isEmpty(groupList)) {
            this.cleanComparison(territoryId);
            return;
        }

        //数据校验
        for (ComparisonGroupDTO group : groupList) {
            if (StringUtils.isBlank(group.getGroupName())) {
                throw new BizValidException("分组名称不能为空");
            }
            if (CollectionUtils.isEmpty(group.getComparisonData())) {
                throw new BizValidException("对比数据不能为空");
            }
            //表头长度
            List<String> header = group.getComparisonData().get(0);
            if (header.size() < 3) {
                throw new BizValidException("分组[" + group.getGroupName() + "]对比产品至少需要2条");
            }
            //表头是否有重复
            Set<String> productNameSet = header.stream().skip(1).filter(StringUtils::isNotBlank).map(x -> x).collect(Collectors.toSet());
            if (productNameSet.size() != header.size() - 1) {
                throw new BizValidException("分组[" + group.getGroupName() + "]对比产品不能为空或者重复");
            }
            //获取二维数组的第一列作为 维度
            Set<String> dimensionList = group.getComparisonData().stream().map(x -> x.get(0))
                    .skip(1).filter(StringUtils::isNotBlank).collect(Collectors.toSet());

            if (dimensionList.isEmpty() || dimensionList.size() != group.getComparisonData().size() - 1) {
                throw new BizValidException("分组[" + group.getGroupName() + "]对比维度至少需要1条，且维度名称不能重复或者为空");
            }
            //校验二维数组中每个 值是否为空
            group.getComparisonData().stream().skip(1).forEach(x -> {
                if (x.size() != header.size()) {
                    throw new BizValidException("分组[" + group.getGroupName() + "]对比数据格式错误");
                }
                for (int i = 1; i < x.size(); i++) {
                    if (StringUtils.isBlank(x.get(i))) {
                        throw new BizValidException("分组[" + group.getGroupName() + "],维度[" + x.get(0) + "],产品[" + header.get(i) + "]对比数据格式错误");
                    }
                }
            });

        }

        List<DiMatrixTerritoryComparisonGroup> groups = this.queryByTerritoryId(territoryId);
        Map<String, Long> oldGroupNameMap = groups.stream().collect(Collectors.toMap(DiMatrixTerritoryComparisonGroup::getGroupName, DiMatrixTerritoryComparisonGroup::getId, (a, b) -> a));
        Set<String> newGroupName = groupList.stream().map(ComparisonGroupDTO::getGroupName).collect(Collectors.toSet());
        AtomicLong weight = new AtomicLong(0);
        groupList.forEach((x) -> {
            x.setGroupId(oldGroupNameMap.get(x.getGroupName()));
        });
        // 删除旧数据
        List<Long> delGroupIds = groups.stream().filter(x -> !newGroupName.contains(x.getGroupName())).map(DiMatrixTerritoryComparisonGroup::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(delGroupIds)) {
            this.removeBatchByIds(delGroupIds);
        }
        // 新增新分组
        weight.set(0);
        for (ComparisonGroupDTO group : groupList) {
            DiMatrixTerritoryComparisonGroup groupEntity = new DiMatrixTerritoryComparisonGroup();
            groupEntity.setTerritoryId(territoryId);
            groupEntity.setGroupName(group.getGroupName());
            groupEntity.setId(group.getGroupId());
            groupEntity.setWeight(weight.getAndIncrement());
            this.saveOrUpdate(groupEntity);
            group.setGroupId(groupEntity.getId());
        }

        // 保存维度信息
        for (ComparisonGroupDTO group : groupList) {
            List<DiMatrixTerritoryComparisonDimension> dimensionList = dimensionService.queryByTerritoryId(territoryId, Arrays.asList(group.getGroupId()));
            Map<String, Long> oldDimensionMap = dimensionList.stream().collect(Collectors.toMap((x) -> x.getDimensionName(), DiMatrixTerritoryComparisonDimension::getId, (a, b) -> a));
            Set<String> newDimensionName = group.getComparisonData().stream().skip(1).map(x -> x.get(0)).collect(Collectors.toSet());

            List<Long> delDimensionList = dimensionList.stream().filter(x -> !newDimensionName.contains(x.getDimensionName()))
                    .map(x -> x.getId()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(delDimensionList)) {
                dimensionService.removeBatchByIds(delDimensionList);
            }
            weight.set(0);
            for (List<String> dimension : group.getComparisonData().stream().skip(1).collect(Collectors.toList())) {
                DiMatrixTerritoryComparisonDimension dimensionEntity = new DiMatrixTerritoryComparisonDimension();
                dimensionEntity.setDimensionName(dimension.get(0));
                dimensionEntity.setGroupId(group.getGroupId());
                dimensionEntity.setId(oldDimensionMap.get(dimension.get(0)));
                dimensionEntity.setWeight(weight.getAndIncrement());
                dimensionService.saveOrUpdate(dimensionEntity);
                oldDimensionMap.put(dimensionEntity.getDimensionName(), dimensionEntity.getId());
            }
            // 保存产品
            List<DiMatrixTerritoryComparisonProduct> productList = productService.queryByTerritoryId(territoryId, Arrays.asList(group.getGroupId()));
            Map<String, Long> oldProductMap = productList.stream().collect(Collectors.toMap((x) -> x.getProductName(), DiMatrixTerritoryComparisonProduct::getId, (a, b) -> a));
            Set<String> newProductName = group.getComparisonData().get(0).stream().skip(1).collect(Collectors.toSet());
            List<Long> delProductList = productList.stream().filter(x -> !newProductName.contains(x.getProductName()))
                    .map(x -> x.getId()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(delProductList)) {
                productService.removeBatchByIds(delProductList);
            }
            weight.set(0);
            for (String productName : newProductName) {
                DiMatrixTerritoryComparisonProduct productEntity = new DiMatrixTerritoryComparisonProduct();

                productEntity.setProductName(productName);
                productEntity.setGroupId(group.getGroupId());
                productEntity.setId(oldProductMap.get(productName));
                productEntity.setWeight(weight.getAndIncrement());
                productService.saveOrUpdate(productEntity);
                oldProductMap.put(productEntity.getProductName(), productEntity.getId());

            }

            List<DiMatrixTerritoryComparisonDetail> detailList = detailService.queryByTerritoryId(territoryId, Arrays.asList(group.getGroupId()));
            Map<String, Long> oldDetailMap = detailList.stream().collect(Collectors.toMap((x) -> String.format("%s-%s-%s", x.getComparisonDimensionId(), x.getComparisonProductId(), x.getContent()),
                    DiMatrixTerritoryComparisonDetail::getId, (a, b) -> a));
            // 保存对比明细
            Set<Long> keep = new HashSet<>();
            for (int row = 1; row < group.getComparisonData().size(); row++) {
                String dimensionName = group.getComparisonData().get(row).get(0);
                for (int col = 1; col < group.getComparisonData().get(row).size(); col++) {
                    String productName = group.getComparisonData().get(0).get(col);
                    DiMatrixTerritoryComparisonDetail detailEntity = new DiMatrixTerritoryComparisonDetail();
                    detailEntity.setGroupId(group.getGroupId());
                    detailEntity.setComparisonDimensionId(oldDimensionMap.get(dimensionName));
                    detailEntity.setComparisonProductId(oldProductMap.get(productName));
                    detailEntity.setContent(group.getComparisonData().get(row).get(col));
                    detailEntity.setId(oldDetailMap.get(String.format("%s-%s-%s", detailEntity.getComparisonDimensionId(), detailEntity.getComparisonProductId(), detailEntity.getContent())));
                    detailService.saveOrUpdate(detailEntity);
                    keep.add(detailEntity.getId());
                }
            }
            // 删除旧数据
            List<Long> delDetailIds = detailList.stream().filter(x -> !keep.contains(x.getId())).map(DiMatrixTerritoryComparisonDetail::getId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(delDetailIds)) {
                detailService.removeBatchByIds(delDetailIds);
            }
        }

    }

    private void cleanComparison(Long territoryId) {
        List<DiMatrixTerritoryComparisonGroup> groups = this.queryByTerritoryId(territoryId);
        if (CollectionUtils.isEmpty(groups)) {
            return;
        }
        detailService.removeByGroupId(groups.stream().map(DiMatrixTerritoryComparisonGroup::getId).collect(Collectors.toList()));
        dimensionService.removeByGroupId(groups.stream().map(DiMatrixTerritoryComparisonGroup::getId).collect(Collectors.toList()));
        productService.removeByGroupId(groups.stream().map(DiMatrixTerritoryComparisonGroup::getId).collect(Collectors.toList()));
        this.removeByIds(groups.stream().map(DiMatrixTerritoryComparisonGroup::getId).collect(Collectors.toList()));
    }

    /**
     * 根据territoryId查询对比分组
     *
     * @param territoryId
     * @return
     */
    public List<ComparisonGroupDTO> queryComparisonByTerritoryId(Long territoryId) {

        List<DiMatrixTerritoryComparisonGroup> groups = this.queryByTerritoryId(territoryId);
        if (CollectionUtils.isEmpty(groups)) {
            return List.of();
        }
        List<Long> groupIds = groups.stream().map(DiMatrixTerritoryComparisonGroup::getId).collect(Collectors.toList());

        List<DiMatrixTerritoryComparisonDetail> details = detailService.queryByTerritoryId(territoryId, groupIds);
        List<DiMatrixTerritoryComparisonDimension> dimensions = dimensionService.queryByTerritoryId(territoryId, groupIds);
        List<DiMatrixTerritoryComparisonProduct> products = productService.queryByTerritoryId(territoryId, groupIds);
        Map<String, String> detailMap = details.stream().collect(Collectors.toMap(x -> {
            return x.getComparisonDimensionId() + "-" + x.getComparisonProductId();
        }, DiMatrixTerritoryComparisonDetail::getContent, (a, b) -> a));

        List<ComparisonGroupDTO> result = new ArrayList<>();

        for (DiMatrixTerritoryComparisonGroup group : groups) {
            ComparisonGroupDTO groupDTO = new ComparisonGroupDTO();
            groupDTO.setGroupName(group.getGroupName());
            groupDTO.setGroupId(group.getId());

            List<List<String>> data = new ArrayList<>();
            groupDTO.setComparisonData(data);
            List<DiMatrixTerritoryComparisonProduct> productList = products.stream().filter(x -> x.getGroupId().equals(group.getId())).collect(Collectors.toList());
            List<DiMatrixTerritoryComparisonDimension> dimensionList = dimensions.stream().filter(x -> x.getGroupId().equals(group.getId())).collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(productList) && CollectionUtils.isNotEmpty(dimensionList)) {
                //表头
                List<String> header = new ArrayList<>();
                header.add("维度");
                for (DiMatrixTerritoryComparisonProduct product : productList) {
                    header.add(product.getProductName());
                }
                data.add(header);

                for (DiMatrixTerritoryComparisonDimension dimension : dimensionList) {
                    List<String> row = new ArrayList<>();
                    row.add(dimension.getDimensionName());
                    for (DiMatrixTerritoryComparisonProduct product : productList) {
                        String key = dimension.getId() + "-" + product.getId();
                        if (detailMap.containsKey(key)) {
                            row.add(detailMap.get(key));
                        } else {
                            row.add("");
                        }
                    }
                    data.add(row);
                }
            }
            result.add(groupDTO);
        }
        return result;
    }
}
