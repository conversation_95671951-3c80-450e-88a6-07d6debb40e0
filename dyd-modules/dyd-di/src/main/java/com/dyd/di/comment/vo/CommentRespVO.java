package com.dyd.di.comment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Schema(description = "管理后台 - 星记 Response VO")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CommentRespVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "17327")
    private Long id;

    @Schema(description = "记录当前评论所属的项目阶段，字典:comment_stage", requiredMode = Schema.RequiredMode.REQUIRED)
    private String stage;

    @Schema(description = "标记评论类型，普通评论:comment;文件:file", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotBlank
    private String type;

    @Schema(description = "项目号，在创建商机之前不生成项目，此时为空")
    private String projectNo;

    @Schema(description = "商机号，在创建商机之前为空")
    private String nicheNo;

    @Schema(description = "线索号")
    private String clueNo;

    @Schema(description = "额外信息，目前存文件信息，非文件类型为空")
    private String otherParams;

    @Schema(description = "评论内容")
    private String content;

    @Schema(description = "艾特的用户名称(工号)")
    private List<String> atUserName;

    @Schema(description = "被艾特人点击后跳转地址")
    private String atLinkUrl;

    private String userName;

    private Long commentId;

}