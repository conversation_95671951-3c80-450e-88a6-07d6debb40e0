package com.dyd.di.order.pojo.dto;

import com.dyd.common.core.annotation.Excel;
import com.dyd.di.marketing.domain.DiMarketingContacts;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class OrderDetailDTO {

    /**
     * 主键ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 项目交付日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date orderDeliveryDate;

    /**
     * 合同交付日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date contractDeliveryDate;

    /**
     * 合同id
     */
    private String contractNo;
    /**
     * 项目编号
     */
    private String projectNo;
    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 订单状态 字典 order_status
     */
    private Integer orderStatus;

    /**
     * 安装调试(实施)负责人
     */
    private String enforceUserId;

    /**
     * 安装调试(实施)负责人 名称
     */
    private String enforceUserIdName;

    /**
     * 技术支持复核负责人
     */
    private String orderPreSaleUserId;

    /**
     * 技术支持复核负责人名称
     */
    private String orderPreSaleUserName;


    /**
     * 技术支持复核显示 true:显示
     */
    private Boolean orderPreSaleCheckFlag;

    /**
     * 项目经理
     */
    private String projectManagerUserId;

    /**
     * 项目经理 名称
     */
    private String projectManagerUserIdName;

    /**
     * 电气设计负责人
     */
    private String electricDesignUserId;

    /**
     * 电气设计负责人 名称
     */
    private String electricDesignUserIdName;
    /**
     * 机械设计负责人
     */
    private String machineDesignUserId;

    /**
     * 机械设计负责人 名称
     */
    private String machineDesignUserIdName;
    /**
     * 生产负责人
     */
    private String produceUserId;
    /**
     * 生产负责人 名称
     */
    private String produceUserIdName;
    /**
     * 生产质检负责人
     */
    private String produceQualityUserId;
    /**
     * 生产质检负责人姓名
     */
    private String produceQualityUserName;
    /**
     * 采购负责人
     */
    private String purchaseUserId;

    /**
     * 采购负责人 名称
     */
    private String purchaseUserIdName;

    /**
     * 负责人 所属销售
     */
    private String owner;
    /**
     * 所属销售名称
     */
    private String ownerName;
    /**
     * 所属部门
     */
    private String deptId;
    /**
     * 所属部门名称
     */
    private String deptName;

    /**
     * 订单排期状态 0 待确认 1已确认
     */
    private Integer scheduleStatus;

    /**
     * 联系人列表
     */
    private List<DiMarketingContacts> contactsList;

    /**
     * 产品方案列表
     */
    private List<OrderProductPlanDetailDTO> orderProductPlanDetailDTOList;

    /**
     * 是否变更项目交付日期：0.否，1.是
     */
    private Integer isChangeDeliveryDate;

    /**
     * 订单项目交付日期变更记录主键ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long deliveryChangeId;

    /**
     * 项目交付日期变更后的日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date changeDeliveryDate;

    /**
     * U9订单编号
     */
    private String u9OrderNo;

    /**
     * 合同交付周期(周)
     */
    private BigDecimal contractDeliveryCycleWeek;

    /**
     * 项目开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date projectStartDate;

    /**
     * 项目开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createTime;

    /**
     * 是否显示排期按钮
     */
    private boolean showScheduleBtn;

    /**
     * 是否显示排期变更按钮
     */
    private boolean showScheduleChangeBtn;

    /**
     * 订单阶段：1.立项阶段，2.设计阶段，3.采购阶段，4.生产阶段，5.质量阶段，6.待出货，7.已完成
     */
    private Integer orderStage;

    /**
     * 商机号
     */
    private String nicheNo;

    /**
     * 订单是否提前执行：1.否，2.是
     */
    private String isOrderAdvanceExecution;

    /**
     * 合同是否提前执行：1.否，2.是
     */
    private String isContractAdvanceExecution;

    /**
     * 询价定性（重要性）（importance）
     */
    private String importance;
    /**
     * A+商机判断原因(determine_cause)
     */
    private String determineCause;
    /**
     * 系统工程师
     */
    private String systemsEngineer;
    /**
     * 系统工程师名称
     */
    private String systemsEngineerName;


}

