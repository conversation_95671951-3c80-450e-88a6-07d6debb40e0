package com.dyd.di.pre.domain.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 报价单详情中间页
 */
@Data
public class PreSaleQuoteInfoTempResponse {


    /**
     * 客户名称
     */
    private String companyName;

    /**
     * 联系人姓名
     */
    private String contactsName;

    /**
     * 联系方式
     */
    private String contactsPhone;

    /**
     * 理论成本合计
     */
    private BigDecimal costFeeTotal;

    /**
     * 订单期望交付日期
     */
    private LocalDate expecteDate;

    /**
     * 询价定性（重要性）（importance）
     */
    private String importance;

    /**
     * A+商机判断原因(determine_cause)
     */
    private String determineCause;

    /**
     * 报价单明细
     */
    private List<PreSaleQuoteDetail> preSaleQuoteDetailList;
    private BigDecimal saleServiceCycleTotal;
    private BigDecimal saleServiceQuoteTotal;

    @Data
    public static class PreSaleQuoteDetail {

        @JsonFormat(shape = JsonFormat.Shape.STRING)
        private Long preSaleId;

        /**
         * 产品方案编码
         */
        private String preSaleCode;

        /**
         * 商机编码
         */
        private String nicheCode;

        /**
         * 产品方案名称
         */
        private String preSaleName;

        /**
         * 方案类型
         */
        private Integer orderType;

        /**
         * 物料编码
         */
        private String materialCode;

        private String showVersionNo;

        private String showMaterialNo;

        /**
         * 物料名称
         */
        private String productName;

        /**
         * 需求套数
         */
        private Integer num;

        /**
         * 当前库存
         */
        private String stockNum;

        /**
         * 理论成本小计
         */
        private BigDecimal costFee;

        /**
         * 理论成本合计
         */
        private BigDecimal costFeeSum;

        /**
         * 单套理论交期
         */
        private BigDecimal guideDuration;

        /**
         * 售价报价
         */
        private BigDecimal saleQuote;

        /**
         * 交期报价(天)
         */
        private BigDecimal deliveryQuote;

        /**
         * 国家
         */
        @JsonProperty("countryName")
        private String country;

        /**
         * 国家编码
         */
        private String countryCode;

        /**
         * 省
         */
        @JsonProperty("provinceName")
        private String province;

        /**
         * 省编码
         */
        private String provinceCode;

        /**
         * 市
         */
        @JsonProperty("cityName")
        private String city;

        /**
         * 市编码
         */
        private String cityCode;

        /**
         * 区
         */
        @JsonProperty("areaName")
        private String area;

        /**
         * 区编码
         */
        private String areaCode;

        /**
         * 销售服务报价
         */
        private BigDecimal saleServiceQuote;

        /**
         * 销售服务周期报价（人/天）
         */
        private BigDecimal saleServiceCycle;
        private BigDecimal systemServiceFee;
        private BigDecimal systemServiceCycle;

        /**
         * 技术支持负责人代码
         */
        private String techSupportOwnerCode;

        /**
         * 技术支持负责人名称
         */
        private String techSupportOwnerName;

        /**
         * 方案类型，1：标准品；2：非标,3 贸易
         */
        private Integer preSaleType;

    }

    /**
     * 国家
     */
    private String countryName;

    /**
     * 国家编码
     */
    private String countryCode;

    /**
     * 省
     */
    private String provinceName;

    /**
     * 市
     */
    private String cityName;

    /**
     * 区
     */
    private String areaName;

    /**
     * 省编码
     */
    private String provinceCode;

    /**
     * 市编码
     */
    private String cityCode;

    /**
     * 区编码
     */
    private String areaCode;

    /**
     * 统一收货地址
     */
    private String commonDeliveryAddress;

    /**
     * 方案类型，1：标准品；2：非标,3 贸易
     */
    private Integer preSaleType;

    /**
     * 核算交期（周），获取待报价方案列表中，单套测算交期（天）最大值。 以该值/7，并向上取整。如 7.1周 算8周。
     */
    private BigDecimal calculateDeliveryWeek;


    /**
     * 复核后交期（周）
     */
    private BigDecimal finallyDeliveryWeek;

    /**
     * 期望交期（周）
     */
    private Integer expectationDeliveryWeek;


}
