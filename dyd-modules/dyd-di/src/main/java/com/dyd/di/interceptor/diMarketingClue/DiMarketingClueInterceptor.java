package com.dyd.di.interceptor.diMarketingClue;

import com.dyd.common.core.utils.AfterCommitExecutor;
import com.dyd.di.marketing.domain.DiMarketingClue;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Signature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

@Component
@Intercepts({
        @Signature(
                type = Executor.class,
                method = "update",
                args = {MappedStatement.class, Object.class})
})
@Slf4j
public class DiMarketingClueInterceptor implements Interceptor {

    @Autowired
    private ApplicationContext applicationContext;

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        MappedStatement ms = (MappedStatement) invocation.getArgs()[0];
        Object parameter = invocation.getArgs()[1];
        if (!ms.getId().contains("DiMarketingClueMapper")) {
            return invocation.proceed();
        }
        SqlCommandType commandType = ms.getSqlCommandType();
        if (commandType != SqlCommandType.INSERT) {
            return invocation.proceed();
        }
        if (!(parameter instanceof DiMarketingClue)) {
            return invocation.proceed();
        }

        Object proceed = invocation.proceed();

        try {
            DiMarketingClue clue = (DiMarketingClue) parameter;
            AfterCommitExecutor.submit(() ->
                    applicationContext.publishEvent(new DiMarketingClueCreateEvent(this, clue.getClueNo())),
                    null);
        }catch (Throwable e) {
            log.error(e.getMessage(), e);
        }
        return proceed;
    }
}
