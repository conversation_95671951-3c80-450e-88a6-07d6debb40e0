package com.dyd.di.material.service;

import com.dyd.common.datascope.consts.DataListEnum;
import com.dyd.di.material.entity.DiMaterialStockInfo;
import com.dyd.di.materiel.domain.DiMateriel;
import com.dyd.di.materiel.domain.DiMaterielVersion;
import com.dyd.system.api.domain.SysUserRoleData;

import java.util.List;
import java.util.Map;

/**
 * 公共Service
 */
public interface CommonService {

    /**
     * 获取物料信息
     *
     * @param idList 主键集合
     * @return map<主键, 物料信息>
     */
    Map<Long, DiMateriel> getMaterielMapByIds(List<Long> idList);

//    /**
//     * 根据物料版本ID获取物料版本信息
//     *
//     * @param idList 主键集合
//     * @return map<主键, 物料版本信息>
//     */
//    Map<Long, DiMaterielVersion> getMaterielVersionMapByIds(List<String> idList);

    /**
     * 获取物料库存
     *
     * @param idList 物料主键集合
     * @return map<物料主键, 物料信息>
     */
    Map<Long, DiMaterialStockInfo> getMaterialStockMapByIds(List<Long> idList);

    /**
     * 根据工号获取用户姓名
     *
     * @param createByJobList 创建人工号列表
     * @param updateByJobList 修改人工号列表
     * @return 结果Map<工号job, 姓名name></>
     */
    Map<String, String> getUserNameByJob(List<String> createByJobList, List<String> updateByJobList);

    /**
     * 将人员加入到项目参与者中
     *
     * @param projectNo     项目编号
     * @param personnelList 需要加入到项目中的人员
     * @param status        状态：0.新增，2.删除
     */
    void personnelSaveProject(String projectNo, List<String> personnelList, String status);

    /**
     * 获取登录者及下属层级工号
     *
     * @return 工号集合
     */
    List<String> findLoginJobNumberList();

    /**
     * 获取登录者及下属层级工号
     *
     * @return 字符串SQL
     */
    String findLoginJobNumberStrSql();

    /**
     * 校验详情权限
     *
     * @param createBy
     * @param relationNo
     * @param projectNo
     */
    void checkDetailPurview(String createBy, String relationNo, String projectNo);

    SysUserRoleData findLoginJobNumberList(String dataListEnum, Long userid);

    List<String> decodeLiabilityUserList(String assignee);

    String encodeLiabilityUserList(List<String> userList);
}
