package com.dyd.di.u9.controller;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dyd.common.core.domain.R;
import com.dyd.di.api.model.AddMaterialVersionRequest;
import com.dyd.di.api.model.AddSynErpLogRequest;
import com.dyd.di.materiel.domain.DiMateriel;
import com.dyd.di.materiel.mapper.DiMaterielMapper;
import com.dyd.di.u9.domain.CreateBomMasterTRequest;
import com.dyd.di.u9.domain.DiSynErpLog;
import com.dyd.di.u9.domain.DiSynErpMaterielVersion;
import com.dyd.di.u9.mapper.DiSynErpLogMapper;
import com.dyd.di.u9.mapper.DiSynErpMaterielVersionMapper;
import com.dyd.di.u9.service.U9Service;
import com.dyd.exchange.RemoteExchangeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

@RequestMapping("/u9")
@RestController
public class U9Controller {

    @Autowired
    private U9Service u9Service;

    @Autowired
    private DiSynErpLogMapper diSynErpLogMapper;

    @Autowired
    private DiSynErpMaterielVersionMapper diSynErpMaterielVersionMapper;

    @Autowired
    private RemoteExchangeService remoteExchangeService;

    @Autowired
    private DiMaterielMapper diMaterielMapper;

    @GetMapping("/createSoU9")
    public R createSoU9(@RequestParam("id") Long id){
        u9Service.createSoU9(id);
        return R.ok();
    }


    /**
     * 保存同步u9日志记录
     * @param addSynErpLogRequest
     * @return
     */
    @PostMapping("/insertDiSynErpLog")
    public R insertDiSynErpLog(@RequestBody AddSynErpLogRequest addSynErpLogRequest){

        DiSynErpLog diSynErpLog = new DiSynErpLog();
        diSynErpLog.setRequestParam(addSynErpLogRequest.getRequestParam());
        diSynErpLog.setResponseParam(addSynErpLogRequest.getResponseParam());
        diSynErpLog.setStatus(addSynErpLogRequest.getStatus());
        diSynErpLogMapper.insert(diSynErpLog);
        return R.ok();
    }

    @PostMapping("/inserMaterielVersion")
    public R inserMaterielVersion(@RequestBody AddMaterialVersionRequest request){

        DiSynErpMaterielVersion diSynErpMaterielVersion = new DiSynErpMaterielVersion();
        diSynErpMaterielVersion.setMaterialNo(request.getMaterialNo());
        diSynErpMaterielVersion.setDiVersion(request.getDiVersion());
        diSynErpMaterielVersion.setErpVersion(request.getErpVersion());
        diSynErpMaterielVersionMapper.insert(diSynErpMaterielVersion);
        return R.ok();
    }

    @GetMapping("/selectMaterielVersion")
    public R<Long> selectMaterielVersion(@RequestParam("materielNo") String materielNo,@RequestParam("version") String version){
        return R.ok(diSynErpMaterielVersionMapper.selectCount(Wrappers.<DiSynErpMaterielVersion>lambdaQuery().eq(DiSynErpMaterielVersion::getMaterialNo,materielNo).eq(DiSynErpMaterielVersion::getDiVersion,version)));
    }

    @PostMapping("/syn2U9")
    public R syn2U9(@RequestBody List<Long> list){
        if(CollectionUtils.isEmpty(list)) {
            List<DiSynErpLog> createBomMasterSv = diSynErpLogMapper.selectList(Wrappers.<DiSynErpLog>lambdaQuery().like(DiSynErpLog::getRequestParam, "CreateBomMasterSv").eq(DiSynErpLog::getStatus, 1));
            for (DiSynErpLog diSynErpLog : createBomMasterSv) {
                CreateBomMasterTRequest parse = JSON.parseObject(diSynErpLog.getRequestParam(), CreateBomMasterTRequest.class);
                List<DiMateriel> diMateriels = diMaterielMapper.selectList(Wrappers.<DiMateriel>lambdaQuery().eq(DiMateriel::getMaterielNo, parse.getRequestDTO().get(0).getItemCode()));
                if (CollectionUtils.isNotEmpty(diMateriels)) {

                    remoteExchangeService.syn2U9(Arrays.asList(diMateriels.get(0).getId()));
                }
            }
        }else{
            remoteExchangeService.syn2U9(list);
        }
        return R.ok();
    }

    /**
     * 同步DI客户到u9
     * @param customerCode
     * @return
     */
    @GetMapping("/synCusToU9")
    public R synCusToU9(@RequestParam(value = "customerCode",required = false) String customerCode){
        u9Service.synCusToU9(customerCode);
        return R.ok();
    }
}
