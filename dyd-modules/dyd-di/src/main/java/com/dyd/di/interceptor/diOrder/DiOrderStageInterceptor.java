package com.dyd.di.interceptor.diOrder;

import com.baomidou.mybatisplus.core.conditions.AbstractWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.dyd.common.core.utils.AfterCommitExecutor;
import com.dyd.di.interceptor.AbstractInterceptor;
import com.dyd.di.order.domain.DiOrder;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Signature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
@Intercepts({
        @Signature(
                type = Executor.class,
                method = "update",
                args = {MappedStatement.class, Object.class})
})
@Slf4j
public class DiOrderStageInterceptor extends AbstractInterceptor {
    @Autowired
    private ApplicationContext applicationContext;

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        MappedStatement ms = (MappedStatement) invocation.getArgs()[0];
        Object parameter = invocation.getArgs()[1];
        if (!ms.getId().contains("DiOrderMapper")) {
            return invocation.proceed();
        }
        SqlCommandType commandType = ms.getSqlCommandType();
        if (commandType != SqlCommandType.INSERT && commandType != SqlCommandType.UPDATE) {
            return invocation.proceed();
        }
        if (!(parameter instanceof DiOrder) && !(parameter instanceof UpdateWrapper) && !(parameter instanceof Map)) {
            return invocation.proceed();
        }

        Object proceed = invocation.proceed();

        try {
            if (parameter instanceof Map) {
                Map<?, ?> paramMap = (Map<?, ?>) parameter;
                if (paramMap.containsKey("et")) {
                    Object o = paramMap.get("et");
                    if (Objects.nonNull(o) && o instanceof DiOrder order) {
                        if (Objects.nonNull(order.getOrderStage())) {
                            AfterCommitExecutor.submit(() ->
                                    applicationContext.publishEvent(new DiOrderStageEvent(this, order.getId(), order.getOrderNo())),
                                    null);
                        }
                    }else {
                        o = paramMap.get("ew");
                        if (Objects.nonNull(o) && o instanceof AbstractWrapper updateWrapper) {
                            // 获取 WHERE 条件字段和值
                            Map<String, Object> whereParams = extractWhereParams(updateWrapper);
                            // 获取 SET 更新字段和值
                            Map<String, Object> setParams = extractSetParams(updateWrapper);

                            Integer orderStage = (Integer) setParams.get("order_stage");
                            if (Objects.nonNull(orderStage)) {
                                Long id = (Long) whereParams.get("id");
                                String orderNo = (String) whereParams.get("order_no");
                                AfterCommitExecutor.submit(() ->
                                                applicationContext.publishEvent(new DiOrderStageEvent(this, id, orderNo)),
                                        null);
                            }
                        }
                    }
                }
            }else if (parameter instanceof DiOrder order) {
                if (commandType == SqlCommandType.INSERT || Objects.nonNull(order.getOrderStage())) {
                    AfterCommitExecutor.submit(() ->
                                    applicationContext.publishEvent(new DiOrderStageEvent(this, order.getId(), order.getOrderNo())),
                            null);
                }
            }else {
                UpdateWrapper<?> updateWrapper = (UpdateWrapper<?>) parameter;
                // 获取 WHERE 条件字段和值
                Map<String, Object> whereParams = extractWhereParams(updateWrapper);
                // 获取 SET 更新字段和值
                Map<String, Object> setParams = extractSetParams(updateWrapper);

                Integer orderStage = (Integer) setParams.get("order_stage");
                if (Objects.nonNull(orderStage)) {
                    Long id = (Long) whereParams.get("id");
                    String orderNo = (String) whereParams.get("order_no");
                    AfterCommitExecutor.submit(() ->
                                    applicationContext.publishEvent(new DiOrderStageEvent(this, id, orderNo)),
                            null);
                }
            }
        }catch (Throwable e) {
            log.error(e.getMessage(), e);
        }
        return proceed;
    }

}
