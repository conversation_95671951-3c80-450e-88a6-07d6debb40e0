package com.dyd.di.order.listener;

import com.alibaba.fastjson.JSON;
import com.dyd.common.core.utils.StringUtils;
import com.dyd.di.api.model.SaveSchedulingInformationRequest;
import com.dyd.di.eventbus.AbstractDydEventListener;
import com.dyd.di.order.domain.DiOrder;
import com.dyd.di.order.domain.DiOrderExt;
import com.dyd.di.order.event.OrderActualPaymentEvent;
import com.dyd.di.order.event.OrderAdvanceExecutionEvent;
import com.dyd.di.order.event.OrderManagerUpdateEvent;
import com.dyd.di.order.event.OrderScheduleDoneEvent;
import com.dyd.di.order.service.IDiOrderExtService;
import com.dyd.di.order.service.IDiOrderService;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class CreateOrderTaskListener {

    @Autowired
    private IDiOrderService orderService;

    @Autowired
    private IDiOrderExtService orderExtService;

    @Service
    public class OrderScheduleDoneEventListener extends AbstractDydEventListener<OrderScheduleDoneEvent> {
        @Override
        @EventListener
        public void onEvent(OrderScheduleDoneEvent event) {
            createOrderTask(event.getOrderId());
        }
    }

    @Service
    public class OrderActualPaymentEventListener extends AbstractDydEventListener<OrderActualPaymentEvent> {
        @Override
        @EventListener
        public void onEvent(OrderActualPaymentEvent event) {
            createOrderTask(event.getOrderId());
        }
    }

    @Service
    public class OrderAdvanceExecutionEventListener extends AbstractDydEventListener<OrderAdvanceExecutionEvent> {
        @Override
        @EventListener
        public void onEvent(OrderAdvanceExecutionEvent event) {
            createOrderTask(event.getOrderId());
        }
    }

    public void createOrderTask(Long orderId) {
        DiOrder order = orderService.getById(orderId);
        if (order == null) {
            log.error("订单不存在");
            return;
        }
        if (!orderService.canRun(order)) {
            log.error("订单还不能创建任务");
            return;
        }
        DiOrderExt ext = orderExtService.queryByOrderNo(order.getOrderNo());
        if (ext == null || StringUtils.isBlank(ext.getTempSchedule())) {
            log.error("订单扩展信息不存在");
            return;
        }
        SaveSchedulingInformationRequest request = JSON.parseObject(ext.getTempSchedule(), SaveSchedulingInformationRequest.class);
        orderService.createOrderTask(request);

    }
}
