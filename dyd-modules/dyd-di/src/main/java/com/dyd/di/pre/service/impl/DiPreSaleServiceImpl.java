package com.dyd.di.pre.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dyd.common.core.domain.R;
import com.dyd.common.core.enums.*;
import com.dyd.common.core.exception.ServiceException;
import com.dyd.common.core.utils.*;
import com.dyd.common.security.utils.SecurityUtils;
import com.dyd.dbc.api.RemoteDbcService;
import com.dyd.dbc.api.model.DdUserDTO;
import com.dyd.dbc.api.model.QueryDdUserDTO;
import com.dyd.di.agency.constants.AgencyConstants;
import com.dyd.di.agency.domain.dto.AgencyTaskInfoDTO;
import com.dyd.di.agency.enums.AgencyTaskTypeEnum;
import com.dyd.di.agency.service.DiAgencyTaskService;
import com.dyd.di.api.model.PreSalesPageResponse;
import com.dyd.di.api.model.UpdatePreSaleManifestRequest;
import com.dyd.di.common.DiHelper;
import com.dyd.di.eventbus.utils.EventBusUtils;
import com.dyd.di.implement.domain.dto.FindImplementInquiryDetailDto;
import com.dyd.di.implement.domain.dto.FindImplementInquiryFormDto;
import com.dyd.di.implement.domain.dto.SaveDiImplementInquiryFormDto;
import com.dyd.di.implement.domain.vo.DiImplementInquiryDetailVo;
import com.dyd.di.implement.domain.vo.DiImplementInquiryFormVo;
import com.dyd.di.implement.entity.DiImplementInquiryDetail;
import com.dyd.di.implement.mapper.DiImplementInquiryDetailMapper;
import com.dyd.di.implement.mapper.DiImplementInquiryFormMapper;
import com.dyd.di.implement.service.DiImplementInquiryFormService;
import com.dyd.di.label.pojo.dto.LabelMaterielWarehouseListDTO;
import com.dyd.di.label.pojo.vo.LabelMaterielWarehouseListVO;
import com.dyd.di.label.service.IDiMaterielLabelWarehouseService;
import com.dyd.di.contract.iservice.IDiContractService;
import com.dyd.di.marketing.domain.DiMarketingContacts;
import com.dyd.di.marketing.domain.DiMarketingNiche;
import com.dyd.di.marketing.service.IDiMarketingContactsService;
import com.dyd.di.marketing.service.IDiMarketingNicheService;
import com.dyd.di.material.domain.dto.FindDiMaterialInquiryFormDto;
import com.dyd.di.material.domain.dto.SaveDiMaterialInquiryDetailDto;
import com.dyd.di.material.domain.dto.SaveDiMaterialInquiryFormDto;
import com.dyd.di.material.domain.vo.DiMaterialInquiryFormVo;
import com.dyd.di.material.domain.vo.DiMaterialStorageVo;
import com.dyd.di.material.entity.DiMaterialInquiryDetail;
import com.dyd.di.material.entity.DiMaterialInquiryForm;
import com.dyd.di.material.mapper.DiMaterialInquiryFormMapper;
import com.dyd.di.material.service.CommonService;
import com.dyd.di.material.service.DiMaterialInquiryDetailService;
import com.dyd.di.material.service.DiMaterialInquiryFormService;
import com.dyd.di.material.service.DiMaterialStockInfoService;
import com.dyd.di.materiel.domain.*;
import com.dyd.di.materiel.pojo.MaterielBomVersionVo;
import com.dyd.di.materiel.pojo.MaterielInfoVo;
import com.dyd.di.materiel.pojo.MaterielVersionBomVO;
import com.dyd.di.materiel.pojo.dto.*;
import com.dyd.di.materiel.service.*;
import com.dyd.di.message.domain.DiMessageList;
import com.dyd.di.message.service.IDiMessageListService;
import com.dyd.di.mq.MqProcessBody;
import com.dyd.di.mq.MqRelationBody;
import com.dyd.di.mq.MqSendMsg;
import com.dyd.di.order.domain.*;
import com.dyd.di.order.enums.OrderDingTalkEnum;
import com.dyd.di.order.enums.OrderStageEnum;
import com.dyd.di.order.enums.OrderStatusEnum;
import com.dyd.di.order.event.OrderCompletedEvent;
import com.dyd.di.order.service.*;
import com.dyd.di.order.enums.OrderPreSaleStatusEnum;
import com.dyd.di.oss.OssService;
import com.dyd.di.pre.conver.PreSaleConverUtil;
import com.dyd.di.pre.domain.*;
import com.dyd.di.pre.domain.event.PreSaleGivenUpEvent;
import com.dyd.di.pre.domain.event.PreSaleDeliveryReviewLockedEvent;
import com.dyd.di.pre.domain.event.TechnicalSupportUserChangeEvent;
import com.dyd.di.pre.domain.request.*;
import com.dyd.di.pre.domain.response.*;
import com.dyd.di.pre.entity.*;
import com.dyd.di.pre.enums.DingTalkEnum;
import com.dyd.di.pre.enums.PreSaleStatusEnum;
import com.dyd.di.pre.enums.PreSaleTypeEnum;
import com.dyd.di.pre.enums.*;
import com.dyd.di.pre.event.OrderPreSaleStatusChangedEvent;
import com.dyd.di.pre.event.PreSaleDeliveryReviewDoneEvent;
import com.dyd.di.pre.event.PreSaleDeliveryReviewSupplyDoneEvent;
import com.dyd.di.pre.event.PreSaleTechnicalReviewDoneEvent;
import com.dyd.di.pre.mapper.*;
import com.dyd.di.pre.service.*;
import com.dyd.di.pre.wrapper.PreSaleWrapper;
import com.dyd.di.process.domain.DiProjectRelation;
import com.dyd.di.process.job.CheckProcessProjectStatusJob;
import com.dyd.di.process.mapper.DiProjectRelationMapper;
import com.dyd.di.process.pojo.vo.ProcessProjectAddVO;
import com.dyd.di.process.service.DiProjectRelationUserService;
import com.dyd.di.process.service.IDiProcessProjectService;
import com.dyd.di.process.service.IDiProjectRelationService;
import com.dyd.di.production.domain.DiProductionInquiry;
import com.dyd.di.production.domain.DiProductionInquiryInventory;
import com.dyd.di.production.mapper.DiProductionInquiryMapper;
import com.dyd.di.production.service.IDiProductionInquiryInventoryService;
import com.dyd.di.production.service.IDiProductionInquiryService;
import com.dyd.di.sequence.SequenceService;
import com.dyd.di.syscategory.entity.SysCategory;
import com.dyd.di.syscategory.service.ISysCategoryService;
import com.dyd.di.tools.dto.resp.DesignFeeResp;
import com.dyd.di.tools.service.QuotationCalcService;
import com.dyd.system.api.RemoteDictDataService;
import com.dyd.system.api.RemoteRoleService;
import com.dyd.system.api.RemoteUserService;
import com.dyd.system.api.domain.SysDept;
import com.dyd.system.api.domain.SysDictData;
import com.dyd.system.api.domain.SysUser;
import com.dydtec.base.oss.api.dto.response.OssPreviewDTO;
import com.dydtec.infras.core.base.exception.BizValidException;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.jsonwebtoken.lang.Assert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

import static com.dyd.common.core.enums.PreSaleQuoteStatusEnum.DELIVERY_TIME_REVIEW;
import static com.dyd.di.pre.constant.PreConstant.DEFAULT_BOM_DESIGN_DAY;
import static com.dyd.di.pre.enums.PreSaleStatusEnum.TECHNICAL_SUPPORT;

/**
 * 售前方案Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-19
 */
@Slf4j
@Service
public class DiPreSaleServiceImpl extends ServiceImpl<DiPreSaleMapper, DiPreSale> implements IDiPreSaleService {

    //region 对象实例化
    @Autowired
    private ISysCategoryService sysCategoryService;
    @Autowired
    private QuotationCalcService quotationCalcService;
    @Autowired
    private DiPreSaleMapper diPreSaleMapper;

    @Autowired
    private IDiPreSaleService iDiPreSaleService;

    @Autowired
    private IDiMaterielPriceService diPriceSalePriceService;

    @Autowired
    private PreSaleConverUtil preSaleConverUtil;

    @Autowired
    private DiPreSaleManifestMapper diPreSaleManifestMapper;

    @Autowired
    private IDiPreSaleManifestService iDiPreSaleManifestService;

    @Autowired
    private DiPreSaleUrlMapper diPreSaleUrlMapper;

    @Autowired
    private SequenceService sequenceService;

    @Autowired
    private IDiMarketingNicheService iDiMarketingNicheService;

    @Autowired
    private IDiMarketingContactsService iDiMarketingContactsService;

    @Autowired
    private MqSendMsg mqSendMsg;

    @Autowired
    private DiMaterialStockInfoService diMaterialStockInfoService;

    @Autowired
    private IDiMaterielService iDiMaterielService;

    @Autowired
    private IDiMaterielFileService diMaterielFileService;

//    @Autowired
//    private DiMaterielVersionService iDiMaterielVersionService;

    @Autowired
    private OssService ossService;

    @Autowired
    private IDiMaterielLabelWarehouseService diMaterielLabelWarehouseService;

    @Autowired
    private IDiProcessProjectService iDiProcessProjectService;

    @Autowired
    private IDiProjectRelationService diProjectRelationService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private DiProjectRelationUserService diProjectRelationUserService;

    @Autowired
    private DiProjectRelationMapper diProjectRelationMapper;

    @Autowired
    private DiPreSaleLabelMapper diPreSaleLabelMapper;

    @Autowired
    private DiMaterialInquiryFormMapper diMaterialInquiryFormMapper;

    @Autowired
    private DiMaterialInquiryDetailService diMaterialInquiryDetailService;

    @Autowired
    private DiImplementInquiryFormMapper diImplementInquiryFormMapper;

    @Autowired
    private DiProductionInquiryMapper diProductionInquiryMapper;

    @Autowired
    private DiMaterialInquiryFormService diMaterialInquiryFormService;

    @Autowired
    private IDiProductionInquiryService diProductionInquiryService;

    @Autowired
    private DiImplementInquiryFormService diImplementInquiryFormService;

    @Autowired
    private DiPreSaleCustomizedMapper diPreSaleCustomizedMapper;

    @Autowired
    private DiPreSaleCustomizedPlcMapper diPreSaleCustomizedPlcMapper;

    @Autowired
    private DiPreSaleCustomizedPipingMapper diPreSaleCustomizedPipingMapper;

    @Autowired
    private DiPreSaleCustomizedHeaderMapper diPreSaleCustomizedHeaderMapper;

    @Autowired
    private DiPreSaleCustomizedFanMapper diPreSaleCustomizedFanMapper;

    @Autowired
    private DiPreSaleCustomizedBurnerMapper diPreSaleCustomizedBurnerMapper;

    @Autowired
    private DiPreSaleCustomizedAirMapper diPreSaleCustomizedAirMapper;

    @Autowired
    private IDiProductionInquiryInventoryService diProductionInquiryInventoryService;

    @Autowired
    private DiImplementInquiryDetailMapper diImplementInquiryDetailMapper;

    @Autowired
    private IDiMaterielBomService diMaterielBomService;

    @Autowired
    private IDiMaterielSupplierService iDiMaterielSupplierService;

    @Autowired
    private IDiMaterielPriceService materielPriceService;

    @Autowired
    private RemoteUserService remoteUserService;

    @Autowired
    private RocketMQTemplate rocketMQTemplate;

    @Autowired
    private IDiMaterielService diMaterielService;

    @Autowired
    private IMultiVersionMaterielService iMultiVersionMaterielService;

    @Autowired
    private ITreeBomService treeBomService;

    @Autowired
    private IDiPreSaleMaterielSelectionService selectionService;


    @Autowired
    private RemoteDbcService remoteDbcService;

    @Autowired
    private IDiOrderService diOrderService;


    @Autowired
    private DiOrderElectricDesignService diOrderElectricityService;
    @Autowired
    private DiOrderMachineDesignService diOrderMachineDesignService;

    @Autowired
    private DiOrderMaterielPlanService diOrderMaterielPlanService;


    @Autowired
    private DiOrderProduceService diOrderProduceService;

    @Autowired
    private DiOrderQualityService diOrderQualityService;

    @Autowired
    private PreSaleQuoteService preSaleQuoteService;

    @Autowired
    private IDiOrderService orderService;

    @Autowired
    private IDiMessageListService diMessageListService;

    @Autowired
    private RemoteDictDataService remoteDictDataService;

    @Autowired
    private DiPreSaleQuoteMapper diPreSaleQuoteMapper;

    @Autowired
    private DiPreSaleQuoteDetailMapper diPreSaleQuoteDetailMapper;

    @Autowired
    private DiOrderSupportCheckService supportCheckService;

    @Autowired
    private DiPreSaleHistoryService preSaleHistoryService;

    @Autowired
    private IDiContractService contractService;

    @Autowired
    private IDiPreSaleDeliveryReviewService deliveryReviewService;
    //endregion

    @Autowired
    private OrderCommonService orderCommonService;

    @Autowired
    private IPreSaleQuoteService quoteService;

    @Value("${purchase.manager.approve.role.key}")
    private String purchaseManagerApproveRoleKey;

    @Value("${deliver.manager.approve.role.key}")
    private String deliverManagerApproveRoleKey;


//    @Value("${purchase.manager.user.role:CGFZR}")
//    private String purchaseManagerUserRole;
//
//    @Value("${deliver.manager.user.role:JFFZR}")
//    private String deliverManagerUserRole;

    @Resource
    private RemoteRoleService remoteRoleService;

    @Autowired
    private DiPreSaleUrlService presaleUrlService;

    @Autowired
    private DiPreSaleFeeService diPreSaleFeeService;

    @Autowired
    private CheckProcessProjectStatusJob checkProcessProjectStatusJob;

    @Autowired
    private DiAgencyTaskService agencyTaskService;

    /**
     * 生产部门ID(跟产品确认目前是罗燃)
     */
    private final static Long PRODUCE_DEPT_ID = 971444126L;

    /**
     * 售后部门ID(跟产品确认目前是售后服务部)
     */
    private final static Long AFTER_SALE_DEPT_ID = 550803356L;

    private final static Long PURCHASE_DEPT_ID = 971507119L;

    /**
     * 查询售前方案
     *
     * @param idParam 售前方案主键
     * @return 售前方案
     */
    @Override
    public PreSaleDetailResponse selectDiPreSaleById(Long idParam, String preCode, Integer type, boolean ignoreCache) {

        try {
            DiPreSale diPreSale = null;
            if (Objects.nonNull(idParam)) {
                diPreSale = diPreSaleMapper.selectById(idParam);
            } else {
                diPreSale = this.lambdaQuery().eq(DiPreSale::getPreSaleCode, preCode).orderByDesc(DiPreSale::getId).last("limit 1").one();
            }
            if (Objects.isNull(diPreSale)) {
                return null;
            }

            //产品方案物料
            PreSaleDetailResponse preSaleDetailResponse = preSaleConverUtil.converToPreSaleDetail(diPreSale);
            if (Objects.nonNull(diPreSale.getIndustryCategories())) {
                SysCategory sysCategory = sysCategoryService.lambdaQuery().eq(SysCategory::getCode, diPreSale.getIndustryCategories()).one();
                Optional.ofNullable(sysCategory).ifPresent(category -> preSaleDetailResponse.setIndustryCategoriesName(category.getName()));
            }
            queryRelationNiche(diPreSale, preSaleDetailResponse);
            //tab方案需求
            if (type == null || type == 1) {
                //售前方案现场交接
                queryPreSaleInfo(diPreSale, preSaleDetailResponse);
            }
            if (type == null || type == 2) {//方案详情tab
                queryPresaleDetail(diPreSale, preSaleDetailResponse);
            }
            if (type == null || type == 3) {//图纸信息tab
                queryPreSalePicture(diPreSale, preSaleDetailResponse);
            }
            if (type == null || type == 4) {
                queryPreSaleBomOptimized(diPreSale, preSaleDetailResponse, ignoreCache);
            }
            if (type == null || type == 5) {
                queryPreSaleInquiryOptimized(diPreSale, preSaleDetailResponse, ignoreCache);
            }
            preSaleDetailResponse.setOrderType(String.valueOf(diPreSale.getOrderType()));
            preSaleDetailResponse.setPreSaleStatus(String.valueOf(diPreSale.getPreSaleStatus()));
            preSaleDetailResponse.setPreSaleOrderStatus(diPreSale.getOrderPreSaleStatus());
            preSaleDetailResponse.setPreSaleType(diPreSale.getPreSaleType());
            preSaleDetailResponse.setPreSaleId(diPreSale.getId());
            preSaleDetailResponse.setPackageType(diPreSale.getPackageTypeConfig());
            if (!PreSaleTypeEnum.TRADE.getCode().equals(diPreSale.getPreSaleType())) {
                preSaleDetailResponse.setHasQuoted(quoteService.hasQuoted(diPreSale.getId()));
            } else {
                preSaleDetailResponse.setHasQuoted(0);
            }

            return preSaleDetailResponse;
        } catch (Throwable e) {
            if (e.getCause() != null) {
                log.error("查询售前方案异常：{}", e.getCause().getMessage(), e.getCause());
            }
            log.error("查询售前方案异常", e);
            e.printStackTrace();
            throw e;
        }
    }

    @Override
    public PreSaleDetailResponse selectHistoryOfDiPreSale(Long preSaleId, Integer phase) {
        PreSaleDetailResponse rep = selectHistoryOfDiPreSale(preSaleId, phase, true);
        if (rep != null) {
            DiPreSale preSale = this.queryDiPreSaleById(preSaleId);
            rep.setPreSaleStatus(String.valueOf(preSale.getPreSaleStatus()));
            rep.setPreSaleOrderStatus(preSale.getOrderPreSaleStatus());
        }
        return rep;
    }

    @Override
    public PreSaleDetailResponse selectHistoryOfDiPreSale(Long preSaleId, Integer phase, boolean throwNx) {
        DiPreSaleHistory diPreSaleHistory = preSaleHistoryService.queryDiPreSaleHistoryById(preSaleId, 1);
        if (diPreSaleHistory == null) {
            if (throwNx) {
                Assert.notNull(diPreSaleHistory, "历史产品方案不存在");
            } else {
                return null;
            }
        }
        PreSaleDetailResponse response = JSON.parseObject(diPreSaleHistory.getPreSaleHistoryContent(), PreSaleDetailResponse.class);
        List<MaterielChecklistNewDTO> historyMaterialBomList = JSON.parseArray(diPreSaleHistory.getPreSaleMaterialHistoryContent(), MaterielChecklistNewDTO.class);
        if (CollectionUtils.isNotEmpty(historyMaterialBomList)) {
            List<String> materielNoList = historyMaterialBomList.stream().map(MaterielChecklistNewDTO::getMaterielNo).toList();
            List<DiMateriel> materielList = diMaterielService.queryMaterielByNos(materielNoList);
            Map<String, List<DiMateriel>> materielMap = materielList.stream().collect(Collectors.groupingBy(DiMateriel::getMaterielNo));
            historyMaterialBomList.forEach(historyMaterialBom -> {
                if (CollectionUtils.isNotEmpty(materielMap) && materielMap.containsKey(historyMaterialBom.getMaterielNo()) && Objects.nonNull(materielMap.get(historyMaterialBom.getMaterielNo()).get(0).getSafetyStock())) {
                    historyMaterialBom.setSafetyStock(materielMap.get(historyMaterialBom.getMaterielNo()).get(0).getSafetyStock());
                }
            });
        }
        response.setMaterielHistoryChecklistNewDTOS(historyMaterialBomList);
        MaterielVersionBomDTO materielVersionBomDTO = JSON.parseObject(diPreSaleHistory.getPreSaleMaterialBomHistoryContent(), MaterielVersionBomDTO.class);
        response.setMaterielVersionBomDTO(materielVersionBomDTO);
        //转换历史图片
        ossService.fresh(response.getSceneUrls());
        ossService.fresh(response.getPidFiles());
        ossService.fresh(response.getTechFiles());
        ossService.fresh(response.getMechineShapeFiles());
        ossService.fresh(response.getElectricFiles());
        ossService.fresh(response.getMechineProductFiles());
        ossService.fresh(response.getThreeDDesignList());
        ossService.fresh(response.getBomAnnexList());

        return response;
    }

    /**
     * 查询关联商机信息
     *
     * @param diPreSale
     * @param preSaleDetailResponse
     * @return
     */
    private PreSaleDetailResponse queryRelationNiche(DiPreSale diPreSale, PreSaleDetailResponse preSaleDetailResponse) {
        DiMarketingNiche niche = iDiMarketingNicheService.selectDiMarketingNicheById(diPreSale.getNicheId().toString(), false);
        Assert.notNull(diPreSale.getNicheId(), "商机不能为空");
        if (Objects.nonNull(niche)) {
            preSaleDetailResponse.setNicheOrderType(niche.getOrderType());
            Optional.ofNullable(niche.getDiMarketingCustomer()).ifPresent(customer -> {
                preSaleDetailResponse.setCustomerName(customer.getCompanyName());
            });
            Map<String, String> userNameMap = Maps.newHashMap();
            List<String> list = new ArrayList<>();
            if (StringUtils.isNotBlank(niche.getNicheOwner())) {
                list.add(niche.getNicheOwner());
            }

            R<List<DdUserDTO>> userListResult = remoteDbcService.queryDdUserList(QueryDdUserDTO.builder().jobNumberList(new ArrayList<>(list)).build());
            if (userListResult.isSuccess()) {
                userNameMap = userListResult.getData().stream().collect(Collectors.toMap(DdUserDTO::getJobNumber, DdUserDTO::getName));
                preSaleDetailResponse.setSalesName(userNameMap.get(niche.getNicheOwner()));
            }
            if (StringUtils.isNotBlank(niche.getNicheOwnerDept())) {
                R<SysDept> deptResult = remoteUserService.getInfoByDeptId(Long.valueOf(niche.getNicheOwnerDept()));
                if (null != deptResult.getData())
                    preSaleDetailResponse.setDeptName(deptResult.getData().getDeptName());
            }

            if (Objects.nonNull(niche.getDiMarketingNicheDemand())) {
                preSaleDetailResponse.setExpectedDeliveryTime(niche.getDiMarketingNicheDemand().getTechnicalSupportLeadTime());
            }
        }
        if (PreSaleTypeEnum.nonStandard(diPreSale.getPreSaleType()) || (Objects.nonNull(niche) && niche.getNeedTechSupport())) {
            preSaleDetailResponse.setIsShowTechnical(true);
        }
        preSaleDetailResponse.setIsEditTechnical(true);
        List<DiPreSaleQuoteDetail> preSaleQuoteDetailList = diPreSaleQuoteDetailMapper.selectList(new LambdaQueryWrapper<DiPreSaleQuoteDetail>()
                .eq(DiPreSaleQuoteDetail::getPreSaleId, diPreSale.getId()));
        if (preSaleQuoteDetailList.size() > 0) {
            for (DiPreSaleQuoteDetail quoteDetail : preSaleQuoteDetailList) {
                DiPreSaleQuote preSaleQuote = diPreSaleQuoteMapper.selectOne(new LambdaQueryWrapper<DiPreSaleQuote>()
                        .eq(DiPreSaleQuote::getId, quoteDetail.getPreSaleQuoteId()));
                if (Objects.isNull(preSaleQuote)) {
                    continue;
                }
                long orderCnt = diOrderService.lambdaQuery().eq(DiOrder::getPreSaleQuoteNo, preSaleQuote.getPreSaleQuoteCode()).count();
                if (orderCnt > 0) {
                    preSaleDetailResponse.setIsEditTechnical(false);
                    break;
                }
            }
        }

        return preSaleDetailResponse;
    }

    /**
     * 获取方案详情 tab1
     *
     * @return
     */
    private PreSaleDetailResponse queryPreSaleInfo(DiPreSale diPreSale, PreSaleDetailResponse preSaleDetailResponse) {
        //售前方案现场交接
        List<DiPreSaleUrl> diPreSaleUrls = diPreSaleUrlMapper.selectList(Wrappers.<DiPreSaleUrl>lambdaQuery().eq(DiPreSaleUrl::getDiPreSaleId, diPreSale.getId()).eq(DiPreSaleUrl::getType, "1"));
        if (CollectionUtils.isNotEmpty(diPreSaleUrls)) {
            //ossmap
            Map<String, OssPreviewDTO> ossPreviewDTOMap = ossService.getOssFileByList(OssSysCodeEnum.PRE_SALE_SCENE.getType(), diPreSaleUrls.stream().filter(diPreSaleUrl -> StringUtils.isNotEmpty(diPreSaleUrl.getFileKey())).map(DiPreSaleUrl::getFileKey).collect(Collectors.toList()), 0).stream().collect(Collectors.toMap(OssPreviewDTO::getOssKey, Function.identity()));

            List<PreSaleDetailResponse.SceneUrl> sceneUrls = new ArrayList<>();
            for (DiPreSaleUrl diPreSaleUrl : diPreSaleUrls) {

                Optional.ofNullable(ossPreviewDTOMap.get(diPreSaleUrl.getFileKey())).ifPresent(ossPreviewDTO -> {
                    PreSaleDetailResponse.SceneUrl sceneUrl = new PreSaleDetailResponse.SceneUrl();
                    sceneUrl.setFileUrl(ossPreviewDTO.getShowUrl());
                    sceneUrl.setFileName(ossPreviewDTO.getMimeName());
                    sceneUrl.setIsPic(ossPreviewDTO.getMimeType().contains("image") ? 1 : 0);
                    sceneUrl.setId(diPreSaleUrl.getId());
                    sceneUrl.setFileKey(diPreSaleUrl.getFileKey());
                    sceneUrls.add(sceneUrl);
                });

            }

            preSaleDetailResponse.setExpecteDate(diPreSale.getExpecteDate());
            preSaleDetailResponse.setCountryCode(diPreSale.getCountryCode());
            preSaleDetailResponse.setProvinceCode(diPreSale.getProvinceCode());
            preSaleDetailResponse.setCityCode(diPreSale.getCityCode());
            preSaleDetailResponse.setAreaCode(diPreSale.getAreaCode());
            //现场工作面交接表
            preSaleDetailResponse.setSceneUrls(sceneUrls);
        }
        if (diPreSale.getPreSaleType().equals(2) || diPreSale.getPreSaleType().equals(4)) {
            DiPreSaleCustomized diPreSaleCustomized = diPreSaleCustomizedMapper.selectOne(Wrappers.<DiPreSaleCustomized>lambdaQuery().eq(DiPreSaleCustomized::getPreSaleId, diPreSale.getId()).orderByDesc(DiPreSaleCustomized::getId).last("limit 1"));
            if (diPreSaleCustomized != null) {
                Map<String, String> userNameMap = Maps.newHashMap();
                R<List<DdUserDTO>> userListResult = remoteDbcService.queryDdUserList(QueryDdUserDTO.builder().jobNumberList(new ArrayList<>(Lists.newArrayList(diPreSaleCustomized.getTechSupportOwnerCode()))).build());
                if (userListResult.isSuccess()) {
                    userNameMap = userListResult.getData().stream().collect(Collectors.toMap(DdUserDTO::getJobNumber, DdUserDTO::getName, (a, b) -> a));
                    diPreSaleCustomized.setTechSupportOwnerName(userNameMap.get(diPreSaleCustomized.getTechSupportOwnerCode()));
                }
                preSaleDetailResponse.setDiPreSaleCustomized(diPreSaleCustomized);
                Long customizedId = preSaleDetailResponse.getDiPreSaleCustomized().getId();
                preSaleDetailResponse.setDiPreSaleCustomizedPlc(diPreSaleCustomizedPlcMapper.selectOne(Wrappers.<DiPreSaleCustomizedPlc>lambdaQuery().eq(DiPreSaleCustomizedPlc::getCustomizedId, customizedId)));
                preSaleDetailResponse.setDiPreSaleCustomizedPiping(diPreSaleCustomizedPipingMapper.selectOne(Wrappers.<DiPreSaleCustomizedPiping>lambdaQuery().eq(DiPreSaleCustomizedPiping::getCustomizedId, customizedId)));
                preSaleDetailResponse.setDiPreSaleCustomizedHeader(diPreSaleCustomizedHeaderMapper.selectOne(Wrappers.<DiPreSaleCustomizedHeader>lambdaQuery().eq(DiPreSaleCustomizedHeader::getCustomizedId, customizedId)));
                preSaleDetailResponse.setDiPreSaleCustomizedFan(diPreSaleCustomizedFanMapper.selectOne(Wrappers.<DiPreSaleCustomizedFan>lambdaQuery().eq(DiPreSaleCustomizedFan::getCustomizedId, customizedId)));
                preSaleDetailResponse.setDiPreSaleCustomizedBurner(diPreSaleCustomizedBurnerMapper.selectOne(Wrappers.<DiPreSaleCustomizedBurner>lambdaQuery().eq(DiPreSaleCustomizedBurner::getCustomizedId, customizedId)));
                preSaleDetailResponse.setDiPreSaleCustomizedAir(diPreSaleCustomizedAirMapper.selectOne(Wrappers.<DiPreSaleCustomizedAir>lambdaQuery().eq(DiPreSaleCustomizedAir::getCustomizedId, customizedId)));
            }
        }

//        DiMarketingNiche niche = iDiMarketingNicheService.selectDiMarketingNicheById(diPreSale.getNicheId().toString());


        return preSaleDetailResponse;
    }

    /**
     * 查询产品方案方案详情 tab2
     *
     * @param diPreSale
     * @return
     */
    private PreSaleDetailResponse queryPresaleDetail(DiPreSale diPreSale, PreSaleDetailResponse preSaleDetailResponse) {
        DiPreSaleManifest diPreSaleManifests = diPreSaleManifestMapper.selectOne(Wrappers.<DiPreSaleManifest>lambdaQuery().eq(DiPreSaleManifest::getDiPreSaleId, diPreSale.getId()).orderByDesc(DiPreSaleManifest::getId).last("limit 1"));
        if (diPreSaleManifests == null) {
            return preSaleDetailResponse;
        }
        FeeItem fee = diPreSaleFeeService.getFeeItem(diPreSale, diPreSaleManifests);
        //根据物料号查询物料信息
        MaterielInfoVo materielInfoVo = new MaterielInfoVo();
        materielInfoVo.setId(Long.valueOf(diPreSaleManifests.getMaterialVersion()));
        materielInfoVo.setNeedBom(1);

        PreSaleDetailResponse.PreSaleDisposition preSaleDisposition = new PreSaleDetailResponse.PreSaleDisposition();
        preSaleDisposition.setLength(Objects.nonNull(diPreSaleManifests.getMaterielLength()) ? String.valueOf(diPreSaleManifests.getMaterielLength()) : null);
        preSaleDisposition.setWidth(Objects.nonNull(diPreSaleManifests.getMaterielWidth()) ? String.valueOf(diPreSaleManifests.getMaterielWidth()) : null);
        preSaleDisposition.setHeight(Objects.nonNull(diPreSaleManifests.getMaterielHigh()) ? String.valueOf(diPreSaleManifests.getMaterielHigh()) : null);


        MaterielInfoDTO materielInfoDTO = iDiMaterielService.materielInfo(materielInfoVo);
        if (Objects.nonNull(materielInfoDTO)) {
            MaterielFeeQueryDTO materielFeeQueryDTO = new MaterielFeeQueryDTO();
            materielFeeQueryDTO.setMaterielId(diPreSaleManifests.getMaterialVersion());
            MaterielFeeDTO materielFeeDTO = iDiMaterielService.queryMaterielFee(materielFeeQueryDTO);

            preSaleDetailResponse.setOrderType(diPreSale.getOrderType());

            preSaleDisposition.setStockNum(materielInfoDTO.getMaterialStock());
            preSaleDisposition.setGuideDuration(PreSaleWrapper.of(diPreSale).getGuideDuration());


//            BigDecimal packageType = StringUtils.isNotEmpty(diPreSale.getPackageType()) ? new BigDecimal(diPreSale.getPackageType()) : BigDecimal.ZERO;
//            BigDecimal feeTotal = (Objects.nonNull(materielFeeDTO.getMaterielSumCosts()) ? materielFeeDTO.getMaterielSumCosts() : BigDecimal.ZERO)
//                    .add(Objects.nonNull(materielFeeDTO.getGuideProduceCosts()) ? materielFeeDTO.getGuideProduceCosts() : BigDecimal.ZERO)
//                    .add(packageType).add(Objects.nonNull(diPreSale.getInstallationFee()) ? diPreSale.getInstallationFee() : BigDecimal.ZERO)
//                    .add(Objects.nonNull(materielFeeDTO.getGuideRiskCosts()) ? materielFeeDTO.getGuideRiskCosts() : BigDecimal.ZERO)
//                    .add(Objects.nonNull(materielFeeDTO.getGuideOtherCosts()) ? materielFeeDTO.getGuideOtherCosts() : BigDecimal.ZERO);
            BigDecimal feeTotal = Objects.nonNull(fee.getFeeTotal()) ? fee.getFeeTotal() : BigDecimal.ZERO;
            preSaleDisposition.setCostFeeTotal(PreSaleWrapper.of(diPreSale).getCostFeeTotal());
            preSaleDisposition.setCostFee(PreSaleWrapper.of(diPreSale).getCostFeeTotal());

            BigDecimal techSupportDesignFee = (Objects.nonNull(fee.getTechSupportFee()) ? fee.getTechSupportFee() : BigDecimal.ZERO).add(Objects.nonNull(fee.getElectricDesignFee()) ? fee.getElectricDesignFee() : BigDecimal.ZERO).add(Objects.nonNull(fee.getMechineDesignFee()) ? fee.getMechineDesignFee() : BigDecimal.ZERO);
            preSaleDisposition.setTechSupportDesignFee(techSupportDesignFee);

            if (Objects.nonNull(materielFeeDTO.getMaterielSumCosts())) {
                preSaleDisposition.setQuotedAmount(materielFeeDTO.getMaterielSumCosts());
            } else {
                BigDecimal quotedAmount = Objects.nonNull(fee.getFeeTotal()) ? fee.getFeeTotal() : BigDecimal.ZERO;
                preSaleDisposition.setQuotedAmount(quotedAmount);
            }
            preSaleDisposition.setMaterialProductFee(Objects.nonNull(fee.getMaterialFee()) ? fee.getMaterialFee() : BigDecimal.ZERO);
            //preSaleDisposition.setProductFee(Objects.nonNull(fee.getProductFee()) ? fee.getProductFee() : BigDecimal.ZERO);

//            BigDecimal otherFee = (Objects.nonNull(fee.getDeliveryDebugFee()) ? fee.getDeliveryDebugFee() : BigDecimal.ZERO)
//                    .add(Objects.nonNull(fee.getPackageFee()) ? fee.getPackageFee() : BigDecimal.ZERO)
//                    .add(Objects.nonNull(fee.getRiskFee()) ? fee.getRiskFee() : BigDecimal.ZERO)
//                    .add(Objects.nonNull(fee.getSceneInstallFee()) ? fee.getSceneInstallFee() : BigDecimal.ZERO)
//                    .add(Objects.nonNull(fee.getGuideOtherCosts()) ? fee.getGuideOtherCosts() : BigDecimal.ZERO);
            preSaleDisposition.setOtherFee(Objects.nonNull(fee.getOtherFee()) ? fee.getOtherFee() : BigDecimal.ZERO);


            BigDecimal electricDay = (Objects.nonNull(fee.getElectricDay()) ? fee.getElectricDay() : BigDecimal.ZERO);
            BigDecimal mechineDay = (Objects.nonNull(fee.getMechineDay()) ? fee.getMechineDay() : BigDecimal.ZERO);
            Integer designDay = electricDay.compareTo(mechineDay) > 1 ? electricDay.intValue() : mechineDay.intValue();
            preSaleDisposition.setDesignDay(designDay);

            preSaleDisposition.setSupplyDay(Objects.nonNull(fee.getSupplyDay()) ? fee.getSupplyDay() : BigDecimal.ZERO);
            preSaleDisposition.setProductDay(Objects.nonNull(fee.getProductDay()) ? fee.getProductDay() : BigDecimal.ZERO);


            preSaleDisposition.setMaterialCode(materielInfoDTO.getMaterielNo());
            preSaleDisposition.setMaterialName(materielInfoDTO.getMaterielName());
            preSaleDisposition.setBrand(materielInfoDTO.getBrand());
            preSaleDisposition.setMaterialClassify(materielInfoDTO.getDydClassification());

            if (Objects.nonNull(materielInfoDTO.getMaterielWeight())) {
                preSaleDisposition.setWeight(String.valueOf(materielInfoDTO.getMaterielWeight()));
            }


            preSaleDisposition.setLogoPic(materielInfoDTO.getLogoPic());
            preSaleDisposition.setStandardQuantity(materielInfoDTO.getStandardQuantity());

            preSaleDetailResponse.setPreSaleDisposition(preSaleDisposition);

            // todo BOM 这里要替换成按版本号来查
            List<MaterielChecklistNewDTO> materielBomList = iDiMaterielService.queryMaterielPlanCheckListByVersion(diPreSaleManifests.getMaterialVersion());
            if (CollectionUtils.isNotEmpty(materielBomList)) {
                //物料bom，物料号分组
                Map<String, List<MaterielChecklistNewDTO>> materielMap = materielBomList.stream().collect(Collectors.groupingBy(MaterielChecklistNewDTO::getMaterielNo));


                List<PreSaleDetailResponse.PreSaleDisposition> preSaleDispositions = materielBomList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<MaterielChecklistNewDTO>(Comparator.comparing(s -> s.getMaterielNo()))), ArrayList::new)).stream().map(materielBomDTO -> {
                    PreSaleDetailResponse.PreSaleDisposition preSaleDispositionT = new PreSaleDetailResponse.PreSaleDisposition();
                    preSaleDispositionT.setMaterialCode(materielBomDTO.getMaterielNo());
                    preSaleDispositionT.setMaterialName(materielBomDTO.getMaterielName());
                    preSaleDispositionT.setStockNum(materielBomDTO.getMaterialStock());
                    preSaleDispositionT.setLogoPic(materielBomDTO.getLogoPic());
                    preSaleDispositionT.setBrand(materielBomDTO.getBrand());
                    if (CollectionUtils.isNotEmpty(materielMap.get(materielBomDTO.getMaterielNo()))) {
                        preSaleDispositionT.setNum(materielMap.get(materielBomDTO.getMaterielNo()).size());
                    }
                    return preSaleDispositionT;
                }).collect(Collectors.toList());
                preSaleDisposition.setDarptMaterials(preSaleDispositions);
            }

            //标签
            List<DiPreSaleLabel> preSaleLabels = diPreSaleLabelMapper.selectList(Wrappers.<DiPreSaleLabel>lambdaQuery().eq(DiPreSaleLabel::getPreSaleManifestId, diPreSaleManifests.getId()));
            if (CollectionUtils.isNotEmpty(preSaleLabels)) {

                //标签map，key为
                Map<String, List<DiPreSaleLabel>> preSaleLabelMap = preSaleLabels.stream().filter(preSaleLabel -> StringUtils.isNotEmpty(preSaleLabel.getClassificationName())).collect(Collectors.groupingBy(DiPreSaleLabel::getClassificationName));

                List<PreSaleDetailResponse.WebClassification> webClassifications = new ArrayList<>();
                for (String key : preSaleLabelMap.keySet()) {
                    PreSaleDetailResponse.WebClassification webClassification = new PreSaleDetailResponse.WebClassification();
                    webClassification.setClassificationName(key);

                    //标签
                    List<DiPreSaleLabel> labelMaterielWarehouseListDTOST = preSaleLabelMap.get(key);
                    List<PreSaleDetailResponse.Label> labels = labelMaterielWarehouseListDTOST.stream().map(labelMaterielWarehouseListDTO -> {
                        PreSaleDetailResponse.Label label = new PreSaleDetailResponse.Label();
                        label.setId(labelMaterielWarehouseListDTO.getId());
                        label.setLabelParamType(labelMaterielWarehouseListDTO.getLabelParamType());
                        label.setLabelName(labelMaterielWarehouseListDTO.getLabelName());
                        label.setLabelParamMax(labelMaterielWarehouseListDTO.getLabelParamMax());
                        label.setLabelParamMin(labelMaterielWarehouseListDTO.getLabelParamMin());
                        if (LabelParamTypeEnum.FIX.getLabelParamType().equals(labelMaterielWarehouseListDTO.getLabelParamType())) {
                            Optional.ofNullable(labelMaterielWarehouseListDTO.getLabelParam()).ifPresent(param -> label.setLabelParam(String.valueOf(labelMaterielWarehouseListDTO.getLabelParam())));
                        } else if (LabelParamTypeEnum.FIX_CLASSIFICATION.getLabelParamType().equals(labelMaterielWarehouseListDTO.getLabelParamType())) {
                            label.setLabelParam(labelMaterielWarehouseListDTO.getLabelParamClassification());
                        }
                        label.setLabelUnit(labelMaterielWarehouseListDTO.getLabelUnit());
                        label.setLabelParamValue(labelMaterielWarehouseListDTO.getLabelParamValue());
                        label.setLabelParamValueMax(labelMaterielWarehouseListDTO.getLabelParamValueMax());
                        label.setLabelParamValueMin(labelMaterielWarehouseListDTO.getLabelParamValueMin());
                        label.setLabelParamValueType(labelMaterielWarehouseListDTO.getLabelParamValueType());
                        return label;
                    }).collect(Collectors.toList());
                    webClassification.setLabels(labels);
                    webClassifications.add(webClassification);
                }
                preSaleDetailResponse.setWebClassification(webClassifications);
            }
        }
        return preSaleDetailResponse;
    }


    /**
     * 查询产品方案产品图片 tab3
     *
     * @param diPreSale
     * @return
     */
    private PreSaleDetailResponse queryPreSalePicture(DiPreSale diPreSale, PreSaleDetailResponse preSaleDetailResponse) {
        DiPreSaleManifest diPreSaleManifests = diPreSaleManifestMapper.selectOne(Wrappers.<DiPreSaleManifest>lambdaQuery().eq(DiPreSaleManifest::getDiPreSaleId, diPreSale.getId()).orderByDesc(DiPreSaleManifest::getId).last("limit 1"));
        if (diPreSaleManifests == null) {
            return preSaleDetailResponse;
        }
        List<MaterielFileDTO> materielFileDTOS = Lists.newArrayList();
        if (diPreSale.getPreSaleType() == 1) {
            MaterielInfoVo materielInfoVo = new MaterielInfoVo();
            materielInfoVo.setId(diPreSaleManifests.getMaterialVersion());
            MaterielInfoDTO materielInfoDTO = iDiMaterielService.materielInfo(materielInfoVo);
            if (Objects.isNull(materielInfoDTO)) {
                return preSaleDetailResponse;
            }
            materielFileDTOS = materielInfoDTO.getMaterielFileList();
        } else {
            List<DiPreSaleUrl> diPreSaleUrls = diPreSaleUrlMapper.selectList(Wrappers.<DiPreSaleUrl>lambdaQuery().eq(DiPreSaleUrl::getDiPreSaleId, diPreSale.getId()));
            if (Objects.isNull(diPreSaleUrls)) {
                return preSaleDetailResponse;
            }
            Map<String, OssPreviewDTO> ossPreviewDTOS = ossService.getOssFileByList(OssSysCodeEnum.MATERIEL.getType(), diPreSaleUrls.stream().map(DiPreSaleUrl::getFileKey).toList())
                    .stream().collect(Collectors.toMap(x -> x.getOssKey(), x -> x, (a, b) -> a));
            ;

            materielFileDTOS = diPreSaleUrls.stream().map(file -> {
                MaterielFileDTO materielFileDTO = new MaterielFileDTO();
                materielFileDTO.setId(file.getId().longValue());
                materielFileDTO.setFileKey(file.getFileKey());
                materielFileDTO.setFileUrl(ossPreviewDTOS.containsKey(file.getFileKey()) ? ossPreviewDTOS.get(file.getFileKey()).getShowUrl() : file.getFileUrl());
                materielFileDTO.setFileName(file.getFileName());
                materielFileDTO.setIsPic(file.getIsPic());
                materielFileDTO.setFileType(file.getType());
                return materielFileDTO;
            }).collect(Collectors.toList());
        }


        //List<MaterielFileDTO> materielFileDTOS = materielInfoDTO.getMaterielFileList();

        if (CollectionUtils.isNotEmpty(materielFileDTOS)) {
            //PID图
            preSaleDetailResponse.setPidFiles(materielFileDTOS.stream().filter(materielFileDTO -> MaterielFileTypeEnum.PID_DESIGN.getFileType()
                    .equals(materielFileDTO.getFileType())).collect(Collectors.toList()));

            //技术协议
            preSaleDetailResponse.setTechFiles(materielFileDTOS.stream().filter(materielFileDTO -> MaterielFileTypeEnum.TECHNICAL_PROTOCOL.getFileType()
                    .equals(materielFileDTO.getFileType())).collect(Collectors.toList()));
            //机械外形图
            preSaleDetailResponse.setMechineShapeFiles(materielFileDTOS.stream().filter(materielFileDTO -> MaterielFileTypeEnum.APPEARANCE.getFileType()
                    .equals(materielFileDTO.getFileType())).collect(Collectors.toList()));
            //电气图
            preSaleDetailResponse.setElectricFiles(materielFileDTOS.stream().filter(materielFileDTO -> MaterielFileTypeEnum.ELECTRIC_DESIGN.getFileType()
                    .equals(materielFileDTO.getFileType())).collect(Collectors.toList()));
            //机械生产图
            preSaleDetailResponse.setMechineProductFiles(materielFileDTOS.stream().filter(materielFileDTO -> MaterielFileTypeEnum.FABRICATION.getFileType()
                    .equals(materielFileDTO.getFileType())).collect(Collectors.toList()));

            //三维图
            preSaleDetailResponse.setThreeDDesignList(materielFileDTOS.stream().filter(materielFileDTO -> MaterielFileTypeEnum.THREE_D_DESIGN.getFileType()
                    .equals(materielFileDTO.getFileType())).collect(Collectors.toList()));

        }
        return preSaleDetailResponse;
    }

    /**
     * 查询产品方案多版本物料信息 tab4
     *
     * @param diPreSale
     * @return
     */
    private PreSaleDetailResponse queryPreSaleBom(DiPreSale diPreSale, PreSaleDetailResponse preSaleDetailResponse, boolean ignoreCache) {
        preSaleDetailResponse.setFees(this.getFeeList(diPreSale, ignoreCache));
        preSaleDetailResponse.setBomAnnexList(presaleUrlService.getFileList(diPreSale.getId(), MaterielFileTypeEnum.BOM_ANNEX.getFileType()));
        return preSaleDetailResponse;
    }

    /**
     * 优化后的查询产品方案多版本物料信息 tab4
     * 针对type=4的性能优化
     *
     * @param diPreSale
     * @param preSaleDetailResponse
     * @param ignoreCache
     * @return
     */
    private PreSaleDetailResponse queryPreSaleBomOptimized(DiPreSale diPreSale, PreSaleDetailResponse preSaleDetailResponse, boolean ignoreCache) {
        // 优化1: 使用缓存机制，避免重复计算费用
        List<PreSaleDetailResponse.Fee> fees = getFeeListOptimized(diPreSale, ignoreCache);
        preSaleDetailResponse.setFees(fees);

        // 优化2: 异步加载附件列表，减少主线程阻塞
        List<MaterielFileDTO> bomAnnexList = presaleUrlService.getFileList(diPreSale.getId(), MaterielFileTypeEnum.BOM_ANNEX.getFileType());
        preSaleDetailResponse.setBomAnnexList(bomAnnexList);

        return preSaleDetailResponse;
    }

    private PreSaleDetailResponse.Fee getFeeWithoutCalcFee(DiPreSale preSale, DiPreSaleManifest diPreSaleManifest) {
        FeeItem feeItem = diPreSaleFeeService.queryCalcFee(diPreSaleManifest);
        PreSaleDetailResponse.Fee fee = new PreSaleDetailResponse.Fee();
        BeanUtils.copyProperties(feeItem, fee);
        fee.setRealFee(diPreSaleFeeService.queryFeeForReal(diPreSaleManifest));

        MaterielFeeQueryDTO materielFeeQueryDTO = new MaterielFeeQueryDTO();
        materielFeeQueryDTO.setMaterielId(diPreSaleManifest.getMaterialVersion());
        MaterielFeeDTO materielFeeDTO = iDiMaterielService.queryMaterielFee(materielFeeQueryDTO);

        fee.setPreSaleId(diPreSaleManifest.getDiPreSaleId());
        fee.setPreSaleVersion(diPreSaleManifest.getManifestVersion().toString());
        fee.setManifestId(diPreSaleManifest.getId());
        fee.setPreSaleStatus(preSale.getPreSaleStatus());
        fee.setDeliveryReviewTechSupportFlag(preSale.getDeliveryReviewTechSupportFlag());
        fee.setMaterialCode(materielFeeDTO.getMaterielNo());
        fee.setProductName(materielFeeDTO.getMaterielName());
        fee.setUseNum(diPreSaleManifest.getUseNum());
        fee.setMaterialVersion(diPreSaleManifest.getMaterialVersion().toString());
        fee.setQuantity(preSale.getNum());
        fee.setPreSaleType(preSale.getPreSaleType());

        DiMateriel diMaterielVersion = diMaterielService.getById(diPreSaleManifest.getMaterialVersion());
        //log.info("curPreSale 2222 {}", preSale);
        Optional.ofNullable(diMaterielVersion).ifPresent(version -> {
            fee.setShowMaterialVersion(version.getShowVersionNo());
            fee.setProductStandard(version.getProductStandard());
            if (Objects.nonNull(preSale.getPreSaleTechnicalCheckTime())) { //技术支持已复核
                fee.setIsShowSyncU9(true);
                if (!MaterielSyncStatusEnum.SYNC_SUCCESS.getStatus().equals(version.getSyncStatus())) {
                    fee.setIsDiabledSyncU9(false);
                } else {
                    fee.setIsDiabledSyncU9(true);
                }
            }
        });
        Optional.ofNullable(diMaterielVersion).ifPresent(version -> fee.setShowMaterialVersion(version.getShowVersionNo()));
//        Optional.ofNullable(materielFeeDTO.getCostsValidTime())
//                .ifPresent(date -> fee.setEndTime(materielFeeDTO.getCostsValidTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate()));

        List<MaterielChecklistNewDTO> materielChecklistNewDTOS = iDiMaterielService.queryMaterielPlanCheckListByVersion(diPreSaleManifest.getMaterialVersion());

        long expireCnt = materielChecklistNewDTOS.stream().filter(dto -> dto.getCostsValidTime() == null || dto.getCostsValidTime().compareTo(new Date()) < 0).map(dto -> dto.getMaterielId()).count();
        fee.setHasNullMaterialFee(false);
        if (expireCnt > 0) {
            fee.setHasNullMaterialFee(true);
        }

        DiMarketingNiche diMarketingNiche = iDiMarketingNicheService.lambdaQuery().eq(DiMarketingNiche::getId, preSale.getNicheId()).eq(DiMarketingNiche::getDelFlag, 0).one();
        if (Objects.isNull(diMarketingNiche) || Objects.isNull(diMarketingNiche.getNeedPostSaleInstall()) || !diMarketingNiche.getNeedPostSaleInstall()) {
            fee.setNeedPostSaleInstall(false);
        } else {
            fee.setNeedPostSaleInstall(true);
        }
        DiPreSaleCustomized diPreSaleCustomized = diPreSaleCustomizedMapper.selectOne(new LambdaQueryWrapper<DiPreSaleCustomized>().eq(DiPreSaleCustomized::getPreSaleId, diPreSaleManifest.getDiPreSaleId()));

        if (PreSaleTypeEnum.nonStandard(preSale.getPreSaleType())) {
            if (diPreSaleCustomized != null) {
                fee.setDiPreSaleCustomized(diPreSaleCustomized);
            }
        }
        return fee;
    }

    @Override
    public List<MaterielChecklistNewDTO> queryTobeQuotedMaterialList(List<Long> preSalesIds, Set<Long> outPreSaleIds) {
        List<MaterielChecklistNewDTO> materielChecklistNewDTOLists = new ArrayList<>();
        preSalesIds.parallelStream().forEach(preSaleId -> {
            List<DiPreSaleManifest> newManifest = diPreSaleManifestMapper.selectList(Wrappers.<DiPreSaleManifest>lambdaQuery()
                    .in(DiPreSaleManifest::getDiPreSaleId, preSaleId));
            if (newManifest != null) {
                List<MaterielChecklistNewDTO> materielChecklistNewDTOList = Lists.newArrayList();
                newManifest.forEach(manifest -> {
                    List<MaterielChecklistNewDTO> materialCheckList = iDiMaterielService.queryMaterielPlanCheckListByVersion(Long.valueOf(manifest.getMaterialVersion()));
                    materielChecklistNewDTOList.addAll(materialCheckList);
                });

                for (MaterielChecklistNewDTO newDTO : materielChecklistNewDTOList) {
                    String inquiryNo = "";
                    boolean isNeedInquiryWithMaterial = "purchase_component".equals(newDTO.getSourceProperty()) ? false : (null == newDTO.getGuideProductionCosts() ? true : false);
                    if ((null == newDTO.getGuideMaterialCost()
                            || null == newDTO.getGuideMaterielDuration()
                            || isNeedInquiryWithMaterial
                            || null == newDTO.getCostsValidTime()
                            || newDTO.getCostsValidTime().before(new Date()))
                            && isNeedInquiry(preSaleId, newDTO.getMaterielId(), inquiryNo)) {
                        newDTO.setMaterialInquiryNo(inquiryNo);
                        materielChecklistNewDTOLists.add(newDTO);
                        if (outPreSaleIds != null) {
                            outPreSaleIds.add(preSaleId);
                        }
                    }
                }
            }
        });
        return materielChecklistNewDTOLists;
    }

    /**
     * 方案清单询价列表 tab5
     *
     * @param diPreSale
     * @return
     */
    private PreSaleDetailResponse queryPreSaleInquiry(DiPreSale diPreSale, PreSaleDetailResponse preSaleDetailResponse, boolean ignoreCalcFee) {
        //费用及各交付周期
        preSaleDetailResponse.setFees(this.getFeeList(diPreSale, ignoreCalcFee));
        //最新方案版本所对应的到期物料清单信息
        List<DiPreSaleManifest> newManifest = diPreSaleManifestMapper.selectList(Wrappers.<DiPreSaleManifest>lambdaQuery()
                .eq(DiPreSaleManifest::getDiPreSaleId, diPreSale.getId()));
        if (newManifest != null) {
//            List<MaterielChecklistNewDTO> materielChecklistNewDTOList = Lists.newArrayList();
//            newManifest.forEach(manifest -> {
//                List<MaterielChecklistNewDTO> materialCheckList = iDiMaterielService.queryMaterielPlanCheckListByVersion(Long.valueOf(manifest.getMaterialVersion()));
//                materielChecklistNewDTOList.addAll(materialCheckList);
//            });
//
//            List<MaterielChecklistNewDTO> materielChecklistNewDTOLists = new ArrayList<>();
//            for (MaterielChecklistNewDTO newDTO : materielChecklistNewDTOList) {
//                String inquiryNo = "";
//                boolean isNeedInquiryWithMaterial = "purchase_component".equals(newDTO.getSourceProperty()) ? false : (null == newDTO.getGuideProductionCosts() ? true : false);
//                if ((null == newDTO.getGuideMaterialCost()
//                        || null == newDTO.getGuideMaterielDuration()
//                        || isNeedInquiryWithMaterial
//                        || null == newDTO.getCostsValidTime()
//                        || newDTO.getCostsValidTime().before(new Date()))
//                        && isNeedInquiry(diPreSale.getId(), newDTO.getMaterielId(), inquiryNo)) {
//                    newDTO.setMaterialInquiryNo(inquiryNo);
//                    materielChecklistNewDTOLists.add(newDTO);
//                }
//            }
            preSaleDetailResponse.setMaterielChecklistNewDTOS(queryTobeQuotedMaterialList(Arrays.asList(diPreSale.getId())));
            List<MaterielChecklistNewDTO> hasBomTreeList = Lists.newArrayList();
            String inquiryNo = "";
            newManifest.forEach(manifest -> {
                List<MaterielChecklistNewDTO> materialBomTreeList = iMultiVersionMaterielService.parentNodesWithGrandchildren(manifest.getMaterialVersion());
                hasBomTreeList.addAll(materialBomTreeList);
            });
            List<MaterielChecklistNewDTO> materielProductionCostInquiryList = hasBomTreeList.stream().filter(item -> item.getGuideProductionCosts() == null && isNeedInquiry(diPreSale.getId(), item.getMaterielId(), inquiryNo)).toList();
            preSaleDetailResponse.setMaterieProductionCostlInquiryList(materielProductionCostInquiryList);
            //原物料询价列表：采购询价单列表，查询产品方案中所有版本对应的物料询价单
            FindDiMaterialInquiryFormDto dto = new FindDiMaterialInquiryFormDto();
//            dto.setPreSaleId(diPreSale.getId());
            dto.setPreSaleCode(diPreSale.getPreSaleCode());
//            dto.setPreSaleStatus(preSale.getPreSaleStatus());
            List<DiMaterialInquiryFormVo> diMaterialInquiryFormVoList = diMaterialInquiryFormMapper.findInquiryFormList(dto);
            Map<String, String> nameMap2 = commonService.getUserNameByJob(diMaterialInquiryFormVoList.stream().map(DiMaterialInquiryFormVo::getCreateBy).filter(StrUtil::isNotBlank).collect(Collectors.toList()), diMaterialInquiryFormVoList.stream().map(DiMaterialInquiryFormVo::getUpdateBy).filter(StrUtil::isNotBlank).collect(Collectors.toList()));

            for (DiMaterialInquiryFormVo diMaterialInquiryFormVo : diMaterialInquiryFormVoList) {
                if (nameMap2.containsKey(diMaterialInquiryFormVo.getCreateBy())) {
                    diMaterialInquiryFormVo.setCreateBy(nameMap2.get(diMaterialInquiryFormVo.getCreateBy()));
                }
                if (nameMap2.containsKey(diMaterialInquiryFormVo.getUpdateBy())) {
                    diMaterialInquiryFormVo.setUpdateBy(nameMap2.get(diMaterialInquiryFormVo.getUpdateBy()));
                }
                if ("2".equals(diMaterialInquiryFormVo.getIsQuoted())) {
                    diMaterialInquiryFormVo.setIsQuoted("询价中");
                } else if ("1".equals(diMaterialInquiryFormVo.getIsQuoted())) {
                    diMaterialInquiryFormVo.setIsQuoted("已完成");
                }
                DiPreSale inquiryPreSale = diPreSaleMapper.selectById(diMaterialInquiryFormVo.getPreSaleId());
                Optional.ofNullable(inquiryPreSale).ifPresent(item -> {
                    diMaterialInquiryFormVo.setPreSaleStatus(item.getPreSaleStatus());
                });

            }
            preSaleDetailResponse.setDiMaterialInquiryFormVos(diMaterialInquiryFormVoList);
            //履约任务询价列表：生产询价+实施询价列表，查询产品方案中所有版本对应的生产+实施询价单
            List<PreSaleDetailResponse.FulfillmentTask> fulfillmentTaskList = new ArrayList<>();
            //实施询价清单
            FindImplementInquiryFormDto findImplementInquiryFormDto = new FindImplementInquiryFormDto();
//            findImplementInquiryFormDto.setPreSaleId(diPreSale.getId());
            findImplementInquiryFormDto.setPreSaleCode(diPreSale.getPreSaleCode());
            List<DiImplementInquiryFormVo> diImplementInquiryFormVoList = diImplementInquiryFormMapper.findInquiryFormList(findImplementInquiryFormDto);
            Map<String, String> nameMap = commonService.getUserNameByJob(diImplementInquiryFormVoList.stream().map(DiImplementInquiryFormVo::getCreateBy).filter(StrUtil::isNotBlank).collect(Collectors.toList()), diImplementInquiryFormVoList.stream().map(DiImplementInquiryFormVo::getQuotationBy).filter(StrUtil::isNotBlank).collect(Collectors.toList()));

            for (DiImplementInquiryFormVo vo : diImplementInquiryFormVoList) {

                FindImplementInquiryDetailDto dto1 = new FindImplementInquiryDetailDto();
                dto1.setImplementInquiryId(Long.valueOf(vo.getId()));
                List<DiImplementInquiryDetailVo> voList = diImplementInquiryDetailMapper.findInquiryDetailList(dto1);
                String saleQuotation = null;
                String saleDuration = null;
                String inquiryStatusStr = "询价中";
                if (CollectionUtils.isNotEmpty(voList)) {
                    saleQuotation = String.valueOf(voList.get(0).getImplementOffer());
                    saleDuration = String.valueOf(voList.get(0).getImplementCycle());
                    if (Objects.nonNull(voList.get(0).getImplementOffer()) && Objects.nonNull(voList.get(0).getImplementCycle())) {
                        inquiryStatusStr = "已完成";
                    }
                }
                if (nameMap.containsKey(vo.getQuotationBy())) {
                    vo.setQuotationBy(nameMap.get(vo.getQuotationBy()));
                }
                if (nameMap.containsKey(vo.getCreateBy())) {
                    vo.setCreateBy(nameMap.get(vo.getCreateBy()));
                }
                PreSaleDetailResponse.FulfillmentTask fulfillmentTask = new PreSaleDetailResponse.FulfillmentTask();
                fulfillmentTask.setInquiryType("交付询价");
                fulfillmentTask.setPreSaleId(vo.getPreSaleId());
                fulfillmentTask.setPreSaleCode(vo.getPreSaleCode());
                fulfillmentTask.setVersion(vo.getPreSaleVersion());
//                fulfillmentTask.setPreSaleStatus(diPreSale.getPreSaleStatus());
                DiPreSale inquiryPreSale = diPreSaleMapper.selectById(vo.getPreSaleId());
                Optional.ofNullable(inquiryPreSale).ifPresent(preSale -> {
                    fulfillmentTask.setPreSaleStatus(preSale.getPreSaleStatus());
                });
                fulfillmentTask.setPreSaleQuotation(saleQuotation);
                fulfillmentTask.setPreSaleDuration(saleDuration);
                fulfillmentTask.setInquiryId(vo.getId());
                fulfillmentTask.setInquiryCode(vo.getImplementInquiryNo());
                fulfillmentTask.setInitiator(vo.getCreateBy());
                fulfillmentTask.setInitiatorTime(vo.getCreateTime());
                if (StringUtils.isNotBlank(vo.getQuotationBy())) {
                    fulfillmentTask.setQuoter(vo.getQuotationBy());
                }
                fulfillmentTask.setQuoterTime(vo.getQuotationTime());
                fulfillmentTask.setInquiryStatusStr(inquiryStatusStr);
                fulfillmentTask.setInquiryStatus(vo.getInquiryStatus());
                fulfillmentTaskList.add(fulfillmentTask);
            }
            //生产询价清单
            DiProductionInquiry diProductionInquiry = new DiProductionInquiry();
//            diProductionInquiry.setBusinessId(diPreSale.getId().toString());
            diProductionInquiry.setPreSaleCode(diPreSale.getPreSaleCode());
            List<DiProductionInquiry> diProductionInquiryList = diProductionInquiryMapper.selectDiProductionInquiryList(diProductionInquiry);
            Map<String, String> nameMap1 = commonService.getUserNameByJob(diProductionInquiryList.stream().map(DiProductionInquiry::getCreateBy).filter(StrUtil::isNotBlank).collect(Collectors.toList()), diProductionInquiryList.stream().map(DiProductionInquiry::getQuoter).filter(StrUtil::isNotBlank).collect(Collectors.toList()));

            for (DiProductionInquiry vo : diProductionInquiryList) {
                DiProductionInquiryInventory inquiryInventory = new DiProductionInquiryInventory();
                inquiryInventory.setInquiryNo(vo.getInquiryNo());
                List<DiProductionInquiryInventory> diProductionInquiryInventoryList = diProductionInquiryInventoryService.selectDiProductionInquiryInventoryList(inquiryInventory);
                String saleQuotation = null;
                String saleDuration = null;
                String inquiryStatusStr = "询价中";
                if (CollectionUtils.isNotEmpty(diProductionInquiryInventoryList)) {
                    saleQuotation = String.valueOf(diProductionInquiryInventoryList.get(0).getProductionQuotation());
                    saleDuration = String.valueOf(diProductionInquiryInventoryList.get(0).getProductionCycle());
                    if (Objects.nonNull(diProductionInquiryInventoryList.get(0).getProductionQuotation()) && Objects.nonNull(diProductionInquiryInventoryList.get(0).getProductionCycle())) {
                        inquiryStatusStr = "已完成";
                    }
                }
                if (nameMap1.containsKey(vo.getCreateBy())) {
                    vo.setCreateBy(nameMap1.get(vo.getCreateBy()));
                }
                if (nameMap1.containsKey(vo.getQuoter())) {
                    vo.setQuoter(nameMap1.get(vo.getQuoter()));
                }
                PreSaleDetailResponse.FulfillmentTask fulfillmentTask = new PreSaleDetailResponse.FulfillmentTask();
                fulfillmentTask.setInquiryType("生产询价");
                fulfillmentTask.setVersion(vo.getPreSaleVersion());
                fulfillmentTask.setPreSaleId(vo.getPreSaleId());
                fulfillmentTask.setPreSaleCode(vo.getPreSaleCode());
                DiPreSale inquiryPreSale = diPreSaleMapper.selectById(vo.getPreSaleId());
                Optional.ofNullable(inquiryPreSale).ifPresent(preSale -> {
                    fulfillmentTask.setPreSaleStatus(preSale.getPreSaleStatus());
                });

                fulfillmentTask.setPreSaleQuotation(saleQuotation);
                if ("询价中".equals(inquiryStatusStr)) {
                    fulfillmentTask.setPreSaleDuration(null);
                } else {
                    fulfillmentTask.setPreSaleDuration(saleDuration);
                }
                fulfillmentTask.setInquiryId(vo.getId());
                fulfillmentTask.setInquiryCode(vo.getInquiryNo());
                fulfillmentTask.setInitiator(vo.getCreateBy());
                fulfillmentTask.setInitiatorTime(vo.getCreateTime());
                fulfillmentTask.setQuoter(vo.getQuoter());
                fulfillmentTask.setQuoterTime(vo.getQuoterTime());
                fulfillmentTask.setInquiryStatusStr(inquiryStatusStr);
                fulfillmentTask.setInquiryStatus(vo.getInquiryStatus());
                fulfillmentTaskList.add(fulfillmentTask);
            }
            if (CollectionUtils.isNotEmpty(fulfillmentTaskList)) {
                preSaleDetailResponse.setFulfillmentTaskList(fulfillmentTaskList.stream().sorted(Comparator.comparing(PreSaleDetailResponse.FulfillmentTask::getInitiatorTime).reversed()).collect(Collectors.toList()));
            }
        }
        preSaleDetailResponse.setSectionList(selectionService.selectionQryByPre(diPreSale.getId()));
        return preSaleDetailResponse;
    }

    /**
     * 优化后的方案清单询价列表 tab5
     * 针对type=5的性能优化
     *
     * @param diPreSale
     * @param preSaleDetailResponse
     * @param ignoreCalcFee
     * @return
     */
    private PreSaleDetailResponse queryPreSaleInquiryOptimized(DiPreSale diPreSale, PreSaleDetailResponse preSaleDetailResponse, boolean ignoreCalcFee) {
        // 优化1: 使用优化后的费用查询方法
        preSaleDetailResponse.setFees(this.getFeeListOptimized(diPreSale, ignoreCalcFee));

        // 优化2: 批量查询物料清单，减少数据库交互
        List<DiPreSaleManifest> newManifest = diPreSaleManifestMapper.selectList(
            Wrappers.<DiPreSaleManifest>lambdaQuery()
                .eq(DiPreSaleManifest::getDiPreSaleId, diPreSale.getId())
        );

        if (CollectionUtils.isEmpty(newManifest)) {
            preSaleDetailResponse.setSectionList(selectionService.selectionQryByPre(diPreSale.getId()));
            return preSaleDetailResponse;
        }

        // 优化3: 并行处理物料询价数据
        CompletableFuture<List<MaterielChecklistNewDTO>> materielChecklistFuture =
            CompletableFuture.supplyAsync(() -> queryTobeQuotedMaterialList(Arrays.asList(diPreSale.getId())));

        CompletableFuture<List<MaterielChecklistNewDTO>> materielProductionFuture =
            CompletableFuture.supplyAsync(() -> {
                List<MaterielChecklistNewDTO> hasBomTreeList = Lists.newArrayList();
                String inquiryNo = "";
                newManifest.forEach(manifest -> {
                    List<MaterielChecklistNewDTO> materialBomTreeList =
                        iMultiVersionMaterielService.parentNodesWithGrandchildren(manifest.getMaterialVersion());
                    hasBomTreeList.addAll(materialBomTreeList);
                });
                return hasBomTreeList.stream()
                    .filter(item -> item.getGuideProductionCosts() == null &&
                                  isNeedInquiry(diPreSale.getId(), item.getMaterielId(), inquiryNo))
                    .toList();
            });

        // 优化4: 并行查询询价单数据
        CompletableFuture<List<DiMaterialInquiryFormVo>> materialInquiryFuture =
            CompletableFuture.supplyAsync(() -> {
                FindDiMaterialInquiryFormDto dto = new FindDiMaterialInquiryFormDto();
                dto.setPreSaleCode(diPreSale.getPreSaleCode());
                return diMaterialInquiryFormMapper.findInquiryFormList(dto);
            });

        CompletableFuture<List<DiImplementInquiryFormVo>> implementInquiryFuture =
            CompletableFuture.supplyAsync(() -> {
                FindImplementInquiryFormDto findImplementInquiryFormDto = new FindImplementInquiryFormDto();
                findImplementInquiryFormDto.setPreSaleCode(diPreSale.getPreSaleCode());
                return diImplementInquiryFormMapper.findInquiryFormList(findImplementInquiryFormDto);
            });

        CompletableFuture<List<DiProductionInquiry>> productionInquiryFuture =
            CompletableFuture.supplyAsync(() -> {
                DiProductionInquiry diProductionInquiry = new DiProductionInquiry();
                diProductionInquiry.setPreSaleCode(diPreSale.getPreSaleCode());
                return diProductionInquiryMapper.selectDiProductionInquiryList(diProductionInquiry);
            });

        try {
            // 等待所有异步任务完成并设置结果
            preSaleDetailResponse.setMaterielChecklistNewDTOS(materielChecklistFuture.get());
            preSaleDetailResponse.setMaterieProductionCostlInquiryList(materielProductionFuture.get());

            // 处理物料询价数据
            List<DiMaterialInquiryFormVo> diMaterialInquiryFormVoList = materialInquiryFuture.get();
            processInquiryFormList(diMaterialInquiryFormVoList);
            preSaleDetailResponse.setDiMaterialInquiryFormVos(diMaterialInquiryFormVoList);

            // 处理履约任务询价数据
            List<PreSaleDetailResponse.FulfillmentTask> fulfillmentTaskList = new ArrayList<>();

            // 处理实施询价
            List<DiImplementInquiryFormVo> diImplementInquiryFormVoList = implementInquiryFuture.get();
            fulfillmentTaskList.addAll(processImplementInquiryList(diImplementInquiryFormVoList));

            // 处理生产询价
            List<DiProductionInquiry> diProductionInquiryList = productionInquiryFuture.get();
            fulfillmentTaskList.addAll(processProductionInquiryList(diProductionInquiryList));

            if (CollectionUtils.isNotEmpty(fulfillmentTaskList)) {
                preSaleDetailResponse.setFulfillmentTaskList(
                    fulfillmentTaskList.stream()
                        .sorted(Comparator.comparing(PreSaleDetailResponse.FulfillmentTask::getInitiatorTime).reversed())
                        .collect(Collectors.toList())
                );
            }

        } catch (Exception e) {
            log.error("查询询价数据异常，方案ID: {}, 错误: {}", diPreSale.getId(), e.getMessage(), e);
            // 降级处理：使用原有的同步方式
            return queryPreSaleInquiry(diPreSale, preSaleDetailResponse, ignoreCalcFee);
        }

        preSaleDetailResponse.setSectionList(selectionService.selectionQryByPre(diPreSale.getId()));
        return preSaleDetailResponse;
    }

    /**
     * 处理物料询价表单列表
     *
     * @param diMaterialInquiryFormVoList
     */
    private void processInquiryFormList(List<DiMaterialInquiryFormVo> diMaterialInquiryFormVoList) {
        if (CollectionUtils.isEmpty(diMaterialInquiryFormVoList)) {
            return;
        }

        // 批量获取用户名称
        List<String> userCodes = diMaterialInquiryFormVoList.stream()
            .flatMap(vo -> Stream.of(vo.getCreateBy(), vo.getUpdateBy()))
            .filter(StrUtil::isNotBlank)
            .distinct()
            .collect(Collectors.toList());

        Map<String, String> nameMap = commonService.getUserNameByJob(userCodes, new ArrayList<>());

        for (DiMaterialInquiryFormVo vo : diMaterialInquiryFormVoList) {
            if (nameMap.containsKey(vo.getCreateBy())) {
                vo.setCreateBy(nameMap.get(vo.getCreateBy()));
            }
            if (nameMap.containsKey(vo.getUpdateBy())) {
                vo.setUpdateBy(nameMap.get(vo.getUpdateBy()));
            }

            // 设置询价状态
            if ("2".equals(vo.getIsQuoted())) {
                vo.setIsQuoted("询价中");
            } else if ("1".equals(vo.getIsQuoted())) {
                vo.setIsQuoted("已完成");
            }

            // 设置方案状态
            DiPreSale inquiryPreSale = diPreSaleMapper.selectById(vo.getPreSaleId());
            Optional.ofNullable(inquiryPreSale).ifPresent(item -> {
                vo.setPreSaleStatus(item.getPreSaleStatus());
            });
        }
    }

    /**
     * 处理实施询价列表
     *
     * @param diImplementInquiryFormVoList
     * @return
     */
    private List<PreSaleDetailResponse.FulfillmentTask> processImplementInquiryList(List<DiImplementInquiryFormVo> diImplementInquiryFormVoList) {
        if (CollectionUtils.isEmpty(diImplementInquiryFormVoList)) {
            return new ArrayList<>();
        }

        // 批量获取用户名称
        List<String> userCodes = diImplementInquiryFormVoList.stream()
            .flatMap(vo -> Stream.of(vo.getCreateBy(), vo.getQuotationBy()))
            .filter(StrUtil::isNotBlank)
            .distinct()
            .collect(Collectors.toList());

        Map<String, String> nameMap = commonService.getUserNameByJob(userCodes, new ArrayList<>());

        return diImplementInquiryFormVoList.stream().map(vo -> {
            FindImplementInquiryDetailDto dto1 = new FindImplementInquiryDetailDto();
            dto1.setImplementInquiryId(Long.valueOf(vo.getId()));
            List<DiImplementInquiryDetailVo> voList = diImplementInquiryDetailMapper.findInquiryDetailList(dto1);

            String saleQuotation = null;
            String saleDuration = null;
            String inquiryStatusStr = "询价中";

            if (CollectionUtils.isNotEmpty(voList)) {
                saleQuotation = String.valueOf(voList.get(0).getImplementOffer());
                saleDuration = String.valueOf(voList.get(0).getImplementCycle());
                if (Objects.nonNull(voList.get(0).getImplementOffer()) && Objects.nonNull(voList.get(0).getImplementCycle())) {
                    inquiryStatusStr = "已完成";
                }
            }

            PreSaleDetailResponse.FulfillmentTask fulfillmentTask = new PreSaleDetailResponse.FulfillmentTask();
            fulfillmentTask.setInquiryType("交付询价");
            fulfillmentTask.setPreSaleId(vo.getPreSaleId());
            fulfillmentTask.setPreSaleCode(vo.getPreSaleCode());
            fulfillmentTask.setVersion(vo.getPreSaleVersion());

            DiPreSale inquiryPreSale = diPreSaleMapper.selectById(vo.getPreSaleId());
            Optional.ofNullable(inquiryPreSale).ifPresent(preSale -> {
                fulfillmentTask.setPreSaleStatus(preSale.getPreSaleStatus());
            });

            fulfillmentTask.setPreSaleQuotation(saleQuotation);
            fulfillmentTask.setPreSaleDuration(saleDuration);
            fulfillmentTask.setInquiryId(vo.getId());
            fulfillmentTask.setInquiryCode(vo.getImplementInquiryNo());
            fulfillmentTask.setInitiator(nameMap.getOrDefault(vo.getCreateBy(), vo.getCreateBy()));
            fulfillmentTask.setInitiatorTime(vo.getCreateTime());
            fulfillmentTask.setQuoter(nameMap.getOrDefault(vo.getQuotationBy(), vo.getQuotationBy()));
            fulfillmentTask.setQuoterTime(vo.getQuotationTime());
            fulfillmentTask.setInquiryStatusStr(inquiryStatusStr);
            fulfillmentTask.setInquiryStatus(vo.getInquiryStatus());

            return fulfillmentTask;
        }).collect(Collectors.toList());
    }

    /**
     * 处理生产询价列表
     *
     * @param diProductionInquiryList
     * @return
     */
    private List<PreSaleDetailResponse.FulfillmentTask> processProductionInquiryList(List<DiProductionInquiry> diProductionInquiryList) {
        if (CollectionUtils.isEmpty(diProductionInquiryList)) {
            return new ArrayList<>();
        }

        // 批量获取用户名称
        List<String> userCodes = diProductionInquiryList.stream()
            .flatMap(vo -> Stream.of(vo.getCreateBy(), vo.getQuoter()))
            .filter(StrUtil::isNotBlank)
            .distinct()
            .collect(Collectors.toList());

        Map<String, String> nameMap = commonService.getUserNameByJob(userCodes, new ArrayList<>());

        return diProductionInquiryList.stream().map(vo -> {
            DiProductionInquiryInventory inquiryInventory = new DiProductionInquiryInventory();
            inquiryInventory.setInquiryNo(vo.getInquiryNo());
            List<DiProductionInquiryInventory> diProductionInquiryInventoryList =
                diProductionInquiryInventoryService.selectDiProductionInquiryInventoryList(inquiryInventory);

            String saleQuotation = null;
            String saleDuration = null;
            String inquiryStatusStr = "询价中";

            if (CollectionUtils.isNotEmpty(diProductionInquiryInventoryList)) {
                saleQuotation = String.valueOf(diProductionInquiryInventoryList.get(0).getProductionQuotation());
                saleDuration = String.valueOf(diProductionInquiryInventoryList.get(0).getProductionCycle());
                if (Objects.nonNull(diProductionInquiryInventoryList.get(0).getProductionQuotation()) &&
                    Objects.nonNull(diProductionInquiryInventoryList.get(0).getProductionCycle())) {
                    inquiryStatusStr = "已完成";
                }
            }

            PreSaleDetailResponse.FulfillmentTask fulfillmentTask = new PreSaleDetailResponse.FulfillmentTask();
            fulfillmentTask.setInquiryType("生产询价");
            fulfillmentTask.setVersion(vo.getPreSaleVersion());
            fulfillmentTask.setPreSaleId(vo.getPreSaleId());
            fulfillmentTask.setPreSaleCode(vo.getPreSaleCode());

            DiPreSale inquiryPreSale = diPreSaleMapper.selectById(vo.getPreSaleId());
            Optional.ofNullable(inquiryPreSale).ifPresent(preSale -> {
                fulfillmentTask.setPreSaleStatus(preSale.getPreSaleStatus());
            });

            fulfillmentTask.setPreSaleQuotation(saleQuotation);
            if ("询价中".equals(inquiryStatusStr)) {
                fulfillmentTask.setPreSaleDuration(null);
            } else {
                fulfillmentTask.setPreSaleDuration(saleDuration);
            }
            fulfillmentTask.setInquiryId(vo.getId());
            fulfillmentTask.setInquiryCode(vo.getInquiryNo());
            fulfillmentTask.setInitiator(nameMap.getOrDefault(vo.getCreateBy(), vo.getCreateBy()));
            fulfillmentTask.setInitiatorTime(vo.getCreateTime());
            fulfillmentTask.setQuoter(nameMap.getOrDefault(vo.getQuoter(), vo.getQuoter()));
            fulfillmentTask.setQuoterTime(vo.getQuoterTime());
            fulfillmentTask.setInquiryStatusStr(inquiryStatusStr);
            fulfillmentTask.setInquiryStatus(vo.getInquiryStatus());

            return fulfillmentTask;
        }).collect(Collectors.toList());
    }

    /**
     * 判断当前方案物料是否需要询价
     *
     * @param preSaleId
     * @param materialId
     * @param inquiryNo
     * @return
     */
    private boolean isNeedInquiry(Long preSaleId, Long materialId, String inquiryNo) {
        FindDiMaterialInquiryFormDto dto = new FindDiMaterialInquiryFormDto();
        dto.setPreSaleId(preSaleId);
        List<DiMaterialInquiryFormVo> diMaterialInquiryFormVoList = diMaterialInquiryFormMapper.findInquiryFormList(dto);
        for (DiMaterialInquiryFormVo inquiryForm : diMaterialInquiryFormVoList) {
            if (Objects.isNull(inquiryForm.getQuotedStatus()) || inquiryForm.getQuotedStatus().equals(4)) {
                continue;
            }
            DiMaterialInquiryDetail inquiryDetail = new DiMaterialInquiryDetail();
            inquiryDetail.setMaterialInquiryId(Long.valueOf(inquiryForm.getId()));
            inquiryDetail.setMaterialId(materialId);
            List<DiMaterialInquiryDetail> diMaterialInquiryDetailList = diMaterialInquiryDetailService.selectDiMaterialInquiryDetailList(inquiryDetail);
            if (CollectionUtil.isNotEmpty(diMaterialInquiryDetailList)) {
                inquiryNo = inquiryForm.getMaterialInquiryNo();
                return false;
            }
        }
        return true;
    }

    /**
     * 物料询价
     *
     * @param id
     * @return
     */
    @Override
    public DiMaterialInquiryForm materialInquiry(Long id, String expectCompleteTime) {
        DiPreSale diPreSale = diPreSaleMapper.selectById(id);
        List<DiPreSale> preSales = diPreSaleMapper.selectList(Wrappers.<DiPreSale>lambdaQuery().eq(DiPreSale::getPreSaleCode, diPreSale.getPreSaleCode()));
        //产品方案物料
        List<DiPreSaleManifest> diPreSaleManifests = diPreSaleManifestMapper.selectList(Wrappers.<DiPreSaleManifest>lambdaQuery()
                .in(DiPreSaleManifest::getDiPreSaleId, preSales.stream().map(DiPreSale::getId).toList())
                .orderByDesc(DiPreSaleManifest::getId));
        if (CollectionUtils.isEmpty(diPreSaleManifests)) {
            throw new ServiceException("产品方案物料为空");
        }
        //物料号
        Integer preSaleVersion = 1;
        if (1 < diPreSaleManifests.size()) {
            DiPreSaleManifest diPreSaleManifest = diPreSaleManifests.stream().max(Comparator.comparingInt(DiPreSaleManifest::getManifestVersion)).orElse(null);
            //最新版本物料号
            preSaleVersion = diPreSaleManifest.getManifestVersion();
        }
        List<MaterielChecklistNewDTO> materielList = queryTobeQuotedMaterialList(Arrays.asList(diPreSale.getId()));
        if (CollectionUtils.isEmpty(materielList)) {
            throw new ServiceException("物料有效期为空，或者没有过期的物料清单");
        }
        SaveDiMaterialInquiryFormDto dto = new SaveDiMaterialInquiryFormDto();
        List<SaveDiMaterialInquiryDetailDto> detailDtoList = new ArrayList<>();
        for (MaterielChecklistNewDTO newD : materielList) {
            SaveDiMaterialInquiryDetailDto detailDto = new SaveDiMaterialInquiryDetailDto();
            detailDto.setMaterialId(newD.getMaterielId());
            detailDto.setMaterielNo(newD.getMaterielNo());
            detailDto.setMaterielName(newD.getMaterielName());
            detailDto.setBrand(newD.getBrand());
            detailDto.setPatternNo(newD.getPatternNo());
            detailDto.setProductStandard(newD.getProductStandard());
            detailDto.setMaterielProperty(newD.getSourceProperty());
            detailDto.setConsumeQuantity(newD.getUseNum());
            detailDto.setMaterielVersion(newD.getMaterielId().toString());
            detailDto.setMaterielVersionNo(newD.getVersionNo());
            detailDto.setDydClassification(newD.getDydClassification());
            detailDto.setSourceProperty(newD.getSourceProperty());
            detailDto.setIsEntrustProduce(newD.getIsEntrustProduce());
            detailDto.setIsHasBom(newD.getIsHasBom());
            detailDto.setParentMaterielNo(newD.getParentMaterielNo());
            detailDto.setParentVersionNo(newD.getParentVersionNo());
            detailDto.setGuideSumCosts(newD.getGuideSumCosts());
            detailDto.setMaterialStock(newD.getMaterialStock());
            detailDto.setIsNew("2");
            if (null != newD.getCostsValidTime()) {
                detailDto.setValidityDate(LocalDate.ofInstant(newD.getCostsValidTime().toInstant(), ZoneId.systemDefault()));
            }
            detailDtoList.add(detailDto);
        }
        dto.setPreSaleId(diPreSale.getId());
        dto.setPreSaleCode(diPreSale.getPreSaleCode());
        dto.setInquiryType(1);
        dto.setPreSaleVersion(preSaleVersion);
        dto.setInquirySource("1");
        dto.setDetailDto(detailDtoList);
        DateFormat fmt = new SimpleDateFormat("yyyy-MM-dd");
        Optional.ofNullable(expectCompleteTime).ifPresent(time -> {
            try {
                dto.setExpectCompleteTime(fmt.parse(time));
            } catch (ParseException e) {
                throw new RuntimeException(e);
            }
        });
        DiMaterialInquiryForm inquiry = diMaterialInquiryFormService.addInquiryForm(dto);
        //获取部门下所有员工
        SysUser user = new SysUser();
        user.setDeptId(PURCHASE_DEPT_ID);
        List<SysUser> userList = remoteUserService.findUserListByCondition(user);
        if (CollectionUtil.isEmpty(userList)) {
            log.error("DiPreSaleServiceImpl---materialInquiry()---根据部门ID未获取到员工列表，部门ID：{}", PURCHASE_DEPT_ID);
            return inquiry;
        }
        //员工工号
        List<String> jobNumList = new ArrayList<>();
        //发送钉钉消息
        String title = StrUtil.format(DingTalkEnum.MATERIAL_INQUIRY_ADD_NOTICE.getTitle(), inquiry.getMaterialInquiryNo());
        String content = StrUtil.format(DingTalkEnum.MATERIAL_INQUIRY_ADD_NOTICE.getMessage(), diPreSale.getNicheCode(), diPreSale.getPreSaleCode(), SecurityUtils.getLoginUser().getSysUser().getNickName());
        DiMessageList diMessageList = new DiMessageList();
        diMessageList.setBusinessModule("询价单");
        diMessageList.setTitle(title);
        diMessageList.setRemarks(diMessageList.getTitle());
        diMessageList.setContent(content);
        diMessageList.setSendingTime(new Date());
        userList.forEach(item -> {
            jobNumList.add(item.getUserName());
            diMessageList.setSendingUser(item.getUserName());
            diMessageListService.insertDiMessageList(diMessageList);
        });
        //新增成功后生成待办任务
        AgencyTaskInfoDTO agencyTaskInfoDto = new AgencyTaskInfoDTO();
        agencyTaskInfoDto.setBusinessKey(inquiry.getMaterialInquiryNo());
        agencyTaskInfoDto.setProjectNo(getProjectNo(diPreSale.getNicheCode()));
        agencyTaskInfoDto.setTaskTypeEnum(AgencyTaskTypeEnum.PURCHASE_MANAGE_INQUIRY);
        agencyTaskInfoDto.setTaskName(diPreSale.getPreSaleName());
        agencyTaskInfoDto.setTaskStateDesc("询价中");
        agencyTaskInfoDto.setLiabilityByList(jobNumList);
        agencyTaskInfoDto.setType("1");
        String topic = AgencyConstants.AGENCY_TASK_TOPIC + AgencyConstants.DELIMITER_COLON + AgencyConstants.AGENCY_TASK_TAG;
        rocketMQTemplate.syncSend(topic, agencyTaskInfoDto);
        return inquiry;
    }

    @Override
    public DiMaterialInquiryForm materialMfgFeeInquiry(Long id, String expectCompleteTime) {
        DiPreSale diPreSale = diPreSaleMapper.selectById(id);
//        List<DiPreSale> preSales = diPreSaleMapper.selectList(Wrappers.<DiPreSale>lambdaQuery().eq(DiPreSale::getPreSaleCode, diPreSale.getPreSaleCode()));
        //产品方案物料
        List<DiPreSaleManifest> diPreSaleManifests = diPreSaleManifestMapper.selectList(Wrappers.<DiPreSaleManifest>lambdaQuery().in(DiPreSaleManifest::getDiPreSaleId, id).orderByDesc(DiPreSaleManifest::getId));
        if (CollectionUtils.isEmpty(diPreSaleManifests)) {
            throw new ServiceException("产品方案物料为空");
        }
        //物料号
        Integer preSaleVersion = 1;
        if (1 < diPreSaleManifests.size()) {
            DiPreSaleManifest diPreSaleManifest = diPreSaleManifests.stream().max(Comparator.comparingInt(DiPreSaleManifest::getManifestVersion)).orElse(null);
            //最新版本物料号
            preSaleVersion = diPreSaleManifest.getManifestVersion();
        }
        List<MaterielChecklistNewDTO> hasBomTreeList = Lists.newArrayList();
        String inquiryNo = "";
        diPreSaleManifests.forEach(manifest -> {
            List<MaterielChecklistNewDTO> materialBomTreeList = iMultiVersionMaterielService.parentNodesWithGrandchildren(manifest.getMaterialVersion());
            hasBomTreeList.addAll(materialBomTreeList);
        });
        List<MaterielChecklistNewDTO> materielList = hasBomTreeList.stream().filter(item -> item.getGuideProductionCosts() == null && isNeedInquiry(diPreSale.getId(), item.getMaterielId(), inquiryNo)).toList();
        if (CollectionUtils.isEmpty(materielList)) {
            throw new ServiceException("没有可以询价的物料清单");
        }
        SaveDiMaterialInquiryFormDto dto = new SaveDiMaterialInquiryFormDto();
        List<SaveDiMaterialInquiryDetailDto> detailDtoList = new ArrayList<>();
        for (MaterielChecklistNewDTO newD : materielList) {
            SaveDiMaterialInquiryDetailDto detailDto = new SaveDiMaterialInquiryDetailDto();
            detailDto.setMaterialId(newD.getMaterielId());
            detailDto.setMaterielNo(newD.getMaterielNo());
            detailDto.setMaterielName(newD.getMaterielName());
            detailDto.setBrand(newD.getBrand());
            detailDto.setPatternNo(newD.getPatternNo());
            detailDto.setProductStandard(newD.getProductStandard());
            detailDto.setMaterielProperty(newD.getSourceProperty());
            detailDto.setConsumeQuantity(newD.getUseNum());
            detailDto.setMaterielVersion(newD.getMaterielId().toString());
            detailDto.setMaterielVersionNo(newD.getVersionNo());
            detailDto.setDydClassification(newD.getDydClassification());
            detailDto.setSourceProperty(newD.getSourceProperty());
            detailDto.setIsEntrustProduce(newD.getIsEntrustProduce());
            detailDto.setIsHasBom(newD.getIsHasBom());
            detailDto.setParentMaterielNo(newD.getParentMaterielNo());
            detailDto.setParentVersionNo(newD.getParentVersionNo());
            detailDto.setGuideSumCosts(newD.getGuideSumCosts());
            detailDto.setMaterialStock(newD.getMaterialStock());
            detailDto.setIsNew("2");
            if (null != newD.getCostsValidTime()) {
                detailDto.setValidityDate(LocalDate.ofInstant(newD.getCostsValidTime().toInstant(), ZoneId.systemDefault()));
            }
            detailDtoList.add(detailDto);
        }
        dto.setPreSaleId(diPreSale.getId());
        dto.setPreSaleCode(diPreSale.getPreSaleCode());
        dto.setInquiryType(3);
        dto.setPreSaleVersion(preSaleVersion);
        dto.setInquirySource("1");
        dto.setDetailDto(detailDtoList);
        DateFormat fmt = new SimpleDateFormat("yyyy-MM-dd");
        Optional.ofNullable(expectCompleteTime).ifPresent(time -> {
            try {
                dto.setExpectCompleteTime(fmt.parse(time));
            } catch (ParseException e) {
                throw new RuntimeException(e);
            }
        });
        DiMaterialInquiryForm inquiry = diMaterialInquiryFormService.addInquiryForm(dto);
        //获取部门下所有员工
        SysUser user = new SysUser();
        user.setDeptId(PURCHASE_DEPT_ID);
        List<SysUser> userList = remoteUserService.findUserListByCondition(user);
        if (CollectionUtil.isEmpty(userList)) {
            log.error("DiPreSaleServiceImpl---materialMfgFeeInquiry()---根据部门ID未获取到员工列表，部门ID：{}", PURCHASE_DEPT_ID);
            return inquiry;
        }
        //员工工号
        List<String> jobNumList = new ArrayList<>();
        //发送钉钉消息
        String title = StrUtil.format(DingTalkEnum.MFG_FEE_INQUIRY_ADD_NOTICE.getTitle(), inquiry.getMaterialInquiryNo());
        String content = StrUtil.format(DingTalkEnum.MFG_FEE_INQUIRY_ADD_NOTICE.getMessage(), diPreSale.getNicheCode(), diPreSale.getPreSaleCode(), SecurityUtils.getLoginUser().getSysUser().getNickName());
        DiMessageList diMessageList = new DiMessageList();
        diMessageList.setBusinessModule("询价单");
        diMessageList.setTitle(title);
        diMessageList.setRemarks(diMessageList.getTitle());
        diMessageList.setContent(content);
        diMessageList.setSendingTime(new Date());
        userList.forEach(item -> {
            jobNumList.add(item.getUserName());
            diMessageList.setSendingUser(item.getUserName());
            diMessageListService.insertDiMessageList(diMessageList);
        });
        //新增成功后生成待办任务
        AgencyTaskInfoDTO agencyTaskInfoDto = new AgencyTaskInfoDTO();
        agencyTaskInfoDto.setBusinessKey(inquiry.getMaterialInquiryNo());
        agencyTaskInfoDto.setProjectNo(getProjectNo(diPreSale.getNicheCode()));
        agencyTaskInfoDto.setTaskTypeEnum(AgencyTaskTypeEnum.PURCHASE_MANAGE_MFG_FEE);
        agencyTaskInfoDto.setTaskName(diPreSale.getPreSaleName());
        agencyTaskInfoDto.setTaskStateDesc("询价中");
        agencyTaskInfoDto.setLiabilityByList(jobNumList);
        agencyTaskInfoDto.setType("1");
        String topic = AgencyConstants.AGENCY_TASK_TOPIC + AgencyConstants.DELIMITER_COLON + AgencyConstants.AGENCY_TASK_TAG;
        rocketMQTemplate.syncSend(topic, agencyTaskInfoDto);
        return inquiry;
    }

    @Override
    public String productionInquiry(Long id) {
        DiPreSale diPreSale = diPreSaleMapper.selectById(id);
        //产品方案物料
        List<DiPreSaleManifest> diPreSaleManifests = diPreSaleManifestMapper.selectList(Wrappers.<DiPreSaleManifest>lambdaQuery().eq(DiPreSaleManifest::getDiPreSaleId, id));
        //物料号
        String materialCode = "";
        Integer preSaleVersion = 1;
        Long materialVersion = 0L;
        if (1 < diPreSaleManifests.size()) {
            DiPreSaleManifest diPreSaleManifest = diPreSaleManifests.stream().max(Comparator.comparingInt(DiPreSaleManifest::getManifestVersion)).orElse(null);
            if (null != diPreSaleManifest) {
                //最新版本物料号
                materialCode = diPreSaleManifest.getMaterialCode();
                preSaleVersion = diPreSaleManifest.getManifestVersion();
                materialVersion = diPreSaleManifest.getMaterialVersion();
            } else {
                materialCode = diPreSaleManifests.get(0).getMaterialCode();
            }
        } else {
            materialCode = diPreSaleManifests.get(0).getMaterialCode();
            preSaleVersion = diPreSaleManifests.get(0).getManifestVersion();
            materialVersion = diPreSaleManifests.get(0).getMaterialVersion();
        }
        DiProductionInquiry diProductionInquiry = new DiProductionInquiry();
        diProductionInquiry.setBelonging("产品方案");
        diProductionInquiry.setBusinessId(diPreSale.getId().toString());
        diProductionInquiry.setPreSaleCode(diPreSale.getPreSaleCode());
        diProductionInquiry.setPreSaleId(diPreSale.getId());
        diProductionInquiry.setPreSaleVersion(preSaleVersion);

        List<DiProductionInquiryInventory> inquiryInventories = new ArrayList<>();
        DiProductionInquiryInventory vo = new DiProductionInquiryInventory();
        vo.setMaterialNumber(materialCode);
        vo.setMaterialVersion(materialVersion.toString());
        inquiryInventories.add(vo);
        diProductionInquiry.setDiProductionInquiryInventoryList(inquiryInventories);
        String no = diProductionInquiryService.insertDiProductionInquiry(diProductionInquiry);
        //获取部门下所有员工
        SysUser user = new SysUser();
        user.setDeptId(PRODUCE_DEPT_ID);
        List<SysUser> userList = remoteUserService.findUserListByCondition(user);
        if (CollectionUtil.isEmpty(userList)) {
            //根据生产部门ID未获取到用户列表
            log.error("DiPreSaleServiceImpl---productionInquiry()---根据部门ID未获取到员工列表，部门ID：{}", PRODUCE_DEPT_ID);
            return no;
        }
        //员工工号
        List<String> jobNumList = new ArrayList<>();
        //发送钉钉消息
        String title = StrUtil.format(DingTalkEnum.PRODUCE_INQUIRY_ADD_NOTICE.getTitle(), no);
        String content = StrUtil.format(DingTalkEnum.PRODUCE_INQUIRY_ADD_NOTICE.getMessage(), diPreSale.getNicheCode(), diPreSale.getPreSaleCode(), SecurityUtils.getLoginUser().getSysUser().getNickName());
        DiMessageList diMessageList = new DiMessageList();
        diMessageList.setBusinessModule("询价单");
        diMessageList.setTitle(title);
        diMessageList.setRemarks(diMessageList.getTitle());
        diMessageList.setContent(content);
        diMessageList.setSendingTime(new Date());
        userList.forEach(item -> {
            jobNumList.add(item.getUserName());
            diMessageList.setSendingUser(item.getUserName());
            diMessageListService.insertDiMessageList(diMessageList);
        });
        //新增成功后生成待办任务
        AgencyTaskInfoDTO agencyTaskInfoDto = new AgencyTaskInfoDTO();
        agencyTaskInfoDto.setBusinessKey(no);
        agencyTaskInfoDto.setProjectNo(getProjectNo(diPreSale.getNicheCode()));
        agencyTaskInfoDto.setTaskTypeEnum(AgencyTaskTypeEnum.PRODUCE_INQUIRY);
        agencyTaskInfoDto.setTaskName(diPreSale.getPreSaleName());
        agencyTaskInfoDto.setTaskStateDesc("询价中");
        agencyTaskInfoDto.setLiabilityByList(jobNumList);
        agencyTaskInfoDto.setType("1");
        String topic = AgencyConstants.AGENCY_TASK_TOPIC + AgencyConstants.DELIMITER_COLON + AgencyConstants.AGENCY_TASK_TAG;
        rocketMQTemplate.syncSend(topic, agencyTaskInfoDto);
        return no;
    }

    @Override
    public String implementInquiry(Long id) {
        DiPreSale diPreSale = diPreSaleMapper.selectById(id);
        //产品方案物料
        List<DiPreSaleManifest> diPreSaleManifests = diPreSaleManifestMapper.selectList(Wrappers.<DiPreSaleManifest>lambdaQuery().eq(DiPreSaleManifest::getDiPreSaleId, id));
        //物料号
        String materialCode = "";
        Integer preSaleVersion = 1;
        Long materialVersion = 0L;
        if (1 < diPreSaleManifests.size()) {
            DiPreSaleManifest diPreSaleManifest = diPreSaleManifests.stream().max(Comparator.comparingInt(DiPreSaleManifest::getManifestVersion)).orElse(null);
            if (null != diPreSaleManifest) {
                //最新版本物料号
                materialCode = diPreSaleManifest.getMaterialCode();
                preSaleVersion = diPreSaleManifest.getManifestVersion();
                materialVersion = diPreSaleManifest.getMaterialVersion();
            } else {
                materialCode = diPreSaleManifests.get(0).getMaterialCode();
            }
        } else {
            materialCode = diPreSaleManifests.get(0).getMaterialCode();
            preSaleVersion = diPreSaleManifests.get(0).getManifestVersion();
        }
        SaveDiImplementInquiryFormDto diProductionInquiry = new SaveDiImplementInquiryFormDto();
        diProductionInquiry.setPreSaleCode(diPreSale.getPreSaleCode());
        diProductionInquiry.setPreSaleId(Long.valueOf(diPreSale.getId()));
        diProductionInquiry.setInquirySource("1");
        diProductionInquiry.setPreSaleVersion(preSaleVersion);
        List<DiImplementInquiryDetail> inquiryDetailList = new ArrayList<>();
        DiImplementInquiryDetail detailDto = new DiImplementInquiryDetail();
        detailDto.setMaterielNo(materialCode);
        detailDto.setMaterialVersion(materialVersion.toString());
        inquiryDetailList.add(detailDto);
        diProductionInquiry.setInquiryDetailList(inquiryDetailList);
        String no = diImplementInquiryFormService.addInquiryForm(diProductionInquiry);
        //获取部门下所有员工
        SysUser user = new SysUser();
        user.setDeptId(AFTER_SALE_DEPT_ID);
        List<SysUser> userList = remoteUserService.findUserListByCondition(user);
        if (CollectionUtil.isEmpty(userList)) {
            //根据生产部门ID未获取到用户列表
            log.error("DiPreSaleServiceImpl---implementInquiry()---根据部门ID未获取到员工列表，部门ID：{}", AFTER_SALE_DEPT_ID);
            return no;
        }
        List<String> jobNumList = new ArrayList<>();
        //发送钉钉消息
        String title = StrUtil.format(DingTalkEnum.INSTALL_DEBUG_ADD_NOTICE.getTitle(), no);
        String content = StrUtil.format(DingTalkEnum.INSTALL_DEBUG_ADD_NOTICE.getMessage(), diPreSale.getNicheCode(), diPreSale.getPreSaleCode(), SecurityUtils.getLoginUser().getSysUser().getNickName());
        DiMessageList diMessageList = new DiMessageList();
        diMessageList.setBusinessModule("询价单");
        diMessageList.setTitle(title);
        diMessageList.setRemarks(diMessageList.getTitle());
        diMessageList.setContent(content);
        diMessageList.setSendingTime(new Date());
        userList.forEach(item -> {
            jobNumList.add(item.getUserName());
            diMessageList.setSendingUser(item.getUserName());
            diMessageListService.insertDiMessageList(diMessageList);
        });
        //新增成功后生成待办任务
        AgencyTaskInfoDTO agencyTaskInfoDto = new AgencyTaskInfoDTO();
        agencyTaskInfoDto.setBusinessKey(no);
        agencyTaskInfoDto.setProjectNo(getProjectNo(diPreSale.getNicheCode()));
        agencyTaskInfoDto.setTaskTypeEnum(AgencyTaskTypeEnum.INSTALL_DEBUG_MANAGE);
        agencyTaskInfoDto.setTaskName(diPreSale.getPreSaleName());
        agencyTaskInfoDto.setTaskStateDesc("询价中");
        agencyTaskInfoDto.setLiabilityByList(jobNumList);
        agencyTaskInfoDto.setType("1");
        String topic = AgencyConstants.AGENCY_TASK_TOPIC + AgencyConstants.DELIMITER_COLON + AgencyConstants.AGENCY_TASK_TAG;
        rocketMQTemplate.syncSend(topic, agencyTaskInfoDto);
        return no;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String technicalSupport(Long id, String techSupportOwnerCode) {
        DiPreSale diPreSale1 = diPreSaleMapper.selectById(id);
        PreSaleDetailResponse preSaleDetailResponse = new PreSaleDetailResponse();
        DiPreSaleCustomized diPreSaleCustomized = diPreSaleCustomizedMapper.selectOne(Wrappers.<DiPreSaleCustomized>lambdaQuery().eq(DiPreSaleCustomized::getPreSaleId, diPreSale1.getId()).orderByDesc(DiPreSaleCustomized::getId).last("limit 1"));
        Assert.notNull(diPreSaleCustomized, "非标方案不能为空");
        Assert.hasLength(techSupportOwnerCode, "技术支持负责人不能为空");
        //判断技术支持负责任人是否修改
        boolean isUpdateTechSupportOwner = false;
        String oldTechSupportOwnerCode = "";
        if (StringUtils.isNotBlank(diPreSaleCustomized.getTechSupportOwnerCode()) && !techSupportOwnerCode.equals(diPreSaleCustomized.getTechSupportOwnerCode())) {
            isUpdateTechSupportOwner = true;
            oldTechSupportOwnerCode = diPreSaleCustomized.getTechSupportOwnerCode();
        }
        diPreSaleCustomized.setTechSupportOwnerCode(techSupportOwnerCode);
        diPreSaleCustomizedMapper.updateById(diPreSaleCustomized);
        preSaleDetailResponse.setDiPreSaleCustomized(diPreSaleCustomized);
        Long customizedId = preSaleDetailResponse.getDiPreSaleCustomized().getId();
        preSaleDetailResponse.setDiPreSaleCustomizedPlc(diPreSaleCustomizedPlcMapper.selectOne(Wrappers.<DiPreSaleCustomizedPlc>lambdaQuery().eq(DiPreSaleCustomizedPlc::getCustomizedId, customizedId)));
        preSaleDetailResponse.setDiPreSaleCustomizedPiping(diPreSaleCustomizedPipingMapper.selectOne(Wrappers.<DiPreSaleCustomizedPiping>lambdaQuery().eq(DiPreSaleCustomizedPiping::getCustomizedId, customizedId)));
        preSaleDetailResponse.setDiPreSaleCustomizedHeader(diPreSaleCustomizedHeaderMapper.selectOne(Wrappers.<DiPreSaleCustomizedHeader>lambdaQuery().eq(DiPreSaleCustomizedHeader::getCustomizedId, customizedId)));
        preSaleDetailResponse.setDiPreSaleCustomizedFan(diPreSaleCustomizedFanMapper.selectOne(Wrappers.<DiPreSaleCustomizedFan>lambdaQuery().eq(DiPreSaleCustomizedFan::getCustomizedId, customizedId)));
        preSaleDetailResponse.setDiPreSaleCustomizedBurner(diPreSaleCustomizedBurnerMapper.selectOne(Wrappers.<DiPreSaleCustomizedBurner>lambdaQuery().eq(DiPreSaleCustomizedBurner::getCustomizedId, customizedId)));
        preSaleDetailResponse.setDiPreSaleCustomizedAir(diPreSaleCustomizedAirMapper.selectOne(Wrappers.<DiPreSaleCustomizedAir>lambdaQuery().eq(DiPreSaleCustomizedAir::getCustomizedId, customizedId)));
        Boolean bl = false;


        if (PreSaleStatusEnum.DEMAND_AND_VERIFICATION.getCode().equals(diPreSale1.getPreSaleStatus())) {
            DiPreSale diPreSale = new DiPreSale();
            diPreSale.setId(id);
            diPreSale.setPreSaleStatus(TECHNICAL_SUPPORT.getCode());
            diPreSaleMapper.updateById(diPreSale);
        }
        AfterCommitExecutor.submit(() ->
                        EventBusUtils.publishEvent(TechnicalSupportUserChangeEvent.builder().nicheCode(diPreSale1.getNicheCode()).build()),
                null);
        return "";
    }

    @Override
    public String initiateQuotation(Long id) {
        return "";
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String lockingscheme(Long id, Long orderId) {
        DiPreSale diPreSale = diPreSaleMapper.selectById(id);

        //修复订单Id 可能为空的问题,
        if (orderId == null && diPreSale.getOrderPreSaleStatus() != null) {
            DiOrder order = orderService.queryByNicheNo(diPreSale.getNicheCode());
            if (order != null) {
                orderId = order.getId();
            }
        }

        DiPreSale curPreSale = diPreSale; //diPreSaleMapper.selectOne(Wrappers.<DiPreSale>lambdaQuery().eq(DiPreSale::getPreSaleCode, diPreSale.getPreSaleCode()).orderByDesc(DiPreSale::getVersion).last("LIMIT 1 FOR UPDATE"));
        DiPreSaleManifest diPreSaleManifest = diPreSaleManifestMapper.selectOne(Wrappers.<DiPreSaleManifest>lambdaQuery().eq(DiPreSaleManifest::getDiPreSaleId, curPreSale.getId()));
        if (Objects.isNull(diPreSaleManifest)) {
            throw new ServiceException("产品方案清单为空，无法锁定方案");
        }
        DiMaterielPrice diMaterielPrice = diPriceSalePriceService.lambdaQuery().eq(DiMaterielPrice::getMaterielId, diPreSaleManifest.getMaterialVersion()).one();
        validManifest(curPreSale, diPreSaleManifest, diMaterielPrice);


        DiMateriel materiel = diMaterielService.getById(diPreSaleManifest.getMaterialVersion());
        MaterielBomVersionVo materielBomVersionVo = new MaterielBomVersionVo();
        //DiMaterielVersion diMaterielVersion = iDiMaterielVersionService.lambdaQuery().eq(DiMaterielVersion::getId, diPreSaleManifest.getMaterialVersion()).one();
        Optional.ofNullable(materiel).ifPresent(version -> {
            if (version.getVersionStatus().equals(0)) {
                materielBomVersionVo.setBomStatus(1);
            }
        });
        materielBomVersionVo.setMaterielVersionId(diPreSaleManifest.getMaterialVersion());

        MaterielBomVersionVo bomVersionVo = new MaterielBomVersionVo();
        bomVersionVo.setMaterielVersionId(diPreSaleManifest.getMaterialVersion());
        diMaterielBomService.bomByVersion(bomVersionVo);

        // 1 售前，2、售中
        int phase = 1;

        //非标
        PreSaleDetailResponse preSaleDetailResponse = this.selectDiPreSaleById(curPreSale.getId(), "", 5, true);
        PreSaleDeliveryReviewLockedEvent preSaleDeliveryReviewLockedEvent = null;

        // 大非标 和非标一致需要交期复核，但是 大非标不做物料有效期校验
        if ((diPreSale.getPreSaleType() == 2 || PreSaleTypeEnum.of(diPreSale.getPreSaleType()) == PreSaleTypeEnum.BIG_NON_STANDARD) && null != diPreSale.getOrderPreSaleStatus()) {
            phase = 2;
            //订单锁定方案
            if (PreSaleTypeEnum.of(diPreSale.getPreSaleType()) == PreSaleTypeEnum.BIG_NON_STANDARD) {
                // 跳过校验
            } else if (Objects.nonNull(preSaleDetailResponse) && CollectionUtils.isNotEmpty(preSaleDetailResponse.getSectionList())
                    || CollectionUtils.isNotEmpty(preSaleDetailResponse.getMaterielChecklistNewDTOS())) {
                throw new RuntimeException("请至询价tab内，清空所有选型物料清单并核准方案中已到期物料清单的价格和有效期再试。");
            }
            Optional.ofNullable(materiel).ifPresent(version -> {
                if (version.getVersionStatus().equals(0)) {
                    diMaterielBomService.transferFormal(materielBomVersionVo);
                }
            });
//            curPreSale.setOrderPreSaleStatus(OrderPreSaleStatusEnum.TECHNICAL_REVIEW.getCode());

            curPreSale.setPreSaleTechnicalCheckTime(LocalDateTime.now());
            curPreSale.setUpdateBy(SecurityUtils.getUsername());
            curPreSale.setUpdateTime(LocalDateTime.now());
            diPreSaleMapper.updateById(curPreSale);
            OrderPreSaleStatusEnum newStatus = this.calcOrderPreSaleStatus(curPreSale, orderId);
            this.updatePreSaleOrderStatus(curPreSale.getId(), newStatus.getCode());
            PreSaleTechnicalReviewDoneEvent event = new PreSaleTechnicalReviewDoneEvent();
            event.setPreSaleId(curPreSale.getId());
            event.setOrderId(orderId);
            EventBusUtils.publishEvent(event);

        } else {
//            if (Objects.nonNull(preSaleDetailResponse) && CollectionUtils.isNotEmpty(preSaleDetailResponse.getSectionList())) {
//                curPreSale.setTechnicalReviewFlag(true);
//            }
// 0:无交期复核，1 交期复核 继续复核，2退出交期复核
            int continueFlag = 0;
            if (PreSaleStatusEnum.DELIVERY_REVIEW.getCode().equals(curPreSale.getPreSaleStatus())) {
                //交期复核时的锁定方案
                //1. 判断 是否需要 采购交期复核和 交付复核
                //1.2 需要复核，标记 状态，发送 交付复核待办，采购复核待办
                //1.1 不需要复核，标记 状态，检查报价单 是否能继续

                //删除交期复核代办
                AgencyTaskInfoDTO agencyTaskInfoDto = new AgencyTaskInfoDTO();
                agencyTaskInfoDto.setTaskTypeEnum(AgencyTaskTypeEnum.DELIVERY_TIME_CHECK);
                agencyTaskInfoDto.setBusinessKey(diPreSale.getId().toString());
                agencyTaskInfoDto.setJumpKey(diPreSale.getId().toString());
                agencyTaskInfoDto.setType("2");
                String topic = AgencyConstants.AGENCY_TASK_TOPIC + AgencyConstants.DELIMITER_COLON + AgencyConstants.AGENCY_TASK_TAG;
                rocketMQTemplate.syncSend(topic, agencyTaskInfoDto);

                preSaleDeliveryReviewLockedEvent = new PreSaleDeliveryReviewLockedEvent();
                preSaleDeliveryReviewLockedEvent.setPreSaleId(diPreSale.getId());
                preSaleDeliveryReviewLockedEvent.setOrderId(orderId);
                preSaleDeliveryReviewLockedEvent.setDeliveryReviewFlag(diPreSale.getDeliveryReviewFlag());
            }

            curPreSale.setPreSaleLockTime(LocalDateTime.now());
            PreSaleDetailResponse.Fee fee = getFee(diPreSaleManifest);
            curPreSale.setCostFeeTotal(fee.getFeeTotal());
            curPreSale.setGuideDuration(fee.computeGuideDurationForProject());
            if (PreSaleStatusEnum.DELIVERY_REVIEW.getCode().equals(curPreSale.getPreSaleStatus())) {
                curPreSale.setDeliveryReviewTechSupportFlag(PreSaleDeliveryTaskFlagEnum.DONE.getCode());
            } else {
                curPreSale.setPreSaleStatus(PreSaleStatusEnum.LOCKED.getCode());
            }
            curPreSale.setUpdateBy(SecurityUtils.getUsername());
            curPreSale.setUpdateTime(LocalDateTime.now());
            diPreSaleMapper.updateById(curPreSale);
            //updateManifestDuration(fee, diPreSaleManifest.getId());
            if (continueFlag != 0) {
                this.notifyPreSaleQuote(diPreSale.getId());
            }

        }
        DiPreSaleCustomized diPreSaleCustomized = diPreSaleCustomizedMapper.selectOne(Wrappers.<DiPreSaleCustomized>lambdaQuery().eq(DiPreSaleCustomized::getPreSaleId, curPreSale.getId()).orderByDesc(DiPreSaleCustomized::getId).last("limit 1"));
        if (Objects.nonNull(diPreSaleCustomized)) {
            saveAgencyTaskSendMessage(Lists.newArrayList(diPreSaleCustomized.getTechSupportOwnerCode()), curPreSale, 2);
        }
        sendLockPreSaleDingTalkMessage(curPreSale);
        diPreSaleFeeService.updatePreSaleFee(curPreSale);
        preSaleHistoryService.saveDiPreSaleHistory(diPreSale, phase);

        if (preSaleDeliveryReviewLockedEvent != null) {
            EventBusUtils.publishEvent(preSaleDeliveryReviewLockedEvent);
        }
        return curPreSale.getId().toString();
    }

//    private void updateManifestDuration(PreSaleDetailResponse.Fee fee, Integer manifestId) {
//        var updateWrapper = Wrappers.lambdaUpdate(DiPreSaleManifest.class)
//                .set(DiPreSaleManifest::getElectricalDesignPeriod, fee.getElectricDay())
//                .set(DiPreSaleManifest::getMechanicalDesignDuration, fee.getMechineDay())
//                .set(DiPreSaleManifest::getProduceDay, fee.getProductDay())
//                .set(DiPreSaleManifest::getSupplyDay, fee.getSupplyDay())
//                .set(DiPreSaleManifest::getTechnicalSupportDuration, fee.getTechSupportDay())
//                .eq(DiPreSaleManifest::getId, manifestId);
//        iDiPreSaleManifestService.update(updateWrapper);
//    }

    @Override
    public void startPurchaseReview(DiPreSale diPreSale, String source) {

        this.lambdaUpdate()
                .set(DiPreSale::getDeliveryReviewSupplyFlag, PreSaleDeliveryTaskFlagEnum.PENDING.getCode())
                .eq(DiPreSale::getId, diPreSale.getId())
                .update();
        //获取采购复核人
        List<String> purchaseManagerUserIds = new ArrayList<>();
        if (PreSaleTypeEnum.BIG_NON_STANDARD.getCode().equals(diPreSale.getPreSaleType())) {
            // 大非标 采购负责人为 技术支持
            DiPreSaleCustomized customized = this.queryCustomized(diPreSale.getId());
            purchaseManagerUserIds = Collections.singletonList(customized.getTechSupportOwnerCode());
        } else {
            List<SysUser> userList = remoteRoleService.internalGetUserByRoleKey(purchaseManagerApproveRoleKey);
            purchaseManagerUserIds = userList.stream().map(SysUser::getUserName).toList();
        }
        if (CollectionUtil.isNotEmpty(purchaseManagerUserIds)) {
            //新增采购复核待办任务和钉钉通知
            orderCommonService.saveAgencyTask(diPreSale.getId().toString(), null, diPreSale.getPreSaleCode(), this.getProjectNo(diPreSale.getNicheCode()), AgencyTaskTypeEnum.DELIVERY_TIME_CHECK_PURCHASE,
                    StringUtils.isNotEmpty(diPreSale.getPreSaleName()) ? diPreSale.getPreSaleName() : diPreSale.getPreSaleCode(), DELIVERY_TIME_REVIEW.getDesc(),
                    purchaseManagerUserIds, null);
            String purchaseTitle = StrUtil.format(DingTalkEnum.DELIVERY_TIME_CHECK_PURCHASE_NOTICE.getTitle(), diPreSale.getPreSaleCode());
            String purchaseContent = StrUtil.format(DingTalkEnum.DELIVERY_TIME_CHECK_PURCHASE_NOTICE.getMessage(), diPreSale.getNicheCode(), diPreSale.getPreSaleCode(), SecurityUtils.getLoginUser().getSysUser().getNickName());
            orderCommonService.sendDingTalkMessage("产品方案", purchaseTitle, purchaseContent, purchaseManagerUserIds, null);
        } else {
            log.info("DiPreSaleServiceImpl---{}()---采购主管审批角色未绑定用户", source);
        }

    }

    /**
     * 重新判断是否需要交期复核
     *
     * @param curPreSale
     * @return
     */
    @Override
    public boolean needDeliveryReview(DiPreSale curPreSale, BigDecimal expectedDeliveryWeek) {

        // DiMarketingNiche niche = iDiMarketingNicheService.getById(curPreSale.getNicheId());
        //BigDecimal expectedDeliveryWeek = BigDecimal.valueOf(niche.getExpectationDeliveryWeek() != null ? niche.getExpectationDeliveryWeek() : 1);
        BigDecimal actualDeliveryWeek = this.queryAfterCheckDeliveryTimeUnitWeek(curPreSale);
        if (actualDeliveryWeek.compareTo(expectedDeliveryWeek) > 0) {
            return true;
        }
        return false;
    }

    private void notifyPreSaleQuote(Long preSaleId) {
        //todo 需要通过消息处理
        quoteService.tryDeliveryTimeReviewDoneByPreSaleId(preSaleId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void complated(Long orderId, Long preSaleId) {
        DiPreSale diPreSale = this.lambdaQuery().eq(DiPreSale::getId, preSaleId).one();
        Assert.notNull(diPreSale, "产品方案不能为空");
        if (!OrderPreSaleStatusEnum.TO_BE_COMPLETED.getCode().equals(diPreSale.getOrderPreSaleStatus())) {
            Assert.isTrue(false, "产品方案当前不是待完成状态，无法变更为已完成");
        }
        PreSaleDetailResponse preSaleDetailResponse = this.selectDiPreSaleById(preSaleId, "", 5, true);
        if (Objects.nonNull(preSaleDetailResponse) && CollectionUtils.isNotEmpty(preSaleDetailResponse.getSectionList())
                && diPreSale.getPreSaleType().equals(PreSaleTypeEnum.NON_STANDARD.getCode())) {
            throw new RuntimeException("请先删除询价tab内，选型物料清单的所有数据后再试");
        }
        updatePreSaleOrderStatus(preSaleId, OrderPreSaleStatusEnum.FIVE.getCode());
        DiOrder diOrder = diOrderService.lambdaQuery().eq(DiOrder::getId, orderId).one();
        //PreSaleQuoteProductCostInfoVO productCostInfoVO = preSaleQuoteService.getQuoteProductCostInfoByNo(diOrder.getPreSaleQuoteNo(), 1);
        //List<OrderProductPlanDetailDTO> orderProductDetailList = OrderConvert.INSTANCE.productInfoListToPlanDetailList(productCostInfoVO.getDetailedList());
//  orderProductDetailList.stream().map(OrderProductPlanDetailDTO::getPreSaleId).toList();
        List<Long> preSaleIdList = preSaleQuoteService.queryPreSaleIdListByQuoteNo(diOrder.getPreSaleQuoteNo());
        List<DiPreSale> orderPreSaleLists = this.lambdaQuery().in(DiPreSale::getId, preSaleIdList).list();
        Long scheduleCompatedCnt = orderPreSaleLists.stream().filter(preSale -> preSale.getOrderPreSaleStatus().equals(OrderPreSaleStatusEnum.FIVE.getCode())).count();
        if (preSaleIdList.size() == scheduleCompatedCnt) {
            diOrderService.lambdaUpdate()
                    .eq(DiOrder::getId, orderId)
                    .set(DiOrder::getOrderStatus, OrderStatusEnum.FINISHED.getCode())
                    .set(DiOrder::getOrderStage, OrderStageEnum.EIGHT.getCode())
                    .update();
            //发送钉钉消息
            orderService.sendDingDingMessage(diOrder, OrderDingTalkEnum.ORDER_CHANGE_COMPLETE);
            OrderCompletedEvent event = new OrderCompletedEvent();
            event.setOrderId(orderId);
            EventBusUtils.publishEvent(event);
        }
        preSaleHistoryService.saveDiPreSaleHistory(diPreSale, PreSalePhaseEnum.ON_SALE.getCode());
    }

    @Override
    public Boolean waiver(Long preSaleId, String reasons) {
        DiPreSale diPreSale = this.lambdaQuery().eq(DiPreSale::getId, preSaleId).one();
        Assert.notNull(diPreSale, "产品方案不能为空");
        try {
            List<DiPreSale> diPreSales = this.lambdaQuery().eq(DiPreSale::getPreSaleCode, diPreSale.getPreSaleCode()).list();
            diPreSales.stream().forEach(preSale -> {
                preSale.setPreSaleWaiverReasons(reasons);
                preSale.setPreSaleStatus(PreSaleStatusEnum.WAIVER.getCode());
                this.updateById(preSale);
                contractService.discardProcess(preSaleId);
//                diProcessProjectService.updateCardFlagProcessProject(diMarketingNiche.getNicheNo());
                DiPreSaleCustomized diPreSaleCustomized = diPreSaleCustomizedMapper.selectOne(Wrappers.<DiPreSaleCustomized>lambdaQuery()
                        .eq(DiPreSaleCustomized::getPreSaleId, preSale.getId())
                        .orderByDesc(DiPreSaleCustomized::getId).last("limit 1"));
                if (diPreSaleCustomized != null) {
                    String oldTechSupportOwnerCode = "";
                    if (StringUtils.isNotBlank(diPreSaleCustomized.getTechSupportOwnerCode())) {
                        oldTechSupportOwnerCode = diPreSaleCustomized.getTechSupportOwnerCode();
                        saveAgencyTaskSendMessage(Lists.newArrayList(oldTechSupportOwnerCode), preSale, 2);
                    }
                }
            });
            List<DiPreSale> diPreSaleList = this.queryByNicheCode(diPreSale.getNicheCode());
            int preSaleCount = diPreSaleList.stream().filter(preSale -> !PreSaleStatusEnum.WAIVER.getCode().equals(preSale.getPreSaleStatus()))
                    .collect(Collectors.toList()).size();

            PreSaleGivenUpEvent preSaleGivenUpEvent = new PreSaleGivenUpEvent();
            preSaleGivenUpEvent.setPreSaleId(preSaleId);
            preSaleGivenUpEvent.setRemaining(preSaleCount);
            preSaleGivenUpEvent.setNicheCode(diPreSale.getNicheCode());
            EventBusUtils.publishEvent(preSaleGivenUpEvent);

            EventBusUtils.publishEvent(TechnicalSupportUserChangeEvent.builder().nicheCode(diPreSale.getNicheCode()).build());
        } catch (Exception ex) {
            log.error("放弃产品方案异常", ex);
            return false;
        }
        //删除商机管理_报价阶段代办
        AgencyTaskInfoDTO agencyTaskInfoDto = new AgencyTaskInfoDTO();
        agencyTaskInfoDto.setType("2");
        agencyTaskInfoDto.setTaskTypeEnum(AgencyTaskTypeEnum.NICHE_MANAGE_QUOTE);
        agencyTaskInfoDto.setBusinessKey(diPreSale.getNicheCode());
        String topic = AgencyConstants.AGENCY_TASK_TOPIC + AgencyConstants.DELIMITER_COLON + AgencyConstants.AGENCY_TASK_TAG;
        rocketMQTemplate.syncSend(topic, agencyTaskInfoDto);
        return true;
    }

    public void updatePreSaleStatusByOrderId(Long preSaleId, Long orderId) {
        OrderPreSaleStatusEnum statusEnum = this.calcOrderPreSaleStatus(preSaleId, orderId);
        log.info("订单号：{}，产品方案：{} 修改状态为:{}", orderId, preSaleId, statusEnum.getDesc());
        updatePreSaleOrderStatus(preSaleId, statusEnum.getCode());
        return;
//        DiPreSale diPreSale = diPreSaleMapper.selectById(preSaleId);
//        Integer preSaleOrderStatus = diPreSale.getOrderPreSaleStatus();
//        if (Objects.isNull(diPreSale)) {
//            log.info("当前产品方案不存在，无法更新履约状态，产品方案编号:{}", preSaleId);
//            return;
//        }
//        //boolean hasMaterialSelection = diPreSaleMaterielSelectionService.hasSelection(preSaleId);
//        //锁定了，但是技术支持复核任务未完成
//        if (OrderPreSaleStatusEnum.TECHNICAL_REVIEW.getCode().equals(diPreSale.getOrderPreSaleStatus()) && !checkOrderSupportComplated(preSaleId)) {
//            return;
//        }
//        //待技术支持复核（还未点击锁定、还未完成任务
//        if (OrderPreSaleStatusEnum.TWO.getCode().equals(diPreSale.getOrderPreSaleStatus())) {
//            return;
//        }
//        //非标方案、状态待开始（初始化）则变更为技术支持复核（中）
//        if ((PreSaleTypeEnum.nonStandard(diPreSale.getPreSaleType())) && OrderPreSaleStatusEnum.INIT.getCode().equals(diPreSale.getOrderPreSaleStatus())) {
//            //boolean hasMaterialSelection = diPreSaleMaterielSelectionService.hasSelection(preSaleId);
//            //if (hasMaterialSelection) {
//            diPreSale.setTechnicalReviewFlag(true);
//            diPreSaleMapper.updateById(diPreSale);
//            updatePreSaleOrderStatus(preSaleId, OrderPreSaleStatusEnum.TWO.getCode());
//            //注意，这里要发送技术支持复核待办和钉钉
//            tscSendMessageAndSaveAgency(diPreSale);
//            //}
//            return;
//        }
//        DiOrder order = diOrderService.lambdaQuery().eq(DiOrder::getId, orderId).one();
//        Assert.notNull(order, "订单不存在");
//        Boolean isElectricDesignCompleted = true;
//        Boolean isMachineDesignCompleted = true;
//        Boolean isElectricDesignCompleting = false;
//        Boolean isMachineDesignCompleting = false;
//        Boolean isProduceCompleted = true;
//        Boolean isQualityCompleted = true;
//        Boolean isPurchaseCompleted = true;
//        Boolean techSupportCompleted = PreSaleTypeEnum.nonStandard(diPreSale.getPreSaleType()) ? (!OrderPreSaleStatusEnum.INIT.getCode().equals(preSaleOrderStatus)
//                && !OrderPreSaleStatusEnum.TWO.getCode().equals(preSaleOrderStatus)
//                || OrderPreSaleStatusEnum.TECHNICAL_REVIEW.getCode().equals(preSaleOrderStatus)) : true;
//        //判断电气设计
//        if (StringUtils.isNotEmpty(order.getElectricDesignUserId())) {
//            if (PreSaleTypeEnum.STANDARD.getCode().equals(diPreSale.getPreSaleType()) || PreSaleTypeEnum.TRADE.getCode().equals(diPreSale.getPreSaleType())) {
//                isElectricDesignCompleted = true;
//            } else {
//                DiOrderElectricDesign diOrderElectricity = diOrderElectricityService.lambdaQuery().eq(DiOrderElectricDesign::getPreSaleId, preSaleId).one();
//                if (diOrderElectricity == null) {
//                    isElectricDesignCompleted = false;
//                } else {
//                    isElectricDesignCompleted = diOrderElectricity.getScheduleStatus() == 2;
//                    if (!isElectricDesignCompleted && diOrderElectricity.getScheduleStatus() == 1) {
//                        isElectricDesignCompleting = true;
//                    }
//                }
//            }
//        }
//        //判断机械设计
//        if (StringUtils.isNotEmpty(order.getMachineDesignUserId())) {
//            if (PreSaleTypeEnum.STANDARD.getCode().equals(diPreSale.getPreSaleType()) || PreSaleTypeEnum.TRADE.getCode().equals(diPreSale.getPreSaleType())) {
//                isMachineDesignCompleted = true;
//            } else {
//                DiOrderMachineDesign diOrderMachineDesign = diOrderMachineDesignService.lambdaQuery().eq(DiOrderMachineDesign::getPreSaleId, preSaleId).one();
//                if (diOrderMachineDesign == null) {
//                    isMachineDesignCompleted = false;
//                } else {
//                    isMachineDesignCompleted = diOrderMachineDesign.getScheduleStatus() == 2;
//                    if (!isMachineDesignCompleted && diOrderMachineDesign.getScheduleStatus() == 1) {
//                        isMachineDesignCompleting = true;
//                    }
//                }
//            }
//        }
//        //判断生产及质量
//        if (StringUtils.isNotEmpty(order.getProduceUserId())) {
//            DiOrderProduce diOrderProduce = diOrderProduceService.lambdaQuery().eq(DiOrderProduce::getPreSaleId, preSaleId).one();
//            if (diOrderProduce == null) {
//                isProduceCompleted = false;
//            } else {
//                isProduceCompleted = diOrderProduce.getScheduleStatus() == 2;
//            }
//        }
//        //判断质检负责人是否勾选
//        if (StringUtils.isNotEmpty(order.getProduceQualityUserId())) {
//            DiOrderQuality diOrderQuality = diOrderQualityService.lambdaQuery().eq(DiOrderQuality::getPreSaleId, preSaleId).one();
//            if (diOrderQuality == null) {
//                isQualityCompleted = false;
//            } else {
//                isQualityCompleted = diOrderQuality.getScheduleStatus() == 2;
//            }
//        }
//        if (StringUtils.isNotEmpty(order.getPurchaseUserId())) {
//            DiOrderMaterielPlan diOrderMaterielPlan = diOrderMaterielPlanService.lambdaQuery().eq(DiOrderMaterielPlan::getPreSaleId, preSaleId).one();
//            if (diOrderMaterielPlan == null) {
//                isPurchaseCompleted = false;
//            } else {
//                isPurchaseCompleted = diOrderMaterielPlan.getScheduleStatus() == 2;
//            }
//        }
//        //标品方案判断生产、质量、采购任务是否完成，完成则变更状态为待完成，未完成则变更为备货生产中
//        if (PreSaleTypeEnum.STANDARD.getCode().equals(diPreSale.getPreSaleType()) || PreSaleTypeEnum.TRADE.getCode().equals(diPreSale.getPreSaleType())) {
//            if (isProduceCompleted && isQualityCompleted && isPurchaseCompleted) {
//                updatePreSaleOrderStatus(preSaleId, OrderPreSaleStatusEnum.TO_BE_COMPLETED.getCode());
//            } else {
//                updatePreSaleOrderStatus(preSaleId, OrderPreSaleStatusEnum.FOUR.getCode());
//            }
//            return;
//        }
//        //如果技术支持复核、电气设计、机械设计、生产计划、质检任务、采购任务都已经完成，则更新方案状态为待完成
//        if (techSupportCompleted && isElectricDesignCompleted && isMachineDesignCompleted && isProduceCompleted && isQualityCompleted && isPurchaseCompleted) {
//            updatePreSaleOrderStatus(preSaleId, OrderPreSaleStatusEnum.TO_BE_COMPLETED.getCode());
//            return;
//        }
//        //如果技术支持复核已完成 并且 机械设计、电气设计未完成，则变更状态为待设计、如果机械设计跟电气设计有一个处于设计中，那么就是设计中，否则也就是待设计
//        if (techSupportCompleted && !isElectricDesignCompleted || !isMachineDesignCompleted) {
//            if (isElectricDesignCompleting || isMachineDesignCompleting) {
//                updatePreSaleOrderStatus(preSaleId, OrderPreSaleStatusEnum.SIX.getCode());
//            } else {
//                updatePreSaleOrderStatus(preSaleId, OrderPreSaleStatusEnum.THREE.getCode());
//            }
//            return;
//        }
//        if (techSupportCompleted && !isProduceCompleted || !isQualityCompleted || !isPurchaseCompleted) {
//            updatePreSaleOrderStatus(preSaleId, OrderPreSaleStatusEnum.FOUR.getCode());
//            return;
//        }
////        if (OrderPreSaleStatusEnum.THREE.getCode().equals(preSaleOrderStatus)) {
////            updatePreSaleOrderStatus(preSaleId, OrderPreSaleStatusEnum.SIX.getCode());
////        }
    }

    private OrderPreSaleStatusEnum calcOrderPreSaleStatus(Long preSaleId, Long orderId) {
        DiPreSale diPreSale = this.queryDiPreSaleById(preSaleId);
        return calcOrderPreSaleStatus(diPreSale, orderId);
    }

    @Override
    public OrderPreSaleStatusEnum calcOrderPreSaleStatus(DiPreSale diPreSale, Long orderId) {
        DiOrder order = diOrderService.lambdaQuery().eq(DiOrder::getId, orderId).one();
        return this.calcOrderPreSaleStatus(diPreSale, order);
    }

    @Override
    public OrderPreSaleStatusEnum calcOrderPreSaleStatus(DiPreSale diPreSale, DiOrder order) {

        Long preSaleId = diPreSale.getId();


        //处于待完成、已完成状态，则不变更状态
//        if (Objects.equals(diPreSale.getOrderPreSaleStatus(), OrderPreSaleStatusEnum.TO_BE_COMPLETED.getCode())) {
//            log.info("方案 {} 处于待完成状态,不调整状态", preSaleId);
//            return OrderPreSaleStatusEnum.TO_BE_COMPLETED;
//        }
        if (Objects.equals(diPreSale.getOrderPreSaleStatus(), OrderPreSaleStatusEnum.FIVE.getCode())) {
            log.info("方案 {} 处于 完成状态,不调整状态", preSaleId);
            return OrderPreSaleStatusEnum.FIVE;
        }

        //DiProcessProject project = projectService.selectByNo(order.getProjectNo());
        boolean canExecute = diOrderService.canRun(order);

        PreSaleTypeEnum preSaleTypeEnum = PreSaleTypeEnum.of(diPreSale.getPreSaleType());
        // 优先看是否有项目经理和 预付款或者提前执行

        if (!canExecute) {
            if (PreSaleTypeEnum.STANDARD.getCode().equals(diPreSale.getPreSaleType()) || PreSaleTypeEnum.TRADE.getCode().equals(diPreSale.getPreSaleType())) {
                log.info(" 订单流转条件不满足 标品或者贸易类 方案 状态{} 处于 {}", preSaleId, OrderPreSaleStatusEnum.INIT.getDesc());
                return OrderPreSaleStatusEnum.INIT;
            } else if (diPreSale.getPreSaleTechnicalCheckTime() != null) {
                log.info(" 订单流转条件不满足 非标方案 且已技术支持复核 状态{} 处于 {}", preSaleId, OrderPreSaleStatusEnum.INIT.getDesc());
                return OrderPreSaleStatusEnum.INIT;
            } else {
                log.info(" 订单流转条件不满足 非标方案 且未技术支持复核 状态{} 处于 {}", preSaleId, OrderPreSaleStatusEnum.TWO.getDesc());
                return OrderPreSaleStatusEnum.TWO;
            }
        } else if (PreSaleTypeEnum.nonStandard(diPreSale.getPreSaleType()) && diPreSale.getPreSaleTechnicalCheckTime() == null) {
            //非标方案，未技术支持复核，还是技术支持阶段
            return OrderPreSaleStatusEnum.TWO;
        }

        int taskCount = 0;
        int taskDoneCount = 0;
        //记录 可能的状态，从 待完成 开始 依次向前。 如：待完成、质量、生产、采购、设计、待开始。
        List<OrderPreSaleStatusEnum> maybeTaskList = new ArrayList<>();
        maybeTaskList.add(OrderPreSaleStatusEnum.TO_BE_COMPLETED);

        //判断质检负责人是否勾选
        if (StringUtils.isNotEmpty(order.getProduceQualityUserId()) && preSaleTypeEnum != PreSaleTypeEnum.TRADE) {
            maybeTaskList.add(OrderPreSaleStatusEnum.QUALITY_TASKS);
            DiOrderQuality diOrderQuality = diOrderQualityService.lambdaQuery().eq(DiOrderQuality::getPreSaleId, preSaleId).one();
            if (diOrderQuality != null && diOrderQuality.getScheduleStatus() == 2) {
                return nextStatus(maybeTaskList, maybeTaskList.get(maybeTaskList.size() - 1));
            }
            taskCount++;
        }

        //判断生产
        if (StringUtils.isNotEmpty(order.getProduceUserId()) && preSaleTypeEnum != PreSaleTypeEnum.TRADE) {
            maybeTaskList.add(OrderPreSaleStatusEnum.PRODUCTION_TASKS);
            DiOrderProduce diOrderProduce = diOrderProduceService.lambdaQuery().eq(DiOrderProduce::getPreSaleId, preSaleId).one();
            if (diOrderProduce != null && diOrderProduce.getScheduleStatus() == 2) {
                return nextStatus(maybeTaskList, maybeTaskList.get(maybeTaskList.size() - 1));
            }
            taskCount++;
        }
        //采购任务
        if (StringUtils.isNotEmpty(order.getPurchaseUserId())) {
            maybeTaskList.add(OrderPreSaleStatusEnum.FOUR);
            DiOrderMaterielPlan diOrderMaterielPlan = diOrderMaterielPlanService.lambdaQuery().eq(DiOrderMaterielPlan::getPreSaleId, preSaleId).one();
            if (diOrderMaterielPlan != null && diOrderMaterielPlan.getScheduleStatus() == 2) {
                return nextStatus(maybeTaskList, maybeTaskList.get(maybeTaskList.size() - 1));
            }

            List<DiPreSaleManifest> diPreSaleManifest = this.queryPreSaleManifestList(preSaleId);
            if (diPreSaleManifest.stream().anyMatch(x -> StringUtils.isNotBlank(x.getMaterialsRequisition()))) {
                return nextStatus(maybeTaskList, maybeTaskList.get(maybeTaskList.size() - 1));
            }
            taskCount++;
        }

        //判断机械设计 的 bom 完成
        if (StringUtils.isNotEmpty(order.getMachineDesignUserId()) && preSaleTypeEnum != PreSaleTypeEnum.TRADE && preSaleTypeEnum != PreSaleTypeEnum.STANDARD) {
            maybeTaskList.add(OrderPreSaleStatusEnum.SIX);
            DiOrderMachineDesign diOrderMachineDesign = diOrderMachineDesignService.lambdaQuery().eq(DiOrderMachineDesign::getPreSaleId, preSaleId).one();
            if (diOrderMachineDesign != null && diOrderMachineDesign.getBomRealEndTime() != null) {
                return nextStatus(maybeTaskList, maybeTaskList.get(maybeTaskList.size() - 1));
            }
            taskCount++;
        }
        //不是 待完成，已完成，且不是 订单tab 的状态，剩下就是 待完成
        if (taskCount == 0) {
            // 没有任务
            return OrderPreSaleStatusEnum.TO_BE_COMPLETED;
        } else {
            // 一个任务都 没有完成
            return maybeTaskList.get(maybeTaskList.size() - 1);
        }
    }

    private OrderPreSaleStatusEnum nextStatus(List<OrderPreSaleStatusEnum> maybeTaskList, OrderPreSaleStatusEnum doneTask) {
        for (int i = maybeTaskList.size() - 1; i >= 0; i--) {
            if (maybeTaskList.get(i).getSort() > doneTask.getSort()) {
                return maybeTaskList.get(i);
            }
        }
        return null;
    }

    @Override
    /**
     * 检查技术支持复核任务是否完成
     *
     * @param preSaleId
     * @return
     */
    public Boolean checkOrderSupportComplated(Long preSaleId) {
        DiPreSale diPreSale = this.lambdaQuery().eq(DiPreSale::getId, preSaleId).one();
        if (PreSaleTypeEnum.STANDARD.getCode().equals(diPreSale.getPreSaleType()) || PreSaleTypeEnum.TRADE.getCode().equals(diPreSale.getPreSaleType())) {
            return true;
        }
        if (!diPreSale.isTechnicalReviewFlag()) {
            return true;
        }
        DiOrderSupportCheck diOrderSupportCheck = supportCheckService.lambdaQuery().eq(DiOrderSupportCheck::getPreSaleId, preSaleId).one();
        if (diOrderSupportCheck == null) {
            return false;
        }
        return diOrderSupportCheck.getScheduleStatus() == 2;
    }

    private void tscSendMessageAndSaveAgency(DiPreSale diPreSale) {
        DiPreSaleCustomized diPreSaleCustomized = diPreSaleCustomizedMapper.selectOne(Wrappers.<DiPreSaleCustomized>lambdaQuery().eq(DiPreSaleCustomized::getPreSaleId, diPreSale.getId()).orderByDesc(DiPreSaleCustomized::getId).last("limit 1"));
        if (null == diPreSaleCustomized) {
            log.info("DiPreSaleServiceImpl---tscSendMessageAndSaveAgency()---未获取到非标方案表数据，产品方案ID：{}", diPreSale.getId());
            return;
        }
        if (StringUtils.isBlank(diPreSaleCustomized.getTechSupportOwnerCode())) {
            log.info("DiPreSaleServiceImpl---tscSendMessageAndSaveAgency()---没有技术支持负责人，非标方案表数据：{}", JSONUtil.toJsonStr(diPreSaleCustomized));
            return;
        }
        if (StringUtils.isBlank(diPreSale.getNicheCode())) {
            log.info("DiPreSaleServiceImpl---tscSendMessageAndSaveAgency()---当前方案没有关联商机，产品方案信息：{}", JSONUtil.toJsonStr(diPreSale));
            return;
        }
        //根据商机获取订单信息
        List<DiOrder> diOrderList = diOrderService.list(new LambdaQueryWrapper<DiOrder>().eq(DiOrder::getNicheNo, diPreSale.getNicheCode()));
        if (CollectionUtils.isEmpty(diOrderList)) {
            log.info("DiPreSaleServiceImpl---tscSendMessageAndSaveAgency()---没有获取到订单数据，商机ID：{}", diPreSale.getNicheCode());
            return;
        }
        if (diOrderList.size() > 1) {
            log.info("DiPreSaleServiceImpl---tscSendMessageAndSaveAgency()---方案关联的订单数据大于1条，商机ID：{}", diPreSale.getNicheCode());
            return;
        }
        sendMessageAndSaveAgency(diPreSale.getPreSaleCode(), diPreSale.getPreSaleName(), diPreSaleCustomized.getTechSupportOwnerCode(), diPreSale.getPreSaleStatus(), diOrderList.get(0));
    }

    private void sendMessageAndSaveAgency(String preSaleCode, String preSaleName, String sendUser, String preSaleStatus, DiOrder diOrder) {
        //发送钉钉消息
        String title = StrUtil.format(DingTalkEnum.TECHNICAL_SUPPORT_NOTICE.getTitle(), preSaleCode);
        String content = StrUtil.format(DingTalkEnum.TECHNICAL_SUPPORT_NOTICE.getMessage(),
                StringUtils.isEmpty(diOrder.getProjectNo()) ? "" : diOrder.getProjectNo(),
                diOrder.getOrderNo(), preSaleCode);
        DiMessageList diMessageList = new DiMessageList();
        diMessageList.setBusinessModule("产品方案");
        diMessageList.setTitle(title);
        diMessageList.setRemarks(diMessageList.getTitle());
        diMessageList.setContent(content);
        diMessageList.setSendingTime(new Date());
        diMessageList.setSendingUser(sendUser);
        diMessageListService.insertDiMessageList(diMessageList);
        //新增成功后生成待办任务
        AgencyTaskInfoDTO agencyTaskInfoDto = new AgencyTaskInfoDTO();
        agencyTaskInfoDto.setBusinessKey(preSaleCode);
        agencyTaskInfoDto.setJumpKey(diOrder.getOrderNo());
        agencyTaskInfoDto.setProjectNo(diOrder.getProjectNo());
        agencyTaskInfoDto.setTaskTypeEnum(AgencyTaskTypeEnum.TECHNICAL_SUPPORT_CHECK);
        agencyTaskInfoDto.setTaskName(preSaleName);
        //获取状态
        Map<String, String> statusMap = remoteDictDataService.queryAsMap("pre_sale_status");
        String taskStateDesc = "";
        if (CollectionUtils.isNotEmpty(statusMap) && statusMap.containsKey(preSaleStatus)) {
            taskStateDesc = statusMap.get(preSaleStatus);
        }
        agencyTaskInfoDto.setTaskStateDesc(taskStateDesc);
        agencyTaskInfoDto.setLiabilityByList(Collections.singletonList(sendUser));
        agencyTaskInfoDto.setType("1");
        String topic = AgencyConstants.AGENCY_TASK_TOPIC + AgencyConstants.DELIMITER_COLON + AgencyConstants.AGENCY_TASK_TAG;
        rocketMQTemplate.syncSend(topic, agencyTaskInfoDto);
    }

    /**
     * 更新产品方案售中状态,基于流程节点
     *
     * @param id
     */
//    public void updatePreSaleOrderStatus(Long id) {
//        DiPreSale diPreSale = diPreSaleMapper.selectById(id);
//        if (Objects.isNull(diPreSale)) {
//            log.info("当前产品方案不存在，无法更新履约状态，产品方案编号:{}", id);
//            return;
//        }
//        if (diPreSale.getPreSaleType().equals(1)) {
//            if (checkProduceComplated(id)) {
//                updatePreSaleOrderStatus(id, OrderPreSaleStatusEnum.TO_BE_COMPLETED.getCode());
//            } else {
//                updatePreSaleOrderStatus(id, OrderPreSaleStatusEnum.FOUR.getCode());
//            }
//            return;
//        }
//        boolean hasMaterialSelection = diPreSaleMaterielSelectionService.hasSelection(id);
//        if (hasMaterialSelection && OrderPreSaleStatusEnum.INIT.getCode().equals(diPreSale.getOrderPreSaleStatus())) {
//            updatePreSaleOrderStatus(id, OrderPreSaleStatusEnum.TWO.getCode());
//            //注意，这里要发送技术支持复核待办和钉钉
//            tscSendMessageAndSaveAgency(diPreSale);
//            return;
//        }
//        if (checkDesignComplated(id) && checkProduceComplated(id)) {
//            updatePreSaleOrderStatus(id, OrderPreSaleStatusEnum.TO_BE_COMPLETED.getCode());
//            return;
//        }
//        if (!checkDesignComplated(id)) {
//            updatePreSaleOrderStatus(id, OrderPreSaleStatusEnum.THREE.getCode());
//            return;
//        }
//        if (!checkProduceComplated(id)) {
//            updatePreSaleOrderStatus(id, OrderPreSaleStatusEnum.FOUR.getCode());
//            return;
//        }
//        if (OrderPreSaleStatusEnum.THREE.getCode().equals(diPreSale.getOrderPreSaleStatus())) {
//            updatePreSaleOrderStatus(id, OrderPreSaleStatusEnum.SIX.getCode());
//        }
//    }
    @Override
    public void updatePreSaleOrderStatus(Long id, Integer status) {
        DiPreSale diPreSaleUpdate = this.lambdaQuery().eq(DiPreSale::getId, id).one();
        Assert.notNull(diPreSaleUpdate, "产品方案不存在");
        log.info("更新产品方案订单状态，产品方案编号:{},原状态:{}，新状态:{}", id, diPreSaleUpdate.getOrderPreSaleStatus(), status);
        diPreSaleUpdate.setOrderPreSaleStatus(status);
        diPreSaleMapper.updateById(diPreSaleUpdate);
        //if (!status.equals(diPreSaleUpdate.getOrderPreSaleStatus())) {
        OrderPreSaleStatusChangedEvent event = new OrderPreSaleStatusChangedEvent();
        event.setPreSaleId(diPreSaleUpdate.getId());
        event.setNicheNo(diPreSaleUpdate.getNicheCode());
        event.setOrderPreSaleStatus(OrderPreSaleStatusEnum.of(status));
        EventBusUtils.publishEvent(event);
        //}
    }

    /**
     * 验证方案清单内必填字段
     *
     * @param diPreSaleManifest
     */
    private void validManifest(DiPreSale preSale, DiPreSaleManifest diPreSaleManifest, DiMaterielPrice materielPrice) {
        if (diPreSaleManifest == null) {
            throw new ServiceException("方案清单未添加，不能锁定");
        }
        if (preSale == null) {
            throw new ServiceException("方案未添加，不能锁定");
        }
        if (materielPrice == null) {
            throw new ServiceException("物料价格未完善，不能锁定");
        }

    }

    @Override
    @Transactional
    public String createNewPlan(Long id) {
        //通过最新方案版本的id查出方案

        DiPreSale diPreSale = diPreSaleMapper.selectById(id);
        DiPreSale curPreSale = diPreSaleMapper.selectOne(Wrappers.<DiPreSale>lambdaQuery().eq(DiPreSale::getPreSaleCode, diPreSale.getPreSaleCode()).orderByDesc(DiPreSale::getVersion).last("LIMIT 1 FOR UPDATE"));
        DiPreSaleCustomized preSaleCustomized = diPreSaleCustomizedMapper.selectOne(Wrappers.<DiPreSaleCustomized>lambdaQuery().eq(DiPreSaleCustomized::getPreSaleId, curPreSale.getId()).orderByDesc(DiPreSaleCustomized::getId).last("limit 1"));
        List<DiPreSaleManifest> diPreSaleManifests = diPreSaleManifestMapper.selectList(Wrappers.<DiPreSaleManifest>lambdaQuery().eq(DiPreSaleManifest::getDiPreSaleId, curPreSale.getId()).eq(DiPreSaleManifest::getManifestVersion, curPreSale.getVersion()).eq(DiPreSaleManifest::getDelFlag, 0));

        Long srcPreSaleId = curPreSale.getId();
        //重置方案版本状态
        curPreSale.setId(null);
        curPreSale.setCreateBy("1");
        curPreSale.setCreateBy(SecurityUtils.getUsername());
        curPreSale.setCreateTime(LocalDateTime.now());
        curPreSale.setUpdateBy(SecurityUtils.getUsername());
        curPreSale.setUpdateTime(LocalDateTime.now());
        curPreSale.setVersion(curPreSale.getVersion() + 1);
        curPreSale.setPreSaleStatus(TECHNICAL_SUPPORT.getCode());
        curPreSale.setPreSaleLockTime(null);
        curPreSale.setDeliveryReviewFlag(null);
        curPreSale.setDeliveryReviewTechSupportFlag(null);
        curPreSale.setFinallyDeliveryWeek(null);
        curPreSale.setDeliveryReviewSupplyFlag(null);
        curPreSale.setDeliveryReviewDeliveryFlag(null);
        diPreSaleMapper.insert(curPreSale);


        if (preSaleCustomized != null) {
            DiPreSaleCustomized newPreSaleCustomized = new DiPreSaleCustomized();
            BeanUtils.copyProperties(preSaleCustomized, newPreSaleCustomized);
            newPreSaleCustomized.setId(null);
            newPreSaleCustomized.setPreSaleId(curPreSale.getId());
//            newPreSaleCustomized.setPreSaleCode(preSaleCustomized.getPreSaleCode());
//            newPreSaleCustomized.setMechanicalDesignQuotation(preSaleCustomized.getMechanicalDesignQuotation());
//            newPreSaleCustomized.setMechanicalDesignDuration(preSaleCustomized.getMechanicalDesignDuration());
//            newPreSaleCustomized.setElectricalDesignQuotation(preSaleCustomized.getElectricalDesignQuotation());
//            newPreSaleCustomized.setElectricalDesignDuration(preSaleCustomized.getElectricalDesignDuration());
//            newPreSaleCustomized.setTechnicalSupportQuotation(preSaleCustomized.getTechnicalSupportQuotation());
//            newPreSaleCustomized.setTechnicalSupportDuration(preSaleCustomized.getTechnicalSupportDuration());
//            newPreSaleCustomized.setRiskCosts(preSaleCustomized.getRiskCosts());
//
//            newPreSaleCustomized.setTechnicalSupport(preSaleCustomized.getTechnicalSupport());
//            newPreSaleCustomized.
            diPreSaleCustomizedMapper.insert(newPreSaleCustomized);

            if ("0".equals(newPreSaleCustomized.getCustomizedBurner())) {
                DiPreSaleCustomizedBurner customizedBurner = diPreSaleCustomizedBurnerMapper.selectOne(Wrappers.<DiPreSaleCustomizedBurner>lambdaQuery().eq(DiPreSaleCustomizedBurner::getCustomizedId, preSaleCustomized.getId()));
                Optional.ofNullable(customizedBurner).ifPresent(burner -> {
                    DiPreSaleCustomizedBurner newCustomizedBurner = new DiPreSaleCustomizedBurner();
                    BeanUtils.copyProperties(burner, newCustomizedBurner);
                    newCustomizedBurner.setId(null);
                    newCustomizedBurner.setCustomizedId(newPreSaleCustomized.getId());
                    diPreSaleCustomizedBurnerMapper.insert(newCustomizedBurner);
                });
            }
            if ("0".equals(newPreSaleCustomized.getCustomizedFan())) {
                DiPreSaleCustomizedFan customizedFan = diPreSaleCustomizedFanMapper.selectOne(Wrappers.<DiPreSaleCustomizedFan>lambdaQuery().eq(DiPreSaleCustomizedFan::getCustomizedId, preSaleCustomized.getId()));
                Optional.ofNullable(customizedFan).ifPresent(fan -> {
                    DiPreSaleCustomizedFan newCustomizedFan = new DiPreSaleCustomizedFan();
                    BeanUtils.copyProperties(fan, newCustomizedFan);
                    newCustomizedFan.setId(null);
                    newCustomizedFan.setCustomizedId(newPreSaleCustomized.getId());
                    diPreSaleCustomizedFanMapper.insert(newCustomizedFan);
                });
            }
            if ("0".equals(newPreSaleCustomized.getCustomizedAir())) {
                DiPreSaleCustomizedAir customizedAir = diPreSaleCustomizedAirMapper.selectOne(Wrappers.<DiPreSaleCustomizedAir>lambdaQuery().eq(DiPreSaleCustomizedAir::getCustomizedId, preSaleCustomized.getId()));
                Optional.ofNullable(customizedAir).ifPresent(air -> {
                    DiPreSaleCustomizedAir newCustomizedAir = new DiPreSaleCustomizedAir();
                    BeanUtils.copyProperties(air, newCustomizedAir);
                    newCustomizedAir.setId(null);
                    newCustomizedAir.setCustomizedId(newPreSaleCustomized.getId());
                    diPreSaleCustomizedAirMapper.insert(newCustomizedAir);
                });
            }
            if ("0".equals(newPreSaleCustomized.getCustomizedHeader())) {
                DiPreSaleCustomizedHeader customizedHeader = diPreSaleCustomizedHeaderMapper.selectOne(Wrappers.<DiPreSaleCustomizedHeader>lambdaQuery().eq(DiPreSaleCustomizedHeader::getCustomizedId, preSaleCustomized.getId()));
                Optional.ofNullable(customizedHeader).ifPresent(header -> {
                    DiPreSaleCustomizedHeader newCustomizedHeader = new DiPreSaleCustomizedHeader();
                    BeanUtils.copyProperties(header, newCustomizedHeader);
                    newCustomizedHeader.setId(null);
                    newCustomizedHeader.setCustomizedId(newPreSaleCustomized.getId());
                    diPreSaleCustomizedHeaderMapper.insert(newCustomizedHeader);
                });
            }
            if ("0".equals(newPreSaleCustomized.getCustomizedPiping())) {
                DiPreSaleCustomizedPiping customizedPiping = diPreSaleCustomizedPipingMapper.selectOne(Wrappers.<DiPreSaleCustomizedPiping>lambdaQuery().eq(DiPreSaleCustomizedPiping::getCustomizedId, preSaleCustomized.getId()));
                Optional.ofNullable(customizedPiping).ifPresent(piping -> {
                    DiPreSaleCustomizedPiping newCustomizedPiping = new DiPreSaleCustomizedPiping();
                    BeanUtils.copyProperties(piping, newCustomizedPiping);
                    newCustomizedPiping.setId(null);
                    newCustomizedPiping.setCustomizedId(newPreSaleCustomized.getId());
                    diPreSaleCustomizedPipingMapper.insert(newCustomizedPiping);
                });
            }
            if ("0".equals(newPreSaleCustomized.getCustomizedPlc())) {
                DiPreSaleCustomizedPlc customizedPlc = diPreSaleCustomizedPlcMapper.selectOne(Wrappers.<DiPreSaleCustomizedPlc>lambdaQuery().eq(DiPreSaleCustomizedPlc::getCustomizedId, preSaleCustomized.getId()));
                Optional.ofNullable(customizedPlc).ifPresent(plc -> {
                    DiPreSaleCustomizedPlc newCustomizedPlc = new DiPreSaleCustomizedPlc();
                    BeanUtils.copyProperties(plc, newCustomizedPlc);
                    newCustomizedPlc.setId(null);
                    newCustomizedPlc.setCustomizedId(newPreSaleCustomized.getId());
                    diPreSaleCustomizedPlcMapper.insert(newCustomizedPlc);
                });
            }
        }

        //最新方案版本的所有版本的方案物料
        if (CollectionUtil.isNotEmpty(diPreSaleManifests)) {
            //最新方案版本的最新物料
//            DiPreSaleManifest diPreSaleManifest = diPreSaleManifests.stream().max(Comparator.comparingInt(DiPreSaleManifest::getManifestVersion)).orElse(null);
            //新增方案物料
//            diPreSaleManifest.setId(null);
//            diPreSaleManifest.setDiPreSaleId(diPreSale.getId());
//            diPreSaleManifest.setManifestVersion(1);
//            diPreSaleManifestMapper.insert(diPreSaleManifest);
            savePreSaleManifest(curPreSale.getId(), diPreSaleManifests, null);
        }
        //新建版本是修改方案工期
/*        if (CollectionUtil.isNotEmpty(diPreSaleManifests)) {
            PreSaleDetailResponse.Fee fee = getFee(diPreSaleManifests.get(0));
            curPreSale.setGuideDuration(fee.computeGuideDurationForProject());
            diPreSaleMapper.updateById(curPreSale);
        }*/
        presaleUrlService.copyTo(srcPreSaleId, curPreSale.getId(), null);
        diPreSaleFeeService.updatePreSaleFee(curPreSale);
        return curPreSale.getId().toString();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SavePreSaleBomConfigResponse savePreSaleBomConfig(SavePreSaleBomConfigRequest request) {
        SavePreSaleBomConfigResponse response = new SavePreSaleBomConfigResponse();
        if (request.getMaterielVersionBomVO() == null) {
            throw new ServiceException("物料BOM信息不能为空");
        }
        DiPreSale curPreSale = iDiPreSaleService.queryDiPreSaleById(request.getPreSaleId());
        DiPreSale newPreSale = diPreSaleMapper.selectOne(Wrappers.<DiPreSale>lambdaQuery().eq(DiPreSale::getPreSaleCode, curPreSale.getPreSaleCode()).orderByDesc(DiPreSale::getId).last("limit 1"));
        DiPreSaleCustomized preSaleCustomized = diPreSaleCustomizedMapper.selectOne(Wrappers.<DiPreSaleCustomized>lambdaQuery().eq(DiPreSaleCustomized::getPreSaleId, newPreSale.getId()).orderByDesc(DiPreSaleCustomized::getId).last("limit 1"));

        if (preSaleCustomized == null) {
//            throw new ServiceException("非标产品方案不存在");
            preSaleCustomized = new DiPreSaleCustomized();
            preSaleCustomized.setPreSaleId(newPreSale.getId());
            preSaleCustomized.setPreSaleCode(newPreSale.getPreSaleCode());
        }

        MaterielVersionBomVO newMaterielVersionBomVO = diMaterielBomService.updateVersionBom(request.getMaterielVersionBomVO());

        log.info("修改BOM信息结果---->savePreSaleBomConfig:", JSON.toJSONString(newMaterielVersionBomVO));
        Long newMaterielVersionId = Objects.nonNull(newMaterielVersionBomVO) ? newMaterielVersionBomVO.getMaterielId() : request.getMaterielVersionBomVO().getMaterielId();
        List<DiPreSaleManifest> diPreSaleManifests = Lists.newArrayList();
        DiMateriel materiel = diMaterielService.getById(newMaterielVersionId);
        DiPreSaleManifest diPreSaleManifest = this.queryFirstManifest(request.getPreSaleId());
        if (diPreSaleManifest == null) {
            diPreSaleManifest = new DiPreSaleManifest();
        }
        diPreSaleManifest.setMaterialCode(materiel.getMaterielNo());
        diPreSaleManifest.setProductName(materiel.getMaterielName());

        diPreSaleManifest.setMaterialVersion(newMaterielVersionId);
        List<MaterielChecklistNewDTO> materielChecklistNewDTOS = iDiMaterielService.queryMaterielPlanCheckListByVersion(newMaterielVersionId);

        // 找到costsValidTime最小的元素
        Optional<Date> minCostsValidTime = materielChecklistNewDTOS.stream().filter(materielChecklistNewDTO -> materielChecklistNewDTO.getCostsValidTime() != null).map(MaterielChecklistNewDTO::getCostsValidTime).min(Comparator.naturalOrder());
        DiPreSaleManifest finalDiPreSaleManifest = diPreSaleManifest;
        minCostsValidTime.ifPresent(validTime -> {
            if (new Date().compareTo(validTime) <= 0) {
                finalDiPreSaleManifest.setExpirationDateEnd(validTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate());
            }
        });
        if (minCostsValidTime.isEmpty()) {
            Long materielId = materiel.getId();
            List<MaterielSupplierDTO> materielSuppliers = iDiMaterielSupplierService.selectDiSupplierListByMaterielId(materielId, null);
            // 筛选出主供应商的有效期最远的一个
            Optional<MaterielSupplierDTO> bestMasterSupplier = materielSuppliers.stream().filter(supplierDTO -> supplierDTO.getIsMaster() == 1).max(Comparator.comparing(MaterielSupplierDTO::getCostsValidTime));
            bestMasterSupplier.ifPresent(supplierDTO -> finalDiPreSaleManifest.setExpirationDateEnd(supplierDTO.getCostsValidTime()));
        }

        diPreSaleManifest.setMechanicalDesignQuotation(request.getMechanicalDesignQuotation());
        diPreSaleManifest.setMechanicalDesignDuration(request.getMechanicalDesignDuration());
        diPreSaleManifest.setElectricalDesignQuotation(request.getElectricalDesignQuotation());
        diPreSaleManifest.setElectricalDesignPeriod(request.getElectricalDesignDuration());
        diPreSaleManifest.setTechnicalSupportQuotation(request.getTechnicalSupportQuotation());
        diPreSaleManifest.setTechnicalSupportDuration(request.getTechnicalSupportDuration());
        diPreSaleManifest.setRiskCost(request.getRiskCosts());

        diPreSaleManifests.add(diPreSaleManifest);
        savePreSaleManifest(newPreSale.getId(), diPreSaleManifests, null);

        preSaleCustomized.setMechanicalDesignQuotation(request.getMechanicalDesignQuotation());
        preSaleCustomized.setMechanicalDesignDuration(request.getMechanicalDesignDuration());
        preSaleCustomized.setElectricalDesignQuotation(request.getElectricalDesignQuotation());
        preSaleCustomized.setElectricalDesignDuration(request.getElectricalDesignDuration());
        preSaleCustomized.setTechnicalSupportQuotation(request.getTechnicalSupportQuotation());
        preSaleCustomized.setTechnicalSupportDuration(request.getTechnicalSupportDuration());
        preSaleCustomized.setRiskCosts(request.getRiskCosts());
        if (preSaleCustomized.getId() != null) {
            diPreSaleCustomizedMapper.updateById(preSaleCustomized);
        } else {
            diPreSaleCustomizedMapper.insert(preSaleCustomized);
        }

        //PreSaleDetailResponse.Fee fee = getFee(diPreSaleManifest);
//统一更新这2个字段
//        BigDecimal feeTotal = Objects.nonNull(fee.getFeeTotal()) ? fee.getFeeTotal() : BigDecimal.ZERO;
//        newPreSale.setGuideDuration(fee.computeGuideDurationForProject());
//        newPreSale.setCostFeeTotal(feeTotal);

        newPreSale.setPackageType(String.valueOf(computePackageTypeFee(newPreSale.getPackageTypeConfig(), newPreSale.getNum(), materiel)));
        iDiPreSaleService.updateById(newPreSale);

        // 插入附件
        diPreSaleUrlMapper.delete(Wrappers.
                <DiPreSaleUrl>lambdaUpdate().
                eq(DiPreSaleUrl::getDiPreSaleId, newPreSale.getId()).
                eq(DiPreSaleUrl::getType, MaterielFileTypeEnum.BOM_ANNEX.getFileType()));

        if (Objects.nonNull(request.getBomAnnexList())) {
            List<DiPreSaleUrl> pidFiles = request.getBomAnnexList().stream().map(fileVo -> {
                DiPreSaleUrl diPreSaleUrl = new DiPreSaleUrl();
                diPreSaleUrl.setDiPreSaleId(newPreSale.getId());
                diPreSaleUrl.setType(MaterielFileTypeEnum.BOM_ANNEX.getFileType());
                diPreSaleUrl.setFileKey(fileVo.getFileKey());
                diPreSaleUrl.setFileName(fileVo.getFileName());
                diPreSaleUrl.setFileUrl(fileVo.getFileUrl());
                diPreSaleUrl.setIsPic(fileVo.getIsPic());
                diPreSaleUrl.setDelFlag(0);
                return diPreSaleUrl;
            }).collect(Collectors.toList());
            diPreSaleUrlMapper.insert(pidFiles);
        }

        diPreSaleFeeService.updatePreSaleFee(newPreSale);

        preSaleHistoryService.saveDiPreSaleHistory(newPreSale, PreSalePhaseEnum.ofPreSaleOrderStatus(newPreSale.getOrderPreSaleStatus()).getCode());
//        curPreSale.setCostFeeTotal(materielSumCosts.add(guideProduceCosts).add(new BigDecimal(curPreSale.getPackageType()))
//                .add(Objects.nonNull(materielFeeDTO.getGuideRiskCosts()) ? materielFeeDTO.getGuideRiskCosts() : BigDecimal.ZERO)
//                .add(Objects.nonNull(request.getInstallationFee()) ? request.getInstallationFee() : BigDecimal.ZERO)
//                .add(Objects.nonNull(materielFeeDTO.getGuideOtherCosts()) ? materielFeeDTO.getGuideOtherCosts() : BigDecimal.ZERO));
//
//        curPreSale.setGuideDuration(materielFeeDTO.getGuideDuration());
        response.setMaterielVersionId(newMaterielVersionId);
        response.setMaterielVersionBomVO(newMaterielVersionBomVO);
        return response;
    }

    @Override
    public DiPreSale queryDiPreSaleById(Long id) {
        return this.lambdaQuery().eq(DiPreSale::getId, id).one();
    }

    @Override
    public List<DiPreSale> queryDiPreSaleByIds(List<Long> ids) {
        return this.lambdaQuery().in(DiPreSale::getId, ids).list();
    }

    @Override
    public DiPreSale queryDiPreSaleByCode(String preCode) {
        return this.lambdaQuery().eq(DiPreSale::getPreSaleCode, preCode).orderByDesc(DiPreSale::getId).last("limit 1").one();
    }

    @Override
    public List<DiPreSale> queryDiPreSaleByCodeList(List<String> preCodes) {
        List<DiPreSale> diPreSales = this.lambdaQuery().in(DiPreSale::getPreSaleCode, preCodes).list();
        Map<Long, Optional<DiPreSale>> mulPreSalesMap = diPreSales.stream().collect(Collectors.groupingBy(DiPreSale::getId, Collectors.maxBy(Comparator.comparingInt(version -> Integer.valueOf(version.getVersion())))));
        List<DiPreSale> resultMaterielVersionList = mulPreSalesMap.values().stream().map(Optional::get).collect(Collectors.toList());
        return resultMaterielVersionList;
    }

    /**
     * 获取方案清单对应费用及工期信息
     *
     * @param diPreSaleManifest
     * @return
     */
    @Override
    public PreSaleDetailResponse.Fee getFee(DiPreSaleManifest diPreSaleManifest) {
        return getFee(diPreSaleManifest, true);
    }

    /**
     * 获取方案清单对应费用及工期信息
     *
     * @param diPreSaleManifest
     * @return
     */
    @Override
    public PreSaleDetailResponse.Fee getFee(DiPreSaleManifest diPreSaleManifest, boolean reloadMode) {
        PreSaleDetailResponse.Fee fee = new PreSaleDetailResponse.Fee();
        DiPreSale curPreSale = iDiPreSaleService.queryDiPreSaleById(diPreSaleManifest.getDiPreSaleId());
        PreSaleTypeEnum preSaleTypeEnum = PreSaleTypeEnum.of(curPreSale.getPreSaleType());
        if (preSaleTypeEnum == PreSaleTypeEnum.TRADE || preSaleTypeEnum == PreSaleTypeEnum.STANDARD) {
            fee.setBomDesignDay(BigDecimal.ZERO);
        } else if (preSaleTypeEnum == PreSaleTypeEnum.NON_STANDARD) {
            fee.setBomDesignDay(MoneyUtils.ifNull(diPreSaleManifest.getBomDesignDay(), DEFAULT_BOM_DESIGN_DAY));
        } else if (preSaleTypeEnum == PreSaleTypeEnum.BIG_NON_STANDARD) {
            //fee.setBomDesignDay(MoneyUtils.ifNull(diPreSaleManifest.getRealBomDesignDay(), BigDecimal.ZERO));
        }
        fee.setRealFee(diPreSaleFeeService.queryFeeForReal(diPreSaleManifest));
        //该方案物料清单
        List<MaterielChecklistNewDTO> materielChecklistNewDTOS = iDiMaterielService.queryMaterielPlanCheckListByVersion(diPreSaleManifest.getMaterialVersion());
        List<MaterielChecklistNewDTO> hasBomTreeList = iMultiVersionMaterielService.parentNodesWithGrandchildren(diPreSaleManifest.getMaterialVersion());

        List<Long> materielIds = materielChecklistNewDTOS.stream().map(MaterielChecklistNewDTO::getMaterielId).collect(Collectors.toList());
        materielIds.addAll(hasBomTreeList.stream().map(MaterielChecklistNewDTO::getMaterielId).collect(Collectors.toList()));
        MaterielFeeQueryDTO materielFeeQueryDTO = new MaterielFeeQueryDTO();

        materielFeeQueryDTO.setMaterielId(diPreSaleManifest.getMaterialVersion());
        MaterielFeeDTO materielFeeDTO = iDiMaterielService.queryMaterielFee(materielFeeQueryDTO);
        //指导工期
        fee.setPreSaleId(diPreSaleManifest.getDiPreSaleId());
        fee.setPreSaleVersion(diPreSaleManifest.getManifestVersion().toString());
        fee.setManifestId(diPreSaleManifest.getId());

        //非标技术支持等7个费用+工期
        DiPreSaleCustomized diPreSaleCustomized = diPreSaleCustomizedMapper.selectOne(new LambdaQueryWrapper<DiPreSaleCustomized>().eq(DiPreSaleCustomized::getPreSaleId, diPreSaleManifest.getDiPreSaleId()));
        if (diPreSaleCustomized != null) {
            fee.setRiskFee(Objects.nonNull(diPreSaleCustomized.getRiskCosts()) ? diPreSaleCustomized.getRiskCosts() : BigDecimal.ZERO);
        }
        String industry = curPreSale.getIndustryCategories();
        String difficultyLevel = curPreSale.getDifficultyLevel();
        DesignFeeResp designFeeResp = quotationCalcService.calcDesignFee(industry, curPreSale.getDifficultyLevel(),
                curPreSale.getMechanicalDifficulty(),
                curPreSale.getElectricalDifficulty(),
                curPreSale.getProductionDifficulty(),
                curPreSale.getAfterSalesDifficulty());
        if (Objects.nonNull(diPreSaleCustomized) && Objects.nonNull(curPreSale.getIndustryCategories()) && Objects.nonNull(curPreSale.getDifficultyLevel())) {
            fee.setTechSupportFee(Objects.nonNull(designFeeResp.getTechnicalSupportCost()) ? designFeeResp.getTechnicalSupportCost() : BigDecimal.ZERO);
            fee.setTechSupportDay(Objects.nonNull(designFeeResp.getTechnicalSupportDay()) ? designFeeResp.getTechnicalSupportDay() : BigDecimal.ZERO);
            fee.setMechineDesignFee(Objects.nonNull(designFeeResp.getMechanicalDesignCost()) ? designFeeResp.getMechanicalDesignCost() : BigDecimal.ZERO);
            fee.setMechineDay(Objects.nonNull(designFeeResp.getMechanicalDesignDay()) ? designFeeResp.getMechanicalDesignDay() : BigDecimal.ZERO);
            fee.setElectricDesignFee(Objects.nonNull(designFeeResp.getElectricalDesignCost()) ? designFeeResp.getElectricalDesignCost() : BigDecimal.ZERO);
            fee.setElectricDay(Objects.nonNull(designFeeResp.getElectricalDesignDay()) ? designFeeResp.getElectricalDesignDay() : BigDecimal.ZERO);


        }
        if (Objects.nonNull(curPreSale.getIndustryCategories()) && Objects.nonNull(curPreSale.getDifficultyLevel())) {
            fee.setDeliveryDebugFee(Objects.nonNull(designFeeResp.getAfterSaleCost()) ? designFeeResp.getAfterSaleCost() : BigDecimal.ZERO);
            fee.setDeliveryDebugDay(Objects.nonNull(designFeeResp.getAfterSaleDay()) ? designFeeResp.getAfterSaleDay() : BigDecimal.ZERO);
        }

        Optional.ofNullable(curPreSale).ifPresent(preSale -> {
            fee.setPreSaleStatus(preSale.getPreSaleStatus());
            fee.setDeliveryReviewTechSupportFlag(preSale.getDeliveryReviewTechSupportFlag());
        });
        fee.setMaterialCode(materielFeeDTO.getMaterielNo());
        fee.setProductName(materielFeeDTO.getMaterielName());
        fee.setUseNum(diPreSaleManifest.getUseNum());
        fee.setMaterialVersion(diPreSaleManifest.getMaterialVersion().toString());
        fee.setQuantity(curPreSale.getNum());
        fee.setPreSaleType(curPreSale.getPreSaleType());
        DiMateriel diMaterielVersion = diMaterielService.getById(diPreSaleManifest.getMaterialVersion());
        //log.info("curPreSale 11111 {}", curPreSale);
        Optional.ofNullable(diMaterielVersion).ifPresent(version -> {
            fee.setShowMaterialVersion(version.getShowVersionNo());
            fee.setProductStandard(version.getProductStandard());
            if (Objects.nonNull(curPreSale.getPreSaleTechnicalCheckTime())) { //技术支持已复核
                fee.setIsShowSyncU9(true);
                if (!MaterielSyncStatusEnum.SYNC_SUCCESS.getStatus().equals(version.getSyncStatus())) {
                    fee.setIsDiabledSyncU9(false);
                } else {
                    fee.setIsDiabledSyncU9(true);
                }
            }
//            Boolean isShowSYncU9 = !version.getShowSync() && Objects.nonNull(curPreSale.getPreSaleLockTime()); //未同步
//            Boolean isDisabledSyncU9 = version.getShowSync(); // 已同步过
//            fee.setIsShowSyncU9(isShowSYncU9);
//            fee.setIsDiabledSyncU9(isDisabledSyncU9);
        });
        Optional.ofNullable(diMaterielVersion).ifPresent(version -> fee.setShowMaterialVersion(version.getShowVersionNo()));
        Optional.ofNullable(materielFeeDTO.getCostsValidTime())
                .ifPresent(date -> fee.setEndTime(materielFeeDTO.getCostsValidTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate()));


        //先取物料当前的指导物料费,如果有则取指导物料费+指导制作费之和
        //如果没有则根据所有未到期的物料清单，取单价*用量之和
        long expireCnt = materielChecklistNewDTOS.stream().filter(dto -> dto.getCostsValidTime() == null || dto.getCostsValidTime().compareTo(new Date()) < 0).map(dto -> dto.getMaterielId()).count();
        fee.setHasNullMaterialFee(false);
        if (expireCnt > 0) {
            fee.setHasNullMaterialFee(true);
        }
        Map<Long, DiMaterielPrice> priceMap = materielPriceService.queryByMateriel(materielIds).stream().collect(Collectors.toMap(DiMaterielPrice::getMaterielId, x -> x));
        BigDecimal materialFee = new BigDecimal(0L);
        BigDecimal singleSuitMaterielFee = new BigDecimal(0L);
        int severalCnt = Objects.nonNull(diPreSaleManifest.getUseNum()) ? diPreSaleManifest.getUseNum() : BigDecimal.ONE.intValue();
        for (MaterielChecklistNewDTO itemCheckList : materielChecklistNewDTOS) {
            int useNum = itemCheckList.getUseNum();
            DiMaterielPrice itemMaterielPrice = priceMap.get(itemCheckList.getMaterielId());
            BigDecimal materialCosts = Objects.nonNull(itemMaterielPrice) && Objects.nonNull(itemMaterielPrice.getGuideMaterialCosts())
                    ? itemMaterielPrice.getGuideMaterialCosts() : BigDecimal.ZERO;
            materialFee = materialFee.add(materialCosts.multiply(new BigDecimal(useNum)));
            singleSuitMaterielFee = singleSuitMaterielFee.add(materialCosts.multiply(new BigDecimal(useNum)));
        }
        materialFee = materialFee.multiply(new BigDecimal(severalCnt));
        //fee.setSingleSuitMaterielFee(singleSuitMaterielFee);
        fee.setMaterialFee(materialFee);
        if (CollectionUtils.isNotEmpty(hasBomTreeList)) {
            BigDecimal materialProductionCost = new BigDecimal(0L);
            BigDecimal singleSuitGuideProductionCosts = new BigDecimal(0L);
            for (MaterielChecklistNewDTO itemCheckList : hasBomTreeList) {
                DiMaterielPrice itemMaterielPrice = priceMap.get(itemCheckList.getMaterielId());
                BigDecimal materialProductionCosts = Objects.nonNull(itemMaterielPrice) && Objects.nonNull(itemMaterielPrice.getGuideProductionCosts())
                        ? itemMaterielPrice.getGuideProductionCosts() : BigDecimal.ZERO;
                materialProductionCost = materialProductionCost.add(materialProductionCosts.multiply(new BigDecimal(itemCheckList.getUseNum())));
                singleSuitGuideProductionCosts = singleSuitGuideProductionCosts.add(materialProductionCosts.multiply(new BigDecimal(itemCheckList.getUseNum())));
            }
            materialProductionCost = materialProductionCost.multiply(new BigDecimal(severalCnt));
            fee.setGuideProductionCosts(materialProductionCost);
            //fee.setSingleSuitGuideProductionCosts(singleSuitGuideProductionCosts);
        }

        // todo 这里后续要查找该方案对应临时物料的费之和
        List<DiPreSaleMaterielSelection> temporaryMaterialList = selectionService.lambdaQuery().eq(DiPreSaleMaterielSelection::getDiPreSaleId, curPreSale.getId()).eq(DiPreSaleMaterielSelection::getDelFlag, 0).list();
        long hasNullPrice = temporaryMaterialList.stream().filter(item -> item.getGuideCosts() == null).count();
        if (hasNullPrice == 0) {
            BigDecimal temporaryMaterialCost = temporaryMaterialList.stream().map(item -> item.getGuideCosts().multiply(new BigDecimal(item.getConsumeQuantity()))).reduce(BigDecimal.ZERO, BigDecimal::add);
            fee.setTemporaryMaterialCosts(temporaryMaterialCost);
        }
        long hasNullProductionCosts = temporaryMaterialList.stream().filter(item -> item.getGuideProductionCosts() == null).count();
        if (hasNullProductionCosts == 0) {
            BigDecimal temporaryMaterialProductionCost = temporaryMaterialList.stream().map(item -> item.getGuideProductionCosts().multiply(new BigDecimal(item.getConsumeQuantity()))).reduce(BigDecimal.ZERO, BigDecimal::add);
            fee.setTemporaryProductionCosts(temporaryMaterialProductionCost);
        }

        //生产费用，停用了，设置成0
//        fee.setProductFee(Objects.nonNull(materielFeeDTO.getGuideProduceCosts()) ? materielFeeDTO.getGuideProduceCosts() : BigDecimal.ZERO);
        //fee.setProductFee(BigDecimal.ZERO);

        DiMateriel diMateriel = iDiMaterielService.lambdaQuery().eq(DiMateriel::getId, materielFeeDTO.getMaterielId()).one();
        BigDecimal packageFee = computePackageTypeFee(curPreSale.getPackageTypeConfig(), curPreSale.getNum(), diMateriel);
        packageFee = packageFee.multiply(new BigDecimal(severalCnt));
        fee.setPackageFee(packageFee);

        //fee.setSceneInstallFee(Objects.nonNull(curPreSale.getInstallationFee()) ? curPreSale.getInstallationFee() : BigDecimal.ZERO);
        Integer useNum = PreSaleTypeEnum.TRADE.getCode().equals(curPreSale.getPreSaleType()) ? diPreSaleManifest.getUseNum() : curPreSale.getNum();
        fee.setProductDay(computeProductDay(useNum, PreSaleTypeEnum.of(curPreSale.getPreSaleType())));

        //取物料清单中最大的指导物料货期
        if (curPreSale.getPreSaleType().equals(PreSaleTypeEnum.TRADE.getCode())) {
            fee.setSupplyDay(materielFeeDTO.getGuideDuration());
        } else {
            fee.setSupplyDay(computeSupplyDay(materielChecklistNewDTOS, materielFeeDTO));
        }

        DiMarketingNiche diMarketingNiche = iDiMarketingNicheService.lambdaQuery().eq(DiMarketingNiche::getId, curPreSale.getNicheId()).eq(DiMarketingNiche::getDelFlag, 0).one();
        if (Objects.isNull(diMarketingNiche) || Objects.isNull(diMarketingNiche.getNeedPostSaleInstall()) || !diMarketingNiche.getNeedPostSaleInstall()) {
            fee.setDeliveryDebugFee(BigDecimal.ZERO);
            fee.setDeliveryDebugDay(BigDecimal.ZERO);
            fee.setNeedPostSaleInstall(false);
        } else {
            fee.setNeedPostSaleInstall(true);
        }

//        fee.setGuideOtherCosts(Objects.nonNull(materielFeeDTO.getGuideOtherCosts()) ? materielFeeDTO.getGuideOtherCosts() : BigDecimal.ZERO);

        BigDecimal otherFee = (Objects.nonNull(fee.getDeliveryDebugFee()) ? fee.getDeliveryDebugFee() : BigDecimal.ZERO)
                .add(Objects.nonNull(fee.getPackageFee()) ? fee.getPackageFee() : BigDecimal.ZERO)
                .add(Objects.nonNull(fee.getRiskFee()) ? fee.getRiskFee() : BigDecimal.ZERO)
//                .add(Objects.nonNull(fee.getSceneInstallFee()) ? fee.getSceneInstallFee() : BigDecimal.ZERO)
//                .add(Objects.nonNull(fee.getGuideOtherCosts()) ? fee.getGuideOtherCosts() : BigDecimal.ZERO);
                ;
        fee.setOtherFee(otherFee);


        if (diMateriel.getIsStandard().equals(1) || PreSaleTypeEnum.TRADE.getCode().equals(curPreSale.getPreSaleType()) ||
                (Objects.nonNull(fee.getTechSupportFee())
                        && Objects.nonNull(fee.getMechineDesignFee())
                        && Objects.nonNull(fee.getElectricDesignFee())
                        && Objects.nonNull(fee.getMaterialFee())
                        //&& Objects.nonNull(fee.getProductFee())
                        && Objects.nonNull(fee.getDeliveryDebugFee())
                        && Objects.nonNull(fee.getPackageFee())
                        // && Objects.nonNull(curPreSale.getInstallationFee())
                )) {
            BigDecimal[] fees = new BigDecimal[]{
                    Objects.nonNull(fee.getTechSupportFee()) ? fee.getTechSupportFee() : BigDecimal.ZERO,
                    Objects.nonNull(fee.getMechineDesignFee()) ? fee.getMechineDesignFee() : BigDecimal.ZERO,
                    Objects.nonNull(fee.getElectricDesignFee()) ? fee.getElectricDesignFee() : BigDecimal.ZERO,
                    Objects.nonNull(fee.getMaterialFee()) ? fee.getMaterialFee() : BigDecimal.ZERO,
                    Objects.nonNull(fee.getGuideProductionCosts()) ? fee.getGuideProductionCosts() : BigDecimal.ZERO,
                    Objects.nonNull(fee.getTemporaryMaterialCosts()) ? fee.getTemporaryMaterialCosts() : BigDecimal.ZERO,
                    Objects.nonNull(fee.getTemporaryProductionCosts()) ? fee.getTemporaryProductionCosts() : BigDecimal.ZERO,
                    //Objects.nonNull(fee.getProductFee()) ? fee.getProductFee() : BigDecimal.ZERO,
                    Objects.nonNull(fee.getDeliveryDebugFee()) ? fee.getDeliveryDebugFee() : BigDecimal.ZERO,
                    Objects.nonNull(fee.getPackageFee()) ? fee.getPackageFee() : BigDecimal.ZERO,
                    Objects.nonNull(fee.getRiskFee()) ? fee.getRiskFee() : BigDecimal.ZERO};
            BigDecimal feeTotal = Arrays.stream(fees).reduce(BigDecimal.ZERO, BigDecimal::add);
            fee.setFeeTotal(feeTotal);
            if (PreSaleTypeEnum.TRADE.getCode().equals(curPreSale.getPreSaleType())) {
                if (materielFeeDTO.getSafetyStock() == null || materielFeeDTO.getSafetyStock() == 0) {
                    fee.setGuideDuration(materielFeeDTO.getGuideDuration());
                } else {
                    fee.setGuideDuration(BigDecimal.ZERO);
                }
            } else {
                fee.setGuideDuration(curPreSale.getGuideDuration());
            }
//            BigDecimal[] withoutTemporaryFees = new BigDecimal[]{Objects.nonNull(fee.getTechSupportFee()) ? fee.getTechSupportFee() : BigDecimal.ZERO,
//                    Objects.nonNull(fee.getMechineDesignFee()) ? fee.getMechineDesignFee() : BigDecimal.ZERO,
//                    Objects.nonNull(fee.getElectricDesignFee()) ? fee.getElectricDesignFee() : BigDecimal.ZERO,
//                    Objects.nonNull(fee.getMaterialFee()) ? fee.getMaterialFee() : BigDecimal.ZERO,
//                    Objects.nonNull(fee.getProductFee()) ? fee.getProductFee() : BigDecimal.ZERO,
//                    Objects.nonNull(fee.getDeliveryDebugFee()) ? fee.getDeliveryDebugFee() : BigDecimal.ZERO,
//                    Objects.nonNull(fee.getPackageFee()) ? fee.getPackageFee() : BigDecimal.ZERO,
//                    Objects.nonNull(curPreSale.getInstallationFee()) ? curPreSale.getInstallationFee() : BigDecimal.ZERO,
//                    //Objects.nonNull(fee.getGuideOtherCosts()) ? fee.getGuideOtherCosts() : BigDecimal.ZERO,
//                    Objects.nonNull(fee.getRiskFee()) ? fee.getRiskFee() : BigDecimal.ZERO,
//                    Objects.nonNull(fee.getGuideProductionCosts()) ? fee.getGuideProductionCosts() : BigDecimal.ZERO};
            //BigDecimal withoutTemporaryFeeTotal = Arrays.stream(withoutTemporaryFees).reduce(BigDecimal.ZERO, BigDecimal::add);
            //fee.setFeeTotalWithoutTemporary(withoutTemporaryFeeTotal);
        } else {
            fee.setFeeTotal(new BigDecimal(0L));
        }
        fee.setMaterielChecklistNewDTOS(materielChecklistNewDTOS);

//        StringBuilder deliveryAddress = new StringBuilder();
//        if (Objects.nonNull(curPreSale.getCountry())) {
//            deliveryAddress.append(curPreSale.getCountry());
//        }
//        if (Objects.nonNull(curPreSale.getProvince())) {
//            deliveryAddress.append(curPreSale.getProvince());
//        }
//        if (Objects.nonNull(curPreSale.getCity())) {
//            deliveryAddress.append(curPreSale.getCity());
//        }
//        if (Objects.nonNull(curPreSale.getArea())) {
//            deliveryAddress.append(curPreSale.getArea());
//        }
//        if (Objects.nonNull(curPreSale.getCommonDeliveryAddress())) {
//            deliveryAddress.append(curPreSale.getCommonDeliveryAddress());
//        }
//        fee.setDeliveryAddress(deliveryAddress.toString());

        if (PreSaleTypeEnum.nonStandard(curPreSale.getPreSaleType())) {
            if (diPreSaleCustomized != null) {
                fee.setDiPreSaleCustomized(diPreSaleCustomized);
            }
        }
        return fee;
    }


    /**
     * 计算生产周期
     *
     * @param
     * @return
     */
    private BigDecimal computeProductDay(Integer useNum, PreSaleTypeEnum preSaleType) {
        if (preSaleType == PreSaleTypeEnum.TRADE) {
            return BigDecimal.ZERO;
        }
        Assert.notNull(useNum, "需求套数不能为空");
        if (useNum <= 4) {
            return new BigDecimal(21);
        }
        if (useNum > 4 && useNum <= 10) {
            return new BigDecimal(28);
        }
        if (useNum > 10 && useNum <= 20) {
            return new BigDecimal(35);
        }
        if (useNum > 20) {
            return new BigDecimal(42);
        }
        return new BigDecimal(0);
//        return Objects.nonNull(materielFeeDTO.getGuideProduceDuration()) ? materielFeeDTO.getGuideProduceDuration() : null;
    }

    /**
     * 计算包装费用
     *
     * @param packageType
     * @param useNum
     * @param materiel
     * @return
     */
    private BigDecimal computePackageTypeFee(String packageType, Integer useNum, DiMateriel materiel) {
        BigDecimal length = Objects.nonNull(materiel.getMaterielLength()) && materiel.getMaterielLength().longValue() != 0 ? materiel.getMaterielLength().add(new BigDecimal(10)) : BigDecimal.ZERO;
        BigDecimal width = Objects.nonNull(materiel.getMaterielWidth()) && materiel.getMaterielWidth().longValue() != 0 ? materiel.getMaterielWidth().add(new BigDecimal(10)) : BigDecimal.ZERO;
        BigDecimal height = Objects.nonNull(materiel.getMaterielHigh()) && materiel.getMaterielHigh().longValue() != 0 ? materiel.getMaterielHigh().add(new BigDecimal(20)) : BigDecimal.ZERO;
        BigDecimal volumetric = (length.multiply(width).multiply(height)).divide(new BigDecimal(1000000000), RoundingMode.HALF_UP).setScale(4, RoundingMode.HALF_UP);
        BigDecimal preSalePackageType = StringUtils.isNotEmpty(packageType) ? new BigDecimal(packageType) : BigDecimal.ZERO;
//        useNum = Objects.nonNull(useNum) ? useNum : 0;
        BigDecimal packageFee = preSalePackageType.multiply(volumetric);
        return packageFee.longValue() == 0 ? preSalePackageType : packageFee;
    }

//    /**
//     * 计算物料指导工期
//     *
//     * @return
//     */
//    private BigDecimal computeGuideDuration(BigDecimal supportDay, BigDecimal electricDay, BigDecimal mechineDay, BigDecimal supplyDay, BigDecimal productDay, BigDecimal deliveryDebugDay) {
//        BigDecimal supportPeriod = Objects.nonNull(supportDay) ? supportDay : BigDecimal.ZERO;
//        BigDecimal electricalDesignPeriod = Objects.nonNull(electricDay) ? electricDay : BigDecimal.ZERO;
//        BigDecimal mechanicalDesignDuration = Objects.nonNull(mechineDay) ? mechineDay : BigDecimal.ZERO;
//        BigDecimal supplyDate = Objects.nonNull(supplyDay) ? supplyDay : BigDecimal.ZERO;
//        BigDecimal productionDuration = Objects.nonNull(productDay) ? productDay : BigDecimal.ZERO;
//        BigDecimal implementationDuration = Objects.nonNull(deliveryDebugDay) ? deliveryDebugDay : BigDecimal.ZERO;
//        BigDecimal durations = electricalDesignPeriod.compareTo(mechanicalDesignDuration) > 0 ? electricalDesignPeriod : mechanicalDesignDuration;
//
//        BigDecimal varX = supportPeriod.add(supplyDate).add(new BigDecimal(7));
//        BigDecimal varY = durations;
//
//        BigDecimal resultDurations = varX.compareTo(varY) > 0 ? varX : varY;
//        return resultDurations.add(productionDuration);
//    }

    /**
     * 计算供应周期
     *
     * @param materielChecklistNewDTOS
     * @param materielFeeDTO
     * @return
     */
    private BigDecimal computeSupplyDay(List<MaterielChecklistNewDTO> materielChecklistNewDTOS, MaterielFeeDTO materielFeeDTO) {
        //先取当前物料的指导物料货期字段，如果为空，则取物料清单中最大的指导物料货期
//        MaterielChecklistNewDTO materielDurationFilter = materielChecklistNewDTOS.stream()
//                .filter(dto -> dto.getSafetyStock() == null || dto.getSafetyStock().equals(0))
//                .filter(x -> x.getGuideMaterielDuration() != null)
//                .max(Comparator.comparing(MaterielChecklistNewDTO::getGuideMaterielDuration)).orElse(null);
        List<MaterielChecklistNewDTO> unsafe = materielChecklistNewDTOS.stream()
                .filter(dto -> dto.getSafetyStock() == null || dto.getSafetyStock().equals(0)).toList();
        if (CollectionUtils.isEmpty(unsafe)) {
            return BigDecimal.ZERO;
        }

        MaterielChecklistNewDTO materielDurationFilter = unsafe.stream().filter(x -> x.getGuideMaterielDuration() != null)
                .max(Comparator.comparing(MaterielChecklistNewDTO::getGuideMaterielDuration)).orElse(null);

        //获取物料清单中物料货期最大值，
        return //Objects.nonNull(materielFeeDTO.getGuideMaterielDuration()) ? materielFeeDTO.getGuideMaterielDuration() :
                (Objects.nonNull(materielDurationFilter) ? materielDurationFilter.getGuideMaterielDuration() : null);
    }

    /**
     * 获取物料费用
     *
     * @param diPreSale
     * @return
     */
    @Override
    public List<PreSaleDetailResponse.Fee> getFeeList(DiPreSale diPreSale, boolean ignoreCalcFee) {
        List<PreSaleDetailResponse.Fee> fees = new ArrayList<>();
        //产品方案物料
        List<DiPreSale> preSales = diPreSaleMapper.selectList(Wrappers.<DiPreSale>lambdaQuery().eq(DiPreSale::getPreSaleCode, diPreSale.getPreSaleCode()).orderByDesc(DiPreSale::getId));
        Long lastId = preSales.get(0).getId();
        Map<Long, DiPreSale> diPreSaleMap = preSales.stream().collect(Collectors.toMap(DiPreSale::getId, Function.identity()));
        List<DiPreSaleManifest> diPreSaleManifests = diPreSaleManifestMapper.selectList(Wrappers.<DiPreSaleManifest>lambdaQuery().in(DiPreSaleManifest::getDiPreSaleId, preSales.stream().map(DiPreSale::getId).toList())
                .orderByDesc(DiPreSaleManifest::getDiPreSaleId));

        diPreSaleManifests.stream().forEach(diPreSaleManifest -> {
            //该方案物料清单
            DiPreSale temp = diPreSaleMap.get(diPreSaleManifest.getDiPreSaleId());
            if ((ignoreCalcFee && lastId.equals(diPreSaleManifest.getDiPreSaleId())) || diPreSaleFeeService.reCalcStatus(temp)) {
                fees.add(getFee(diPreSaleManifest, true));
            } else {
                fees.add(this.getFeeWithoutCalcFee(temp, diPreSaleManifest));
            }
            fees.get(fees.size() - 1).setQuoteTotal(diPreSaleManifest.getQuoteTotal());
            fees.get(fees.size() - 1).setSingleSaleQuote(diPreSaleManifest.getSingleSaleQuote());
        });
        return fees.stream().sorted((a, b) -> {
            if (StringUtils.isBlank(b.getPreSaleVersion())) {
                return -1;
            }
            if (StringUtils.isBlank(a.getPreSaleVersion())) {
                return 1;
            }
            return Integer.valueOf(b.getPreSaleVersion()).compareTo(Integer.valueOf(a.getPreSaleVersion()));
        }).collect(Collectors.toList());
    }

    /**
     * 优化后的获取物料费用方法
     * 针对type=4和type=5的性能优化
     *
     * @param diPreSale
     * @param ignoreCalcFee
     * @return
     */
    public List<PreSaleDetailResponse.Fee> getFeeListOptimized(DiPreSale diPreSale, boolean ignoreCalcFee) {
        // 优化1: 优先使用历史缓存数据，避免重复计算
        if (!ignoreCalcFee && !diPreSaleFeeService.reCalcStatus(diPreSale)) {
            List<PreSaleDetailResponse.Fee> cachedFees = getCachedFeeList(diPreSale);
            if (CollectionUtils.isNotEmpty(cachedFees)) {
                log.info("使用缓存费用数据，方案ID: {}", diPreSale.getId());
                return cachedFees;
            }
        }

        // 优化2: 批量查询，减少数据库交互次数
        List<DiPreSale> preSales = diPreSaleMapper.selectList(
            Wrappers.<DiPreSale>lambdaQuery()
                .eq(DiPreSale::getPreSaleCode, diPreSale.getPreSaleCode())
                .orderByDesc(DiPreSale::getId)
        );

        if (CollectionUtils.isEmpty(preSales)) {
            return new ArrayList<>();
        }

        Long lastId = preSales.get(0).getId();
        Map<Long, DiPreSale> diPreSaleMap = preSales.stream()
            .collect(Collectors.toMap(DiPreSale::getId, Function.identity()));

        List<DiPreSaleManifest> diPreSaleManifests = diPreSaleManifestMapper.selectList(
            Wrappers.<DiPreSaleManifest>lambdaQuery()
                .in(DiPreSaleManifest::getDiPreSaleId, preSales.stream().map(DiPreSale::getId).toList())
                .orderByDesc(DiPreSaleManifest::getDiPreSaleId)
        );

        // 优化3: 并行处理费用计算，提高处理速度
        List<PreSaleDetailResponse.Fee> fees = diPreSaleManifests.parallelStream()
            .map(diPreSaleManifest -> {
                DiPreSale temp = diPreSaleMap.get(diPreSaleManifest.getDiPreSaleId());
                PreSaleDetailResponse.Fee fee;

                if ((ignoreCalcFee && lastId.equals(diPreSaleManifest.getDiPreSaleId()))
                    || diPreSaleFeeService.reCalcStatus(temp)) {
                    fee = getFee(diPreSaleManifest, true);
                } else {
                    fee = this.getFeeWithoutCalcFee(temp, diPreSaleManifest);
                }

                fee.setQuoteTotal(diPreSaleManifest.getQuoteTotal());
                fee.setSingleSaleQuote(diPreSaleManifest.getSingleSaleQuote());
                return fee;
            })
            .sorted((a, b) -> {
                if (StringUtils.isBlank(b.getPreSaleVersion())) {
                    return -1;
                }
                if (StringUtils.isBlank(a.getPreSaleVersion())) {
                    return 1;
                }
                return Integer.valueOf(b.getPreSaleVersion()).compareTo(Integer.valueOf(a.getPreSaleVersion()));
            })
            .collect(Collectors.toList());

        return fees;
    }

    /**
     * 获取缓存的费用列表
     *
     * @param diPreSale
     * @return
     */
    private List<PreSaleDetailResponse.Fee> getCachedFeeList(DiPreSale diPreSale) {
        try {
            // 尝试从历史记录中获取费用数据
            PreSaleDetailResponse history = this.selectHistoryOfDiPreSale(
                diPreSale.getId(),
                PreSalePhaseEnum.ofPreSaleOrderStatus(diPreSale.getOrderPreSaleStatus()).getCode(),
                false
            );

            if (history != null && CollectionUtils.isNotEmpty(history.getFees())) {
                log.info("从历史记录获取费用数据，方案ID: {}", diPreSale.getId());
                return history.getFees();
            }
        } catch (Exception e) {
            log.warn("获取缓存费用数据失败，方案ID: {}, 错误: {}", diPreSale.getId(), e.getMessage());
        }

        return null;
    }


    public void getLable(Long materielId, List<LabelMaterielWarehouseListDTO> labelMaterielWarehouseListDTOS, int pageNum) {
        LabelMaterielWarehouseListVO labelMaterielWarehouseListVO = new LabelMaterielWarehouseListVO();
        labelMaterielWarehouseListVO.setMaterielId(materielId);
        labelMaterielWarehouseListVO.setPageNum(pageNum);
        labelMaterielWarehouseListVO.setPageSize(100);
        PageWrapper<List<LabelMaterielWarehouseListDTO>> pageWrapper = diMaterielLabelWarehouseService.materielLabelList(labelMaterielWarehouseListVO);
        if (Objects.nonNull(pageWrapper) && CollectionUtils.isNotEmpty(pageWrapper.getList())) {
            labelMaterielWarehouseListDTOS.addAll(pageWrapper.getList());
            if (labelMaterielWarehouseListDTOS.size() < pageWrapper.getTotal()) {
                getLable(materielId, labelMaterielWarehouseListDTOS, pageNum + 1);
            }
        }
    }


    /**
     * 查询售前方案列表
     *
     * @param request 售前方案
     * @return 售前方案
     */
    @Override
    public PageWrapper<List<PreSaleListResponse>> selectDiPreSaleList(PreSaleListRequest request) {

        /*//当前登录人id
        Long userid = SecurityUtils.getLoginUser().getUserid();
        R<SysUserRoleData> sysUserDataSource = remoteSysService.getSysUserDataSource(userid);
        if (sysUserDataSource.isError()) {
            throw new ServiceException("获取登录人信息失败");
        }

        List<String> jobNumberList = null;
        //判断当亲登录者数据可见范围
        if (MaterialConstants.ZERO.equals(sysUserDataSource.getData().getType())) {
            //如果是限制范围的，构建登录人工号已经他下属层级工号
            jobNumberList = sysUserDataSource.getData().getUserDataList().stream().map(SysUserRoleData.roleData::getJobNumber).toList();
        }*/
        //数据权限处理
//        List<String> jobNumberList = commonService.findLoginJobNumberList();
//        if (CollectionUtil.isNotEmpty(jobNumberList)) {
//            request.setJobNumberList(jobNumberList);
//        }

        Page page = PageHelper.startPage(request.getPageNum(), request.getPageSize());
        List<DiPreSale> diPreSales = diPreSaleMapper.selectDiPreSaleList(request);
        //返回
        List<PreSaleListResponse> resultList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(diPreSales)) {

            //产品方案物料
            List<DiPreSaleManifest> diPreSaleManifests = diPreSaleManifestMapper.selectList(Wrappers.<DiPreSaleManifest>lambdaQuery().in(DiPreSaleManifest::getDiPreSaleId, diPreSales.stream().map(DiPreSale::getId).collect(Collectors.toList())));
            //物料集合
            Map<Long, DiMateriel> materielMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(diPreSaleManifests)) {
                List<DiMateriel> materielList = iDiMaterielService.list(Wrappers.<DiMateriel>lambdaQuery().in(DiMateriel::getId, diPreSaleManifests.stream().map(DiPreSaleManifest::getMaterialVersion).collect(Collectors.toList())));

                if (CollectionUtils.isNotEmpty(materielList)) {
                    materielMap = materielList.stream().collect(Collectors.toMap(x -> x.getId(), x -> x, (a, b) -> {
                        return a;
                    }));
                }
            }
            //产品方案物料map
            Map<Long, List<DiPreSaleManifest>> preSaleManifestMap = diPreSaleManifests.stream().collect(Collectors.groupingBy(DiPreSaleManifest::getDiPreSaleId));
            List<DiPreSaleQuoteDetail> xx = quoteService.queryPreSaleQuoteDetailListByPreSaleId(diPreSales.stream().map(DiPreSale::getId).toList());
            Map<Long, List<DiPreSaleQuoteDetail>> quoteDetailMap = xx.stream().collect(Collectors.groupingBy(DiPreSaleQuoteDetail::getPreSaleId));
            for (DiPreSale diPreSale : diPreSales) {
                PreSaleListResponse preSaleListResponse = preSaleConverUtil.converToPreSaleResponse(diPreSale);
//                    if (CollectionUtils.isEmpty(preSaleManifestMap.get(diPreSale.getId()))) {
//                        continue;
//                    }
                PreSaleDetailResponse.Fee historyfee = this.queryHistoryFeeFirst(diPreSale.getId(), diPreSale.getVersion());

                preSaleListResponse.setExpecteDate(String.valueOf(diPreSale.getGuideDuration()));
                if (historyfee != null) {
                    preSaleListResponse.setExpecteDate(String.valueOf(historyfee.getGuideDuration()));
                }
                preSaleListResponse.setOrderType(String.valueOf(diPreSale.getOrderType()));
                preSaleListResponse.setPreSaleStatus(String.valueOf(diPreSale.getPreSaleStatus()));

                List<DiPreSaleManifest> diMaterielList = preSaleManifestMap.get(diPreSale.getId());
                DiMateriel diMateriel = null;
                if (CollectionUtils.isNotEmpty(diMaterielList)) {
                    DiPreSaleManifest diPreSaleManifest = diMaterielList.get(0);
                    preSaleListResponse.setProductName(diPreSaleManifest.getProductName());
                    diMateriel = materielMap.get(diPreSaleManifest.getMaterialVersion());
                }

                if (diMateriel != null) {
                    preSaleListResponse.setMaterialCode(diMateriel.getMaterielNo());
                    DiMaterialStorageVo materialStockInfo = diMaterialStockInfoService.getMaterialStockInfo(String.valueOf(diMateriel.getId()));
                    if (Objects.nonNull(materialStockInfo)) {
                        preSaleListResponse.setStockNum(String.valueOf(materialStockInfo.getMaterialStock()));
                    }
                }
                if (StringUtils.isEmpty(preSaleListResponse.getStockNum())) {
                    preSaleListResponse.setStockNum("0");
                }
                preSaleListResponse.setCountryCode(diPreSale.getCountryCode());
                preSaleListResponse.setProvinceCode(diPreSale.getProvince());
                preSaleListResponse.setCityCode(diPreSale.getCity());
                preSaleListResponse.setAreaCode(diPreSale.getArea());
                //版本
                Long materielVersionId = CollectionUtils.isNotEmpty(preSaleManifestMap.get(diPreSale.getId())) ? preSaleManifestMap.get(diPreSale.getId()).get(0).getMaterialVersion() : null;
                if (Objects.nonNull(materielVersionId)) {
                    DiMateriel materielVersion = diMaterielService.getById(materielVersionId);
                    Optional.ofNullable(materielVersion).ifPresent(v -> preSaleListResponse.setMaterialVersion(materielVersion.getShowVersionNo()));
                }
                DiPreSaleCustomized diPreSaleCustomized = diPreSaleCustomizedMapper.selectOne(Wrappers.<DiPreSaleCustomized>lambdaQuery().eq(DiPreSaleCustomized::getPreSaleId, diPreSale.getId()).orderByDesc(DiPreSaleCustomized::getId).last("limit 1"));
                if (Objects.nonNull(diPreSaleCustomized)) {
                    Map<String, String> userNameMap = Maps.newHashMap();
                    R<List<DdUserDTO>> userListResult = remoteDbcService.queryDdUserList(QueryDdUserDTO.builder().jobNumberList(new ArrayList<>(Lists.newArrayList(diPreSaleCustomized.getTechSupportOwnerCode()))).build());
                    if (userListResult.isSuccess()) {
                        userNameMap = userListResult.getData().stream().collect(Collectors.toMap(DdUserDTO::getJobNumber, DdUserDTO::getName));
                        diPreSaleCustomized.setTechSupportOwnerName(userNameMap.get(diPreSaleCustomized.getTechSupportOwnerCode()));
                        preSaleListResponse.setTechSupportOwnerCode(diPreSaleCustomized.getTechSupportOwnerCode());
                        preSaleListResponse.setTechSupportOwnerName(userNameMap.get(diPreSaleCustomized.getTechSupportOwnerCode()));
                    }
                }
                if (!PreSaleTypeEnum.TRADE.getCode().equals(diPreSale.getPreSaleType()) && quoteDetailMap.containsKey(diPreSale.getId())) {
                    preSaleListResponse.setHasQuoted(1);
                } else {
                    preSaleListResponse.setHasQuoted(0);
                }
                resultList.add(preSaleListResponse);
            }
        }
        return PageHelp.render(page, resultList);
    }

    /**
     * 分页查询方案
     *
     * @param diPreSale 售前方案
     * @return
     */
    @Override
    public PageWrapper<List<PreSalesPageResponse>> selectDiPreSaleListForRemote(PreSaleListRequest diPreSale) {

        Page page = PageHelper.startPage(diPreSale.getPageNum(), diPreSale.getPageSize());
        List<DiPreSale> diPreSales = diPreSaleMapper.selectList(Wrappers.<DiPreSale>lambdaQuery().ne(DiPreSale::getPreSaleStatus, 6).ne(DiPreSale::getPreSaleType, 3));
        if (CollectionUtil.isNotEmpty(diPreSales)) {
            Map<Long, DiPreSale> preSaleMap = diPreSales.stream().collect(Collectors.toMap(DiPreSale::getId, Function.identity()));
            List<DiPreSaleManifest> diPreSaleManifestList = diPreSaleManifestMapper.selectList(Wrappers.<DiPreSaleManifest>lambdaQuery().in(DiPreSaleManifest::getDiPreSaleId, diPreSales.stream().map(DiPreSale::getId).collect(Collectors.toList())).eq(DiPreSaleManifest::getDelFlag, 0));
            if (CollectionUtil.isNotEmpty(diPreSaleManifestList)) {


                Map<Long, List<DiPreSaleManifest>> preSaleManifestMap = diPreSaleManifestList.stream().collect(Collectors.groupingBy(DiPreSaleManifest::getDiPreSaleId));

                List<PreSalesPageResponse> preSalesPageResponses = preSaleManifestMap.keySet().stream().map(key -> {
                    PreSalesPageResponse preSalesPageResponse = new PreSalesPageResponse();
                    if (Objects.nonNull(preSaleMap.get(key))) {
                        List<DiProjectRelation> relationList = diProjectRelationMapper.selectList(Wrappers.<DiProjectRelation>lambdaQuery().eq(DiProjectRelation::getRelationNo, preSaleMap.get(key).getNicheCode()).eq(DiProjectRelation::getDelFlag, 0));
                        if (CollectionUtils.isNotEmpty(relationList)) {
                            preSalesPageResponse.setProjectNo(relationList.get(0).getProjectNo());
                        }
                    }

                    List<PreSalesPageResponse.PreSaleManifest> preSaleManifests = preSaleManifestMap.get(key).stream().map(diPreSaleManifest -> {
                        PreSalesPageResponse.PreSaleManifest preSaleManifest = new PreSalesPageResponse.PreSaleManifest();
                        preSaleManifest.setPreSaleManifestId(diPreSaleManifest.getId());
                        List<Long> materielIdList = new ArrayList<>();
                        materielIdList.add(diPreSaleManifest.getMaterialVersion());
                        //子料
                        List<Long> subMaterielIdList = treeBomService.loadSubMateriel(diPreSaleManifest.getMaterialVersion(), Integer.MAX_VALUE).stream().map(DiMaterielBom::getMaterielId)
                                .collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(subMaterielIdList)) {
                            materielIdList.addAll(subMaterielIdList);
                        }
                        //物料
                        List<DiMateriel> diMateriels = diMaterielService.queryMaterielByIds(materielIdList);

                        if (CollectionUtils.isNotEmpty(diMateriels)) {
                            preSaleManifest.setMaterialCode(diMateriels.stream().map(DiMateriel::getMaterielNo).collect(Collectors.toList()));
                        }
                        return preSaleManifest;
                    }).collect(Collectors.toList());

                    preSalesPageResponse.setPreSaleManifests(preSaleManifests);
                    return preSalesPageResponse;
                }).collect(Collectors.toList());

                return PageHelp.render(page, preSalesPageResponses);
            }
        }

        return PageHelp.render(page, null);
    }

    /**
     * 新增售前方案
     *
     * @param request 售前方案
     * @return 结果
     */
    @Transactional
    @Override
    public String insertDiPreSale(PreSaleAddRequest request) {
        if (CollectionUtils.isNotEmpty(request.getPreSaleManifests())) {
            request.getPreSaleManifests().forEach(x -> {
                if (x.getUseNum() == null) {
                    x.setUseNum(1);
                }
            });
        }
        //2024-08-22，讨论决定，以商机的主要销售和共享销售为准，在商机的主要销售或共享销售中能看到所有流程数据，如果不在，则只能看到自己创建的数据
        //请求参数转为实体
        DiPreSale diPreSale = preSaleConverUtil.converToAddDiPreSale(request);
        diPreSale.setPreSaleCode(sequenceService.getSequenceNo(SequenceEnum.DYD_SQ.getCode()));
        diPreSale.setPackageType(request.getPackageType());
        diPreSale.setPackageTypeConfig(request.getPackageType());
        diPreSale.setTraceId("");
        DiMarketingNiche diMarketingNiche = null;
        if (StringUtils.isNotEmpty(request.getNicheCode())) {
            diMarketingNiche = iDiMarketingNicheService.selectDiMarketingNicheByNo(request.getNicheCode());
            if (Objects.nonNull(diMarketingNiche)) {
                if (StringUtils.isEmpty(request.getPreSaleName())) {
                    diPreSale.setPreSaleName(sequenceService.getPreSaleSequenceNo(diMarketingNiche.getProjectName()));
                }
                diPreSale.setNicheId(Integer.parseInt(diMarketingNiche.getId()));
                diPreSale.setNicheOwer(diMarketingNiche.getCreateBy());
                if (Objects.isNull(request.getOrderType())) {
                    diPreSale.setOrderType("5");
                }
            }
            /*DiMarketingNiche niche = new DiMarketingNiche();
            niche.setId(diMarketingNiche.getId());
            niche.setSubmitProposalDate(new Date());
            iDiMarketingNicheService.updateById(niche);*/
        }

        diPreSale.setCreateBy(SecurityUtils.getUsername());
        diPreSale.setPreSaleStatus(PreSaleStatusEnum.DEMAND_AND_VERIFICATION.getCode());
        diPreSale.setVersion(1);
        diPreSale.setDeliveryAddressType(1);

        diPreSale.setProvinceCode(request.getProvinceCode());
        diPreSale.setCityCode(request.getCityCode());
        diPreSale.setAreaCode(request.getAreaCode());
        diPreSale.setCountryCode(request.getCountryCode());
        Long materialId = null;
        Integer preSaleType = request.getPreSaleType();
        if (CollectionUtils.isNotEmpty(request.getPreSaleManifests()) && Objects.nonNull(request.getPreSaleManifests().get(0).getMaterielId())) {
            materialId = request.getPreSaleManifests().get(0).getMaterielId();
        }
        if (Objects.nonNull(materialId) && Objects.isNull(preSaleType)) {
            DiMateriel diMateriels = iDiMaterielService.selectDiMaterielById(materialId);
            if (Objects.nonNull(diMateriels)) {
                if (diMateriels.getIsStandard() == 1) {
                    preSaleType = 1;
                }
            }
            if (preSaleType == null) {
                preSaleType = 2;
            }
        }
        //物料号
        if (PreSaleTypeEnum.TRADE.getCode().equals(request.getPreSaleType())) {
            createTradePreSale(diPreSale, request.getPreSaleManifests(), request.getLabelParams(), request.getPackageType());
        } else if (PreSaleTypeEnum.BIG_NON_STANDARD.getCode().equals(request.getPreSaleType())) {
            createNoStandardPreSale(PreSaleTypeEnum.BIG_NON_STANDARD.getCode(), diPreSale, request.getPreSaleManifests(), request.getLabelParams(), request.getNum(), request.getPackageType(),
                    request.getDiPreSaleCustomized(),
                    request.getDiPreSaleCustomizedPlc(),
                    request.getDiPreSaleCustomizedPiping()
                    , request.getDiPreSaleCustomizedHeader()
                    , request.getDiPreSaleCustomizedFan()
                    , request.getDiPreSaleCustomizedBurner(), request.getDiPreSaleCustomizedAir());
        } else if (PreSaleTypeEnum.STANDARD.getCode().equals(preSaleType)) {
            createStandardPreSale(diPreSale, request.getPreSaleManifests(), request.getLabelParams(), request.getNum(), request.getPackageType());
        } else if (PreSaleTypeEnum.NON_STANDARD.getCode().equals(preSaleType)) {
            createNoStandardPreSale(PreSaleTypeEnum.NON_STANDARD.getCode(), diPreSale, request.getPreSaleManifests(), request.getLabelParams(), request.getNum(), request.getPackageType(),
                    request.getDiPreSaleCustomized(),
                    request.getDiPreSaleCustomizedPlc(),
                    request.getDiPreSaleCustomizedPiping()
                    , request.getDiPreSaleCustomizedHeader()
                    , request.getDiPreSaleCustomizedFan()
                    , request.getDiPreSaleCustomizedBurner(), request.getDiPreSaleCustomizedAir());
        } else {
            Assert.isTrue(false, "产品方案类型异常");
        }

        //判断商机状态，不等于1，证明还未方案报价，将商机状态改为方案报价并发送钉钉消息
        /*if (null != diMarketingNiche && StringUtils.isNotEmpty(diPreSale.getNicheCode()) && !"1".equals(diMarketingNiche.getNicheStatus())) {
            iDiMarketingNicheService.updateNicheStatus(diPreSale.getNicheCode(), "1");
            //发送钉钉消息
            String nickName = "";
            if (SecurityUtils.getLoginUser() != null && SecurityUtils.getLoginUser().getSysUser() != null) {
                nickName = SecurityUtils.getLoginUser().getSysUser().getNickName();
            }
            String content = StrUtil.format(NicheDingTalkEnum.NICHE_STATE_PLAN_OFFER.getMessage(), nickName);
            iDiMarketingNicheService.updateNicheStatusSendDingTalk(diMarketingNiche, content);
        }*/

        //现场工作面交接表
        if (CollectionUtils.isNotEmpty(request.getSceneUrls())) {

            List<DiPreSaleUrl> diPreSaleUrls = request.getSceneUrls().stream().map(fileKey -> {
                DiPreSaleUrl diPreSaleUrl = new DiPreSaleUrl();
                diPreSaleUrl.setDiPreSaleId(diPreSale.getId());
                diPreSaleUrl.setType("1");
                diPreSaleUrl.setFileKey(fileKey);
                diPreSaleUrl.setDelFlag(0);
                return diPreSaleUrl;
            }).collect(Collectors.toList());

            diPreSaleUrlMapper.insertBatchSomeColumn(diPreSaleUrls);
        }

        //售前方案附件
        if (CollectionUtils.isNotEmpty(request.getPreSaleUrls())) {

            List<DiPreSaleUrl> diPreSaleUrlList = request.getPreSaleUrls().stream().map(fileKey -> {
                DiPreSaleUrl diPreSaleUrl = new DiPreSaleUrl();
                diPreSaleUrl.setDiPreSaleId(diPreSale.getId());
                diPreSaleUrl.setType("2");
                diPreSaleUrl.setFileKey(fileKey);
                diPreSaleUrl.setDelFlag(0);
                return diPreSaleUrl;
            }).collect(Collectors.toList());
            diPreSaleUrlMapper.insertBatchSomeColumn(diPreSaleUrlList);
        }

        //查询商机
        if (diProjectRelationService.count(Wrappers.lambdaQuery(DiProjectRelation.class).eq(DiProjectRelation::getRelationNo, request.getNicheCode()).eq(DiProjectRelation::getRelationType, RelationTypeEnum.BUSINESS.getRelationType())) == 0) {
            try {
                ProcessProjectAddVO processProjectAddVO = new ProcessProjectAddVO();
                processProjectAddVO.setSource(1);
                //todo fix 根据产品选型来确定
                processProjectAddVO.setProductType(0L);
                processProjectAddVO.setNicheNo(request.getNicheCode());
                if (Objects.nonNull(diMarketingNiche)) {
                    processProjectAddVO.setSalesDeptId(NumberUtils.toLong(diMarketingNiche.getNicheOwnerDept()));
                    processProjectAddVO.setSaleUserId(diMarketingNiche.getNicheOwner());
                    processProjectAddVO.setProjectName(diMarketingNiche.getProjectName());
                }
                if (Objects.nonNull(request.getDiPreSaleCustomized())) {
                    processProjectAddVO.setTechnicalSupportUserId(request.getDiPreSaleCustomized().getTechSupportOwnerCode());
                }
                iDiProcessProjectService.addProcessProject(processProjectAddVO);
                AfterCommitExecutor.submit(() ->
                                checkProcessProjectStatusJob.check(processProjectAddVO.getNewProjectNo()), null);
            } catch (Exception e) {
                log.error("立项失败{}", e);
            }
        }else {
            AfterCommitExecutor.submit(() ->
                            EventBusUtils.publishEvent(TechnicalSupportUserChangeEvent.builder().nicheCode(diPreSale.getNicheCode()).build()),
                    null);
        }
        try {

            if (StringUtils.isNotEmpty(diPreSale.getProjectCode())) {
                //发送消息
                MqRelationBody mqRelationBody = new MqRelationBody();
                mqRelationBody.setProjectNo(diPreSale.getProjectCode());
                mqRelationBody.setRelationType(RelationTypeEnum.PRE_SALES_SOLUTION.getRelationType());
                mqRelationBody.setRelationNo(diPreSale.getPreSaleCode());
                mqRelationBody.setStatus("0");
                mqSendMsg.sendMsg(BeanUtil.beanToMap(mqRelationBody), "relation");
            }
        } catch (Exception e) {
            log.error("售前方案发送关联消息error{}", e.getMessage());
        }

        try {
            if (CollectionUtils.isNotEmpty(request.getPreSaleManifests()) || StringUtils.isNotEmpty(request.getRemark()) || CollectionUtils.isNotEmpty(request.getPreSaleUrls())) {
                if (StringUtils.isNotEmpty(diPreSale.getProjectCode())) {
                    checkSendMsg(diPreSale);
                }
            }
        } catch (Exception e) {
            log.error("发送进度error", e);
        }
        diPreSaleFeeService.updatePreSaleFee(diPreSale);
        return diPreSale.getId().toString();
    }

    /**
     * 创建贸易类产品方案
     *
     * @param diPreSale
     * @param manifests
     * @param labelParams
     * @param packageType
     */
    public void createTradePreSale(DiPreSale diPreSale, List<PreSaleAddRequest.PreSaleManifest> manifests,
                                    List<PreSaleAddRequest.LabelParam> labelParams, String packageType) {
        diPreSale.setPreSaleType(PreSaleTypeEnum.TRADE.getCode());
        List<Long> materialIds = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(manifests) && manifests.size() > 0) {
            materialIds = manifests.stream().map(PreSaleAddRequest.PreSaleManifest::getMaterielId).toList();
        }
        //Assert.notEmpty(materialIds, "贸易类产品方案物料不能为空");
        Optional.ofNullable(packageType).ifPresent(s -> diPreSale.setPackageType(s));
        /*log.info("查询物料信息:{}", JSON.toJSONString(materialIds));
        List<DiMateriel> diMateriels = iDiMaterielService.queryMaterielByIds(materialIds);
        log.info("查询物料信息，返回{}", JSON.toJSONString(diMateriels));*/

        //为空新增
        if(Objects.isNull(diPreSale.getId())) {
            diPreSaleMapper.insert(diPreSale);
        }
        List<BigDecimal> guideDurations = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(manifests) && manifests.size() > 0) {
            List<DiPreSaleManifest> diPreSaleManifest = preSaleConverUtil.converListToPreManifest(manifests);
            if (diPreSaleManifest.stream().filter(manifest -> Objects.isNull(manifest.getUseNum())).count() > 0) {
                throw new ServiceException("需求套数不能为空");
            }
            log.info("保存物料清单{}",JSON.toJSONString(diPreSaleManifest));
            savePreSaleManifest(diPreSale.getId(), diPreSaleManifest, labelParams);
//            BigDecimal costFeeTotal = diPreSaleManifest.parallelStream()
//                    .map(this::getFee)
//                    .map(PreSaleDetailResponse.Fee::getFeeTotal)
//                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal costFeeTotal = BigDecimal.ZERO;
            for (DiPreSaleManifest manifest : diPreSaleManifest) {
                PreSaleDetailResponse.Fee fee = getFee(manifest);
                costFeeTotal = costFeeTotal.add(fee.getFeeTotal());
                guideDurations.add(fee.getGuideDuration());
            }
            BigDecimal guideDuration = guideDurations.stream()
                    .filter(Objects::nonNull)
                    .max(Comparator.naturalOrder()).orElse(BigDecimal.ZERO);
            diPreSale.setGuideDuration(guideDuration);
            diPreSale.setCostFeeTotal(costFeeTotal);
            diPreSaleMapper.updateById(diPreSale);
        }
    }

    /**
     * 创建标准品方案
     */
    private void createStandardPreSale(DiPreSale diPreSale, List<PreSaleAddRequest.PreSaleManifest> manifests,
                                       List<PreSaleAddRequest.LabelParam> labelParams, Integer useNum, String packageType) {
        Assert.notNull(useNum, "需求套数不能为空！");
        diPreSale.setPreSaleType(PreSaleTypeEnum.STANDARD.getCode());
        Long materialId = null;
        if (CollectionUtils.isNotEmpty(manifests) && Objects.nonNull(manifests.get(0).getMaterielId())) {
            materialId = manifests.get(0).getMaterielId();
        }
        Assert.notNull(materialId, "标准品方案物料不能为空");
        Optional.ofNullable(packageType).ifPresent(s -> diPreSale.setPackageType(s));
        log.info("查询物料信息:{}", materialId);
        DiMateriel diMateriels = iDiMaterielService.selectDiMaterielById(materialId);
        log.info("查询物料信息,物料号{}，返回{}", materialId, JSON.toJSONString(diMateriels));
        BigDecimal packageTypePrice = computePackageTypeFee(packageType, useNum, diMateriels);
        diPreSale.setPackageType(String.valueOf(packageTypePrice));
        diPreSale.setPackageTypeConfig(packageType);

        //费用信息
        diPreSaleMapper.insert(diPreSale);

        if (CollectionUtils.isNotEmpty(manifests) && manifests.size() > 0 &&
                (Objects.nonNull(manifests.get(0).getMaterielId()) || StringUtils.isNotEmpty(manifests.get(0).getProductName()))) {
            List<DiPreSaleManifest> diPreSaleManifest = preSaleConverUtil.converListToPreManifest(manifests);
            diPreSaleManifest.get(0).setBomDesignDay(DEFAULT_BOM_DESIGN_DAY);
            savePreSaleManifest(diPreSale.getId(), diPreSaleManifest, labelParams);
            PreSaleDetailResponse.Fee fee = getFee(diPreSaleManifest.get(0));
            diPreSale.setCostFeeTotal(fee.getFeeTotal());
            diPreSale.setGuideDuration(fee.computeGuideDurationForProject());
            diPreSaleMapper.updateById(diPreSale);
        }
    }

    /**
     * 创建非标准品方案
     *
     * @param diPreSale
     * @param manifests
     * @param labelParams
     * @param useNum
     * @param packageType
     * @param customized
     * @param diPreSaleCustomizedPlc
     * @param diPreSaleCustomizedPiping
     * @param customizedHeader
     * @param customizedFan
     * @param customizedBurner
     * @param customizedAir
     */
    private void createNoStandardPreSale(Integer preSaleType, DiPreSale diPreSale, List<PreSaleAddRequest.PreSaleManifest> manifests,
                                         List<PreSaleAddRequest.LabelParam> labelParams, Integer useNum, String packageType,
                                         DiPreSaleCustomized customized, DiPreSaleCustomizedPlc diPreSaleCustomizedPlc,
                                         DiPreSaleCustomizedPiping diPreSaleCustomizedPiping, DiPreSaleCustomizedHeader customizedHeader,
                                         DiPreSaleCustomizedFan customizedFan, DiPreSaleCustomizedBurner customizedBurner, DiPreSaleCustomizedAir customizedAir) {
        Assert.notNull(useNum, "需求套数不能为空！");
        diPreSale.setPreSaleType(preSaleType);
        Long materialId = null;
        if (CollectionUtils.isNotEmpty(manifests) && Objects.nonNull(manifests.get(0).getMaterielId())) {
            materialId = manifests.get(0).getMaterielId();
        }
        if (Objects.nonNull(materialId)) {
            Optional.ofNullable(packageType).ifPresent(s -> diPreSale.setPackageType(s));
            log.info("查询物料信息:{}", materialId);
            DiMateriel diMateriels = iDiMaterielService.selectDiMaterielById(materialId);
            log.info("查询物料信息,物料号{}，返回{}", materialId, JSON.toJSONString(diMateriels));
            BigDecimal packageTypePrice = computePackageTypeFee(packageType, useNum, diMateriels);
            diPreSale.setPackageType(String.valueOf(packageTypePrice));
            diPreSale.setPackageTypeConfig(packageType);

        }

        diPreSaleMapper.insert(diPreSale);
        //新增非标信息
        Long customizedId = null;
        if (Objects.nonNull(customized)) {
            customized.setPreSaleId(diPreSale.getId());
            customized.setPreSaleCode(diPreSale.getPreSaleCode());
            diPreSaleCustomizedMapper.insert(customized);
            customizedId = customized.getId();
            if (null != diPreSaleCustomizedPlc) {
                diPreSaleCustomizedPlc.setCustomizedId(customizedId);
                diPreSaleCustomizedPlcMapper.insert(diPreSaleCustomizedPlc);
            }
            if (null != diPreSaleCustomizedPiping) {
                diPreSaleCustomizedPiping.setCustomizedId(customizedId);
                diPreSaleCustomizedPipingMapper.insert(diPreSaleCustomizedPiping);
            }
            if (null != customizedHeader) {
                customizedHeader.setCustomizedId(customizedId);
                diPreSaleCustomizedHeaderMapper.insert(customizedHeader);
            }
            if (null != customizedFan) {
                customizedFan.setCustomizedId(customizedId);
                diPreSaleCustomizedFanMapper.insert(customizedFan);
            }
            if (null != customizedBurner) {
                customizedBurner.setCustomizedId(customizedId);
                diPreSaleCustomizedBurnerMapper.insert(customizedBurner);
            }
            if (null != customizedAir) {
                customizedAir.setCustomizedId(customizedId);
                diPreSaleCustomizedAirMapper.insert(customizedAir);
            }
        }

        //方案清单
        if (CollectionUtils.isNotEmpty(manifests) && manifests.size() > 0 &&
                (Objects.nonNull(manifests.get(0).getMaterielId()) || StringUtils.isNotEmpty(manifests.get(0).getProductName()))) {
            List<DiPreSaleManifest> diPreSaleManifest = preSaleConverUtil.converListToPreManifest(manifests);
            if (PreSaleTypeEnum.NON_STANDARD.getCode().equals(Integer.valueOf(preSaleType))) {
                diPreSaleManifest.get(0).setBomDesignDay(DEFAULT_BOM_DESIGN_DAY);
            }
            savePreSaleManifest(diPreSale.getId(), diPreSaleManifest, labelParams);
            PreSaleDetailResponse.Fee fee = getFee(diPreSaleManifest.get(0));
            diPreSale.setCostFeeTotal(fee.getFeeTotal());
            diPreSale.setGuideDuration(fee.computeGuideDurationForProject());
            diPreSaleMapper.updateById(diPreSale);
        }
        if (Objects.nonNull(customized) && StringUtils.isNotBlank(customized.getTechSupportOwnerCode())) {
            this.technicalSupport(diPreSale.getId(), customized.getTechSupportOwnerCode());
        }

    }

    /**
     * 发送新增待办任务消息
     *
     * @param memberList 接收代办人员
     * @param preSale    产品方案信息
     */
    private void saveAgencyTaskSendMessage(List<String> memberList, DiPreSale preSale, Integer type) {
        if (CollectionUtils.isEmpty(memberList) || Objects.isNull(preSale)) {
            return;
        }
        AgencyTaskInfoDTO agencyTaskInfoDto = new AgencyTaskInfoDTO();
        agencyTaskInfoDto.setBusinessKey(preSale.getPreSaleCode());
        agencyTaskInfoDto.setJumpKey(preSale.getId().toString());
        agencyTaskInfoDto.setProjectNo(getProjectNo(preSale.getNicheCode()));
        agencyTaskInfoDto.setTaskTypeEnum(AgencyTaskTypeEnum.PRE_SALE);
        agencyTaskInfoDto.setTaskName(preSale.getPreSaleName());
        //获取状态中文描述
        SysDictData dictData = new SysDictData();
        dictData.setDictType("pre_sale_status");
        dictData.setDictValue(preSale.getPreSaleStatus());
        String clueStatusDesc = remoteDictDataService.selectDictLabel(dictData);
        agencyTaskInfoDto.setTaskStateDesc(clueStatusDesc);
        agencyTaskInfoDto.setLiabilityByList(memberList);
        agencyTaskInfoDto.setType(type.toString());
        String topic = AgencyConstants.AGENCY_TASK_TOPIC + AgencyConstants.DELIMITER_COLON + AgencyConstants.AGENCY_TASK_TAG;
        rocketMQTemplate.syncSend(topic, agencyTaskInfoDto);
        log.info("DiMarketingClueServiceImpl---updateAgencyTaskSendMessage()---新增待办任务消息发送成功");
    }

    /**
     * 申请技术支持发送钉钉消息
     *
     * @param preSale
     */
    private void sendTechnicalDingTalkMessage(DiPreSale preSale, String technicalOwnerCode) {
        DiMarketingNiche niche = iDiMarketingNicheService.lambdaQuery().eq(DiMarketingNiche::getId, preSale.getNicheId()).one();
        Assert.notNull(niche, "商机不能为空");
        DiMarketingContacts diMarketingContacts = new DiMarketingContacts();
        diMarketingContacts.setBusinessId(niche.getNicheNo());
        List<DiMarketingContacts> diMarketingContactsList = iDiMarketingContactsService.selectDiMarketingContactsList(diMarketingContacts);
        List<String> members = Lists.newArrayList(technicalOwnerCode);
        if (Objects.nonNull(diMarketingContactsList)) {
            members.addAll(diMarketingContactsList.stream().filter(item -> StringUtils.isNotEmpty(item.getContactsOwner())).map(DiMarketingContacts::getContactsOwner).collect(Collectors.toList()));
        }
        if (!members.contains(niche.getNicheOwner())) {
            members.add(niche.getNicheOwner());
        }
        SysUser sysUser = new SysUser();
        sysUser.setUserName(technicalOwnerCode);
        List<SysUser> userListByCondition = remoteUserService.findUserListByCondition(sysUser);
        String technicalOwnerName = Optional.ofNullable(userListByCondition).filter(list -> !list.isEmpty()).map(list -> list.get(0).getNickName()).orElse("");
        members.stream().forEach(member -> {
            DiMessageList diMessageList = new DiMessageList();
            diMessageList.setBusinessModule("产品方案");
            diMessageList.setTitle(String.format("产品方案ID：%s", preSale.getPreSaleCode()));
            diMessageList.setSendingTime(new Date());
            diMessageList.setRemarks(diMessageList.getTitle());
            diMessageList.setContent(String.format("商机ID:%s 所属产品方案ID:%s,技术支持负责人已变更为%s,操作人:%s。", preSale.getNicheCode(), preSale.getPreSaleCode(), technicalOwnerName, SecurityUtils.getLoginUser().getSysUser().getNickName()));
            diMessageList.setSendingUser(member);
            diMessageListService.insertDiMessageList(diMessageList);
        });
    }

    /**
     * 发送钉钉消息
     *
     * @param preSale
     */
    private void sendLockPreSaleDingTalkMessage(DiPreSale preSale) {
        DiMarketingNiche niche = iDiMarketingNicheService.lambdaQuery().eq(DiMarketingNiche::getId, preSale.getNicheId()).one();
        Assert.notNull(niche, "商机不能为空");
        DiMarketingContacts diMarketingContacts = new DiMarketingContacts();
        diMarketingContacts.setBusinessId(niche.getNicheNo());
        List<DiMarketingContacts> diMarketingContactsList = iDiMarketingContactsService.selectDiMarketingContactsList(diMarketingContacts);
        List<String> shareSales = Lists.newArrayList();
        if (Objects.nonNull(diMarketingContactsList)) {
            shareSales = diMarketingContactsList.stream().filter(item -> StringUtils.isNotEmpty(item.getContactsOwner())).map(DiMarketingContacts::getContactsOwner).collect(Collectors.toList());
        }
        if (!shareSales.contains(niche.getNicheOwner())) {
            shareSales.add(niche.getNicheOwner());
        }
        shareSales.stream().forEach(user -> {
            DiMessageList diMessageList = new DiMessageList();
            diMessageList.setBusinessModule("产品方案");
            diMessageList.setTitle(String.format("产品方案ID：%s", preSale.getPreSaleCode()));
            diMessageList.setSendingTime(new Date());
            diMessageList.setRemarks(diMessageList.getTitle());
            diMessageList.setContent(String.format("商机ID:%s 所属产品方案ID:%s,技术支持已锁定方案。", preSale.getNicheCode(), preSale.getPreSaleCode()));
            diMessageList.setSendingUser(user);
            diMessageListService.insertDiMessageList(diMessageList);
        });


    }

    public void checkSendMsg(DiPreSale diPreSale) {
        //校验方案清单是否满足完成条件
        //售前方案，有一条售前方案配置清单内至少有一条数据，且每条数据满足下述条件视为完成
        //1,有效期晚于当前日期
        //2,费用合计，设计费用，物料费用，生产费用，实施费用，包装费用，研发周期，供应周期，生产周期，实施周期均为非空
        boolean flag = true;
        /*
        for(PreSaleAddRequest.PreSaleManifest preSaleManifest:preSaleManifests){
            if(!flag){
                break;
            }

            if(Objects.isNull(preSaleManifest.getExpirationDateEnd()) || preSaleManifest.getExpirationDateEnd().isBefore(LocalDate.now())){
                flag = false;
                break;
            }

            if(Objects.isNull(preSaleManifest.getFeeTotal()) || Objects.isNull(preSaleManifest.getDeviseFee())
                    || Objects.isNull(preSaleManifest.getMaterialFee())
                    || Objects.isNull(preSaleManifest.getProduceFee()) || Objects.isNull(preSaleManifest.getImplementFee())
                    || Objects.isNull(preSaleManifest.getPackageFee()) || Objects.isNull(preSaleManifest.getRdDay())
                    || Objects.isNull(preSaleManifest.getSupplyDay()) || Objects.isNull(preSaleManifest.getProduceDay())
                    || Objects.isNull(preSaleManifest.getImplementDay())){
                flag = false;
                break;
            }
        }*/

        //满足条件，发送消息
        MqProcessBody mqProcessBody = new MqProcessBody();
        mqProcessBody.setProjectNo(diPreSale.getProjectCode());
        mqProcessBody.setPlannedSpeedType(PlannedSpeedEnum.PRE_SALE.getPlanned());
        mqProcessBody.setProcess(new BigDecimal("100"));
        log.info("售前方案进度{}", JSON.toJSONString(mqProcessBody));
        mqSendMsg.sendMsg(BeanUtil.beanToMap(mqProcessBody), "process");
    }

    /**
     * 更新发送消息
     *
     * @param diPreSale
     */
    public void checkUpdateSendMsg(DiPreSale diPreSale) {
        //校验方案清单是否满足完成条件
        //售前方案，有一条售前方案配置清单内至少有一条数据，且每条数据满足下述条件视为完成
        //1,有效期晚于当前日期
        //2,费用合计，设计费用，物料费用，生产费用，实施费用，包装费用，研发周期，供应周期，生产周期，实施周期均为非空
        /*boolean flag = true;
        for(PreSaleUpdateRequest.PreSaleManifest preSaleManifest:preSaleManifests){
            if(!flag){
                break;
            }

            if(Objects.isNull(preSaleManifest.getExpirationDateEnd()) || preSaleManifest.getExpirationDateEnd().isBefore(LocalDate.now())){
                flag = false;
                break;
            }

            if(Objects.isNull(preSaleManifest.getFeeTotal()) || Objects.isNull(preSaleManifest.getDeviseFee())
                    || Objects.isNull(preSaleManifest.getMaterialFee())
                    || Objects.isNull(preSaleManifest.getProduceFee()) || Objects.isNull(preSaleManifest.getImplementFee())
                    || Objects.isNull(preSaleManifest.getPackageFee()) || Objects.isNull(preSaleManifest.getRdDay())
                    || Objects.isNull(preSaleManifest.getSupplyDay()) || Objects.isNull(preSaleManifest.getProduceDay())
                    || Objects.isNull(preSaleManifest.getImplementDay())){
                flag = false;
                break;
            }
        }

        //满足条件，发送消息
        if(flag){

        }*/
        MqProcessBody mqProcessBody = new MqProcessBody();
        mqProcessBody.setProjectNo(diPreSale.getProjectCode());
        mqProcessBody.setPlannedSpeedType(PlannedSpeedEnum.PRE_SALE.getPlanned());
        mqProcessBody.setProcess(new BigDecimal("100"));
        mqSendMsg.sendMsg(BeanUtil.beanToMap(mqProcessBody), "process");
    }

    /**
     * 修改售前方案
     *
     * @param request 售前方案
     * @return 结果
     */
    @Transactional
    @Override
    public void updateDiPreSale(PreSaleUpdateRequest request) {
        //售前方案修改
        DiPreSale diPreSale = preSaleConverUtil.converToUpdatePreSale(request);

        DiPreSale newDiPreSale = diPreSaleMapper.selectOne(Wrappers.<DiPreSale>lambdaQuery().eq(DiPreSale::getPreSaleCode, diPreSale.getPreSaleCode()).orderByDesc(DiPreSale::getId).last("limit 1"));
        diPreSale.setId(newDiPreSale.getId());
        diPreSale.setPackageCatatory(String.valueOf(request.getPackageType()));
        diPreSale.setPackageType(String.valueOf(request.getPackageType()));
        diPreSale.setPackageTypeConfig(String.valueOf(request.getPackageType()));
        //物料
        List<DiPreSaleManifest> diPreSaleManifests = diPreSaleManifestMapper.selectList(Wrappers.<DiPreSaleManifest>lambdaQuery().eq(DiPreSaleManifest::getDiPreSaleId, diPreSale.getId()).eq(DiPreSaleManifest::getDelFlag, 0));
        if (PreSaleTypeEnum.TRADE.getCode().equals(newDiPreSale.getPreSaleType()) && Objects.nonNull(request.getManifests())) {
            iDiPreSaleManifestService.remove(Wrappers.<DiPreSaleManifest>lambdaQuery()
                    .eq(DiPreSaleManifest::getDiPreSaleId, diPreSale.getId()));
            //费用信息
            savePreSaleManifest(diPreSale.getId(), request.getManifests(), null);
            //重新获取物料清单

        } else if (CollectionUtils.isNotEmpty(diPreSaleManifests)) {
            DiMateriel diMateriel = new DiMateriel();
            //diMateriel.setMaterielNo(diPreSaleManifests.get(0).getMaterialCode());
            diMateriel.setId(diPreSaleManifests.get(0).getMaterialVersion());
            List<DiMateriel> diMateriels = iDiMaterielService.selectDiMaterielList(diMateriel);
            log.info("查询物料信息,物料号{}，返回{}", diPreSaleManifests.get(0).getMaterialCode(), JSON.toJSONString(diMateriels));
            if (CollectionUtils.isNotEmpty(diMateriels)) {
                DiMateriel diMaterielT = diMateriels.get(0);
                diPreSale.setPackageType(String.valueOf(computePackageTypeFee(String.valueOf(request.getPackageType()), request.getNum(), diMaterielT)));
                diPreSale.setPackageTypeConfig(String.valueOf(request.getPackageType()));
                PreSaleDetailResponse.Fee fee = getFee(diPreSaleManifests.get(0));
                diPreSale.setCostFeeTotal(fee.getFeeTotal());
                diPreSale.setGuideDuration(fee.computeGuideDurationForProject());
            }
        }

        diPreSale.setProvinceCode(request.getProvinceCode());
        diPreSale.setCityCode(request.getCityCode());
        diPreSale.setAreaCode(request.getAreaCode());
        diPreSale.setCountryCode(request.getCountryCode());
        diPreSale.setTechnicalReviewFlag(newDiPreSale.isTechnicalReviewFlag());
        diPreSale.setUpdateTime(LocalDateTime.now());
        diPreSale.setUpdateBy(SecurityUtils.getUsername());
        diPreSaleMapper.updateById(diPreSale);

        DiPreSale curDiPreSale = diPreSaleMapper.selectById(diPreSale.getId());


        //更新非标信息 删除非标需求再新增非标需求
        if (PreSaleTypeEnum.nonStandard(curDiPreSale.getPreSaleType())) {
            if (request.getDiPreSaleCustomized() != null) {
                DiPreSaleCustomized diPreSaleCustomized = diPreSaleCustomizedMapper.selectOne(Wrappers.<DiPreSaleCustomized>lambdaQuery().eq(DiPreSaleCustomized::getId, request.getDiPreSaleCustomized().getId()));
                if (Objects.nonNull(diPreSaleCustomized)) {
                    diPreSaleCustomized.setCustomizedAir(request.getDiPreSaleCustomized().getCustomizedAir());
                    diPreSaleCustomized.setCustomizedBurner(request.getDiPreSaleCustomized().getCustomizedBurner());
                    diPreSaleCustomized.setCustomizedFan(request.getDiPreSaleCustomized().getCustomizedFan());
                    diPreSaleCustomized.setCustomizedHeader(request.getDiPreSaleCustomized().getCustomizedHeader());
                    diPreSaleCustomized.setCustomizedPiping(request.getDiPreSaleCustomized().getCustomizedPiping());
                    diPreSaleCustomized.setCustomizedPlc(request.getDiPreSaleCustomized().getCustomizedPlc());
                    diPreSaleCustomizedMapper.updateById(diPreSaleCustomized);
                }

                Long customizedId = request.getDiPreSaleCustomized().getId();
                diPreSaleCustomizedPlcMapper.delete(Wrappers.<DiPreSaleCustomizedPlc>lambdaUpdate().eq(DiPreSaleCustomizedPlc::getCustomizedId, customizedId));
                diPreSaleCustomizedPipingMapper.delete(Wrappers.<DiPreSaleCustomizedPiping>lambdaUpdate().eq(DiPreSaleCustomizedPiping::getCustomizedId, customizedId));
                diPreSaleCustomizedHeaderMapper.delete(Wrappers.<DiPreSaleCustomizedHeader>lambdaUpdate().eq(DiPreSaleCustomizedHeader::getCustomizedId, customizedId));
                diPreSaleCustomizedFanMapper.delete(Wrappers.<DiPreSaleCustomizedFan>lambdaUpdate().eq(DiPreSaleCustomizedFan::getCustomizedId, customizedId));
                diPreSaleCustomizedBurnerMapper.delete(Wrappers.<DiPreSaleCustomizedBurner>lambdaUpdate().eq(DiPreSaleCustomizedBurner::getCustomizedId, customizedId));
                diPreSaleCustomizedAirMapper.delete(Wrappers.<DiPreSaleCustomizedAir>lambdaUpdate().eq(DiPreSaleCustomizedAir::getCustomizedId, customizedId));

                if (null != request.getDiPreSaleCustomizedPlc()) {
                    request.getDiPreSaleCustomizedPlc().setCustomizedId(customizedId);
                    request.getDiPreSaleCustomizedPlc().setId(null);
                    diPreSaleCustomizedPlcMapper.insert(request.getDiPreSaleCustomizedPlc());
                }
                if (null != request.getDiPreSaleCustomizedPiping()) {
                    request.getDiPreSaleCustomizedPiping().setCustomizedId(customizedId);
                    request.getDiPreSaleCustomizedPiping().setId(null);
                    diPreSaleCustomizedPipingMapper.insert(request.getDiPreSaleCustomizedPiping());
                }
                if (null != request.getDiPreSaleCustomizedHeader()) {
                    request.getDiPreSaleCustomizedHeader().setCustomizedId(customizedId);
                    request.getDiPreSaleCustomizedHeader().setId(null);
                    diPreSaleCustomizedHeaderMapper.insert(request.getDiPreSaleCustomizedHeader());
                }
                if (null != request.getDiPreSaleCustomizedFan()) {
                    request.getDiPreSaleCustomizedFan().setCustomizedId(customizedId);
                    request.getDiPreSaleCustomizedFan().setId(null);
                    diPreSaleCustomizedFanMapper.insert(request.getDiPreSaleCustomizedFan());
                }
                if (null != request.getDiPreSaleCustomizedBurner()) {
                    request.getDiPreSaleCustomizedBurner().setCustomizedId(customizedId);
                    request.getDiPreSaleCustomizedBurner().setId(null);
                    diPreSaleCustomizedBurnerMapper.insert(request.getDiPreSaleCustomizedBurner());
                }
                if (null != request.getDiPreSaleCustomizedAir()) {
                    request.getDiPreSaleCustomizedAir().setCustomizedId(customizedId);
                    request.getDiPreSaleCustomizedAir().setId(null);
                    diPreSaleCustomizedAirMapper.insert(request.getDiPreSaleCustomizedAir());
                }
            }
        }

        //删除售前方案附件
        diPreSaleUrlMapper.delete(Wrappers.
                <DiPreSaleUrl>lambdaUpdate().
                eq(DiPreSaleUrl::getDiPreSaleId, diPreSale.getId()).
                eq(DiPreSaleUrl::getType, "1"));
        //现场工作面交接表
        if (CollectionUtils.isNotEmpty(request.getSceneUrls())) {
            List<DiPreSaleUrl> diPreSaleUrls = request.getSceneUrls().stream().map(fileKey -> {
                DiPreSaleUrl diPreSaleUrl = new DiPreSaleUrl();
                diPreSaleUrl.setDiPreSaleId(diPreSale.getId());
                diPreSaleUrl.setType("1");
                diPreSaleUrl.setFileKey(fileKey);
                diPreSaleUrl.setDelFlag(0);
                return diPreSaleUrl;
            }).collect(Collectors.toList());
            diPreSaleUrlMapper.insert(diPreSaleUrls);
        }

        if (request.getHasPicture() != null && request.getHasPicture()) {
            PreSaleSavePictureRequest savePictureRequest = new PreSaleSavePictureRequest();
            BeanUtils.copyProperties(request, savePictureRequest);
            savePictureRequest.setId(diPreSale.getId());
            this.savePicture(savePictureRequest);
        }
        diPreSaleFeeService.updatePreSaleFee(curDiPreSale);
        preSaleHistoryService.saveDiPreSaleHistory(curDiPreSale, PreSalePhaseEnum.PRE_SALE.getCode());
    }

//    private void appendGuideDurationsTrade(DiPreSale diPreSale, List<DiPreSaleManifest> diPreSaleManifests) {
//        List<BigDecimal> guideDurations = Lists.newArrayList();
//        BigDecimal costFeeTotal = BigDecimal.ZERO;
//        for (DiPreSaleManifest manifest : diPreSaleManifests) {
//            PreSaleDetailResponse.Fee fee = getFee(manifest);
//            costFeeTotal = costFeeTotal.add(fee.getFeeTotal());
//            guideDurations.add(fee.getGuideDuration());
//        }
//        BigDecimal guideDuration = guideDurations.stream().filter(Objects::nonNull)
//                .max(Comparator.naturalOrder()).orElse(BigDecimal.ZERO);
//        diPreSale.setGuideDuration(guideDuration);
//        diPreSale.setCostFeeTotal(costFeeTotal);
//    }

    private void uploadSendDingTalk(DiPreSale diPreSale, AtomicBoolean isUploadAppearance, AtomicBoolean isUploadFabrication,
                                    AtomicBoolean isUploadElectricDesign, AtomicBoolean isUploadThreeDDesign) {
        //获取发送人
        List<DiOrder> orderList = diOrderService.list(new LambdaQueryWrapper<DiOrder>()
                .eq(DiOrder::getNicheNo, diPreSale.getNicheCode())
                .eq(DiOrder::getDelFlag, 0));
        if (CollectionUtils.isEmpty(orderList)) {
            log.info("DiPreSaleServiceImpl---uploadSendDingTalk()---订单未生成---产品方案编号：{}，商机编号：{}", diPreSale.getPreSaleCode(), diPreSale.getNicheCode());
            return;
        }
        if (orderList.size() > 1) {
            log.info("DiPreSaleServiceImpl---uploadSendDingTalk()---订单数量不正确---产品方案编号：{}，商机编号：{}", diPreSale.getPreSaleCode(), diPreSale.getNicheCode());
            return;
        }
        DiOrder order = orderList.get(0);
        if (StringUtils.isBlank(order.getProduceUserId())) {
            log.info("DiPreSaleServiceImpl---uploadSendDingTalk()---订单未选择生产负责人---产品方案编号：{}，商机编号：{}，订单编号：{}", diPreSale.getPreSaleCode(), diPreSale.getNicheCode(), order.getOrderNo());
            return;
        }
        List<String> sendUserList = new ArrayList<>(List.of(order.getProduceUserId().split(",")));
        List<DiOrderProduce> orderProduceList = diOrderProduceService.list(new LambdaQueryWrapper<DiOrderProduce>()
                .eq(DiOrderProduce::getPreSaleCode, diPreSale.getPreSaleCode())
                .eq(DiOrderProduce::getOrderNo, order.getOrderNo())
                .eq(DiOrderProduce::getDelFlag, 0));
        if (CollectionUtils.isNotEmpty(orderProduceList)) {
            if (orderProduceList.size() == 1) {
                if (StringUtils.isNotBlank(orderProduceList.get(0).getProduceUserId())) {
                    sendUserList.addAll(List.of(orderProduceList.get(0).getProduceUserId().split(",")));
                } else {
                    log.info("DiPreSaleServiceImpl---updateDiPreSale()---订单生产数据中生产负责人为空---订单生产数据主键ID：{}", orderProduceList.get(0).getId());
                }
            } else {
                log.info("DiPreSaleServiceImpl---updateDiPreSale()---订单生成的订单生产数据不正确---产品方案编号：{}，订单编号：{}", diPreSale.getPreSaleCode(), order.getOrderNo());
            }
        } else {
            log.info("DiPreSaleServiceImpl---updateDiPreSale()---订单未生成订单生产数据---产品方案编号：{}，订单编号：{}", diPreSale.getPreSaleCode(), order.getOrderNo());
        }
        if (isUploadAppearance.get()) {
            String title = StrUtil.format(DingTalkEnum.UPLOAD_APPEARANCE_NOTICE.getTitle(), diPreSale.getPreSaleCode());
            String content = StrUtil.format(DingTalkEnum.UPLOAD_APPEARANCE_NOTICE.getMessage(), order.getProjectNo(), order.getOrderNo(), diPreSale.getPreSaleCode(), SecurityUtils.getLoginUser().getSysUser().getNickName());
            uploadSendNotice(title, content, sendUserList.stream().distinct().collect(Collectors.toList()));
        }
        if (isUploadFabrication.get()) {
            String title = StrUtil.format(DingTalkEnum.UPLOAD_FABRICATION_NOTICE.getTitle(), diPreSale.getPreSaleCode());
            String content = StrUtil.format(DingTalkEnum.UPLOAD_FABRICATION_NOTICE.getMessage(), order.getProjectNo(), order.getOrderNo(), diPreSale.getPreSaleCode(), SecurityUtils.getLoginUser().getSysUser().getNickName());
            uploadSendNotice(title, content, sendUserList.stream().distinct().collect(Collectors.toList()));
        }
        if (isUploadElectricDesign.get()) {
            String title = StrUtil.format(DingTalkEnum.UPLOAD_ELECTRIC_DESIGN_NOTICE.getTitle(), diPreSale.getPreSaleCode());
            String content = StrUtil.format(DingTalkEnum.UPLOAD_ELECTRIC_DESIGN_NOTICE.getMessage(), order.getProjectNo(), order.getOrderNo(), diPreSale.getPreSaleCode(), SecurityUtils.getLoginUser().getSysUser().getNickName());
            uploadSendNotice(title, content, sendUserList.stream().distinct().collect(Collectors.toList()));
        }
        if (isUploadThreeDDesign.get()) {
            String title = StrUtil.format(DingTalkEnum.UPLOAD_THREE_D_DESIGN_NOTICE.getTitle(), diPreSale.getPreSaleCode());
            String content = StrUtil.format(DingTalkEnum.UPLOAD_THREE_D_DESIGN_NOTICE.getMessage(), order.getProjectNo(), order.getOrderNo(), diPreSale.getPreSaleCode(), SecurityUtils.getLoginUser().getSysUser().getNickName());
            uploadSendNotice(title, content, sendUserList.stream().distinct().collect(Collectors.toList()));
        }
    }

    private void uploadSendNotice(String title, String content, List<String> sendUserList) {
        DiMessageList diMessageList = new DiMessageList();
        diMessageList.setBusinessModule("产品方案");
        diMessageList.setTitle(title);
        diMessageList.setRemarks(diMessageList.getTitle());
        diMessageList.setContent(content);
        diMessageList.setSendingTime(new Date());
        sendUserList.forEach(item -> {
            diMessageList.setSendingUser(item);
            diMessageListService.insertDiMessageList(diMessageList);
        });
    }


    /**
     * 验证是否有方案清单
     *
     * @param manifests
     * @return
     */
    private boolean validPreSaleManifest(List<DiPreSaleManifest> manifests) {
        if (CollectionUtils.isEmpty(manifests)) {
            throw new ServiceException("请联系技术支持，先更新物料bom再编辑图纸信息");
        }
        return true;
    }

    @Override
    public void labelEdit(LabelEdit labelEdit) {
        DiPreSaleLabel diPreSaleLabel = new DiPreSaleLabel();
        diPreSaleLabel.setId(labelEdit.getId());
        diPreSaleLabel.setLabelParamValue(labelEdit.getLabelParamValue());
        diPreSaleLabel.setLabelParamValueMax(labelEdit.getLabelParamValueMax());
        diPreSaleLabel.setLabelParamValueMin(labelEdit.getLabelParamValueMin());
        diPreSaleLabelMapper.updateById(diPreSaleLabel);
    }

/**
 * 更改方案状态
 *
 * @param nicheCode
 * @return
 */
//    @Transactional
//    @Override
//    public R updateStatus(String nicheCode) {
//
//        List<DiPreSale> diPreSales = diPreSaleMapper.selectList(Wrappers.<DiPreSale>lambdaQuery().eq(DiPreSale::getNicheCode, nicheCode));
//        if (CollectionUtils.isNotEmpty(diPreSales)) {
//            DiPreSale diPreSale = new DiPreSale();
//            diPreSale.setPreSaleStatus("4");
//            diPreSaleMapper.update(diPreSale, Wrappers.<DiPreSale>lambdaUpdate().in(DiPreSale::getId, diPreSales.stream().map(DiPreSale::getId).collect(Collectors.toList())));
//        }
//        return R.ok();
//    }

    /**
     * 批量删除售前方案
     *
     * @param ids 需要删除的售前方案主键
     * @return 结果
     */
    @Override
    public int deleteDiPreSaleByIds(Long[] ids) {
        return diPreSaleMapper.deleteDiPreSaleByIds(ids);
    }

    /**
     * 删除售前方案信息
     *
     * @param id 售前方案主键
     * @return 结果
     */
    @Override
    public int deleteDiPreSaleById(Long id) {
        return diPreSaleMapper.deleteDiPreSaleById(id);
    }

    /**
     * 获取方案清单中对应价格
     *
     * @param diPreSaleManifest
     * @return
     */
    @Override
    public PreSaleManifestFee queryPreSaleManifestFee(DiPreSaleManifest diPreSaleManifest) {
        PreSaleManifestFee manifestFee = new PreSaleManifestFee();
        PreSaleDetailResponse.Fee fee = getFee(diPreSaleManifest);
        BeanUtil.copyProperties(fee, manifestFee);
        manifestFee.setGuideDuration(fee.computeGuideDurationForProject());
        BigDecimal manifestFeeTotal = Objects.nonNull(manifestFee.getFeeTotal()) ? manifestFee.getFeeTotal() : BigDecimal.ZERO;
        manifestFee.setTotalCostFee(manifestFeeTotal.multiply(new BigDecimal(manifestFee.getQuantity())));

        return manifestFee;
    }

    /**
     * code转id
     *
     * @param code
     * @return
     */
    @Override
    public String convertCode2Id(String code) {
        DiPreSale preSale = this.queryDiPreSaleByCode(code);
        if (preSale != null) {
            return preSale.getId().toString();
        }
        return null;
    }

    /**
     * 保存图片
     *
     * @param request
     */
    @Override
    public void savePicture(PreSaleSavePictureRequest request) {
        DiPreSale diPreSale = diPreSaleMapper.selectById(request.getId());

//        //历史上传方案附件
//        List<DiPreSaleUrl> urlList = diPreSaleUrlMapper.selectList(Wrappers.<DiPreSaleUrl>lambdaUpdate()
//                .eq(DiPreSaleUrl::getDiPreSaleId, request.getId())
//                .in(DiPreSaleUrl::getType, Arrays.asList(
//                        MaterielFileTypeEnum.PID_DESIGN.getFileType(),
//                        MaterielFileTypeEnum.APPEARANCE.getFileType(),
//                        MaterielFileTypeEnum.FABRICATION.getFileType(),
//                        MaterielFileTypeEnum.TECHNICAL_PROTOCOL.getFileType(),
//                        MaterielFileTypeEnum.ELECTRIC_DESIGN.getFileType(),
//                        MaterielFileTypeEnum.THREE_D_DESIGN.getFileType()
//                )));

        //删除售前方案附件
        diPreSaleUrlMapper.delete(Wrappers.<DiPreSaleUrl>lambdaUpdate().eq(DiPreSaleUrl::getDiPreSaleId, request.getId())
                .in(DiPreSaleUrl::getType, Arrays.asList(
                        MaterielFileTypeEnum.PID_DESIGN.getFileType(),
                        MaterielFileTypeEnum.APPEARANCE.getFileType(),
                        MaterielFileTypeEnum.FABRICATION.getFileType(),
                        MaterielFileTypeEnum.TECHNICAL_PROTOCOL.getFileType(),
                        MaterielFileTypeEnum.ELECTRIC_DESIGN.getFileType(),
                        MaterielFileTypeEnum.THREE_D_DESIGN.getFileType()
                )));

        List<DiPreSaleManifest> diPreSaleManifests = diPreSaleManifestMapper.selectList(Wrappers.<DiPreSaleManifest>lambdaQuery().eq(DiPreSaleManifest::getDiPreSaleId, request.getId()).eq(DiPreSaleManifest::getDelFlag, 0));

        //机械外观图
        AtomicBoolean isUploadAppearance = new AtomicBoolean(false);

        //机械生产图
        AtomicBoolean isUploadFabrication = new AtomicBoolean(false);

        //电气图
        AtomicBoolean isUploadElectricDesign = new AtomicBoolean(false);

        //三维图纸
        AtomicBoolean isUploadThreeDDesign = new AtomicBoolean(false);


        /**
         * pid
         */
        if (Objects.nonNull(request.getPidFiles()) && validPreSaleManifest(diPreSaleManifests)) {
            List<DiPreSaleUrl> pidFiles = request.getPidFiles().stream().map(fileVo -> {
                DiPreSaleUrl diPreSaleUrl = new DiPreSaleUrl();
                diPreSaleUrl.setDiPreSaleId(diPreSale.getId());
                diPreSaleUrl.setType(MaterielFileTypeEnum.PID_DESIGN.getFileType());
                diPreSaleUrl.setFileKey(fileVo.getFileKey());
                diPreSaleUrl.setFileName(fileVo.getFileName());
                diPreSaleUrl.setFileUrl(fileVo.getFileUrl());
                diPreSaleUrl.setIsPic(fileVo.getIsPic());
                diPreSaleUrl.setDelFlag(0);
                return diPreSaleUrl;
            }).collect(Collectors.toList());
            diPreSaleUrlMapper.insert(pidFiles);
            //updatePreSaleMaterielPic(diPreSaleManifests.get(0).getMaterialVersion(), diPreSaleManifests.get(0).getMaterialCode(), MaterielFileTypeEnum.PID_DESIGN.getFileType(), request.getPidFiles());
        }

        /**
         * 机械外观图
         */
        if (Objects.nonNull(request.getMechineShapeFiles()) && validPreSaleManifest(diPreSaleManifests)) {
            List<DiPreSaleUrl> mechineShapeFiles = request.getMechineShapeFiles().stream().map(fileVo -> {
                if (null == fileVo.getId()) {
                    isUploadAppearance.set(true);
                }
                DiPreSaleUrl diPreSaleUrl = new DiPreSaleUrl();
                diPreSaleUrl.setDiPreSaleId(diPreSale.getId());
                diPreSaleUrl.setType(MaterielFileTypeEnum.APPEARANCE.getFileType());
                diPreSaleUrl.setFileKey(fileVo.getFileKey());
                diPreSaleUrl.setFileName(fileVo.getFileName());
                diPreSaleUrl.setFileUrl(fileVo.getFileUrl());
                diPreSaleUrl.setIsPic(fileVo.getIsPic());
                diPreSaleUrl.setDelFlag(0);
                return diPreSaleUrl;
            }).collect(Collectors.toList());
            diPreSaleUrlMapper.insert(mechineShapeFiles);
            //updatePreSaleMaterielPic(diPreSaleManifests.get(0).getMaterialVersion(), diPreSaleManifests.get(0).getMaterialCode(), MaterielFileTypeEnum.APPEARANCE.getFileType(), request.getMechineShapeFiles());
        }

        /**
         * 机械生产图
         */
        if (Objects.nonNull(request.getMechineProductFiles()) && validPreSaleManifest(diPreSaleManifests)) {
            List<DiPreSaleUrl> mechineProductFiles = request.getMechineProductFiles().stream().map(fileVo -> {
                if (null == fileVo.getId()) {
                    isUploadFabrication.set(true);
                }
                DiPreSaleUrl diPreSaleUrl = new DiPreSaleUrl();
                diPreSaleUrl.setDiPreSaleId(diPreSale.getId());
                diPreSaleUrl.setType(MaterielFileTypeEnum.FABRICATION.getFileType());
                diPreSaleUrl.setFileKey(fileVo.getFileKey());
                diPreSaleUrl.setFileName(fileVo.getFileName());
                diPreSaleUrl.setFileUrl(fileVo.getFileUrl());
                diPreSaleUrl.setIsPic(fileVo.getIsPic());
                diPreSaleUrl.setDelFlag(0);
                return diPreSaleUrl;
            }).collect(Collectors.toList());


            diPreSaleUrlMapper.insert(mechineProductFiles);
            //updatePreSaleMaterielPic(diPreSaleManifests.get(0).getMaterialVersion(), diPreSaleManifests.get(0).getMaterialCode(), MaterielFileTypeEnum.FABRICATION.getFileType(), request.getMechineProductFiles());
        }

        /**
         * 技术协议
         */
        if (Objects.nonNull(request.getTechFiles()) && validPreSaleManifest(diPreSaleManifests)) {
            List<DiPreSaleUrl> techFiles = request.getTechFiles().stream().map(fileVo -> {
                DiPreSaleUrl diPreSaleUrl = new DiPreSaleUrl();
                diPreSaleUrl.setDiPreSaleId(diPreSale.getId());
                diPreSaleUrl.setType(MaterielFileTypeEnum.TECHNICAL_PROTOCOL.getFileType());
                diPreSaleUrl.setFileKey(fileVo.getFileKey());
                diPreSaleUrl.setFileName(fileVo.getFileName());
                diPreSaleUrl.setFileUrl(fileVo.getFileUrl());
                diPreSaleUrl.setIsPic(fileVo.getIsPic());
                diPreSaleUrl.setDelFlag(0);
                return diPreSaleUrl;
            }).collect(Collectors.toList());

            diPreSaleUrlMapper.insert(techFiles);
            //updatePreSaleMaterielPic(diPreSaleManifests.get(0).getMaterialVersion(), diPreSaleManifests.get(0).getMaterialCode(), MaterielFileTypeEnum.TECHNICAL_PROTOCOL.getFileType(), request.getTechFiles());
        }

        /**
         * 电气图
         */
        if (Objects.nonNull(request.getElectricFiles()) && validPreSaleManifest(diPreSaleManifests)) {
            List<DiPreSaleUrl> electricFiles = request.getElectricFiles().stream().map(fileVo -> {
                if (null == fileVo.getId()) {
                    isUploadElectricDesign.set(true);
                }
                DiPreSaleUrl diPreSaleUrl = new DiPreSaleUrl();
                diPreSaleUrl.setDiPreSaleId(diPreSale.getId());
                diPreSaleUrl.setType(MaterielFileTypeEnum.ELECTRIC_DESIGN.getFileType());
                diPreSaleUrl.setFileKey(fileVo.getFileKey());
                diPreSaleUrl.setFileName(fileVo.getFileName());
                diPreSaleUrl.setFileUrl(fileVo.getFileUrl());
                diPreSaleUrl.setIsPic(fileVo.getIsPic());
                diPreSaleUrl.setDelFlag(0);
                return diPreSaleUrl;
            }).collect(Collectors.toList());

            diPreSaleUrlMapper.insert(electricFiles);
            //updatePreSaleMaterielPic(diPreSaleManifests.get(0).getMaterialVersion(), diPreSaleManifests.get(0).getMaterialCode(), MaterielFileTypeEnum.ELECTRIC_DESIGN.getFileType(), request.getElectricFiles());
        }

        /**
         * 三维图纸
         */
        if (Objects.nonNull(request.getThreeDDesignList()) && validPreSaleManifest(diPreSaleManifests)) {
            List<DiPreSaleUrl> threeDDesignList = request.getThreeDDesignList().stream().map(fileVo -> {
                if (null == fileVo.getId()) {
                    isUploadThreeDDesign.set(true);
                }
                DiPreSaleUrl diPreSaleUrl = new DiPreSaleUrl();
                diPreSaleUrl.setDiPreSaleId(diPreSale.getId());
                diPreSaleUrl.setType(MaterielFileTypeEnum.THREE_D_DESIGN.getFileType());
                diPreSaleUrl.setFileKey(fileVo.getFileKey());
                diPreSaleUrl.setFileName(fileVo.getFileName());
                diPreSaleUrl.setFileUrl(fileVo.getFileUrl());
                diPreSaleUrl.setIsPic(fileVo.getIsPic());
                diPreSaleUrl.setDelFlag(0);
                return diPreSaleUrl;
            }).collect(Collectors.toList());

            diPreSaleUrlMapper.insert(threeDDesignList);
            //updatePreSaleMaterielPic(diPreSaleManifests.get(0).getMaterialVersion(), diPreSaleManifests.get(0).getMaterialCode(), MaterielFileTypeEnum.THREE_D_DESIGN.getFileType(), request.getThreeDDesignList());
        }

        if (isUploadAppearance.get() || isUploadFabrication.get() || isUploadElectricDesign.get() || isUploadThreeDDesign.get()) {
            uploadSendDingTalk(diPreSale, isUploadAppearance, isUploadFabrication, isUploadElectricDesign, isUploadThreeDDesign);
        }
    }

    @Override
    public CalMaterlalPriceResponse calMaterlalPrice(CalMaterlalPriceRequest request) {
        Assert.notNull(request.getMaterialId(), "物料编号不能为空");
        Assert.notNull(request.getUseNum(), "用量不能为空");
        //Assert.notNull(request.getPackageType(), "包装方式不能为空");
        if (request.getPackageType() == null && request.getPreSaleId() != null) {
            DiPreSale preSale = iDiPreSaleService.queryDiPreSaleById(request.getPreSaleId());
            request.setPackageType(preSale.getPackageTypeConfig());
        }

        CalMaterlalPriceResponse response = new CalMaterlalPriceResponse();
        List<MaterielChecklistNewDTO> materielChecklistNewDTOS = iDiMaterielService.queryMaterielPlanCheckListByVersion(request.getMaterialId());
        List<MaterielChecklistNewDTO> hasBomTreeList = iMultiVersionMaterielService.parentNodesWithGrandchildren(request.getMaterialId());
        //Map<Long, DiMaterielPrice> priceMap = materielPriceService.queryByMateriel(Arrays.asList(request.getMaterialId())).stream().collect(Collectors.toMap(DiMaterielPrice::getMaterielId, x -> x));
        MaterielFeeQueryDTO materielFeeQueryDTO = new MaterielFeeQueryDTO();
        materielFeeQueryDTO.setMaterielId(request.getMaterialId());
        MaterielFeeDTO materielFeeDTO = iDiMaterielService.queryMaterielFee(materielFeeQueryDTO);
        DiMateriel diMateriels = diMaterielService.selectDiMaterielById(request.getMaterialId());
        BigDecimal packageTypePrice = computePackageTypeFee(request.getPackageType(), request.getUseNum(), diMateriels);
        packageTypePrice = packageTypePrice.multiply(new BigDecimal(request.getUseNum()));
        BigDecimal materialProductionCost = new BigDecimal(0L);
        if (CollectionUtils.isNotEmpty(hasBomTreeList)) {
            for (MaterielChecklistNewDTO itemCheckList : hasBomTreeList) {
                DiMaterielPrice itemMaterielPrice = materielPriceService.lambdaQuery().eq(DiMaterielPrice::getMaterielId, itemCheckList.getMaterielId()).one();
                BigDecimal materialProductionCosts = Objects.nonNull(itemMaterielPrice) && Objects.nonNull(itemMaterielPrice.getGuideProductionCosts())
                        ? itemMaterielPrice.getGuideProductionCosts() : BigDecimal.ZERO;
                materialProductionCost = materialProductionCost.add(materialProductionCosts.multiply(new BigDecimal(itemCheckList.getUseNum())));
            }
            materialProductionCost = materialProductionCost.multiply(new BigDecimal(request.getUseNum()));
            response.setGuideProductionCosts(materialProductionCost);
        }

        BigDecimal materialFee = new BigDecimal(0L);
        BigDecimal singleSuitMaterielFee = new BigDecimal(0L);
        int severalCnt = Objects.nonNull(request.getUseNum()) ? request.getUseNum() : BigDecimal.ONE.intValue();
        for (MaterielChecklistNewDTO itemCheckList : materielChecklistNewDTOS) {
            int useNum = itemCheckList.getUseNum();
            DiMaterielPrice itemMaterielPrice = materielPriceService.lambdaQuery().eq(DiMaterielPrice::getMaterielId, itemCheckList.getMaterielId()).one();
            BigDecimal materialCosts = Objects.nonNull(itemMaterielPrice) && Objects.nonNull(itemMaterielPrice.getGuideMaterialCosts())
                    ? itemMaterielPrice.getGuideMaterialCosts() : BigDecimal.ZERO;
            materialFee = materialFee.add(materialCosts.multiply(new BigDecimal(useNum)));
            singleSuitMaterielFee = singleSuitMaterielFee.add(materialCosts.multiply(new BigDecimal(useNum)));
        }
        materialFee = materialFee.multiply(new BigDecimal(severalCnt));

        response.setMaterialFee(materialFee);
        if (materielFeeDTO.getSafetyStock() == null || materielFeeDTO.getSafetyStock() == 0) {
            response.setGuideDuration(materielFeeDTO.getGuideDuration());
        } else {
            response.setGuideDuration(BigDecimal.ZERO);
        }
        response.setPackageFee(packageTypePrice);

        BigDecimal[] fees = new BigDecimal[]{
                Objects.nonNull(response.getMaterialFee()) ? response.getMaterialFee() : BigDecimal.ZERO,
                Objects.nonNull(response.getGuideProductionCosts()) ? response.getGuideProductionCosts() : BigDecimal.ZERO,
                Objects.nonNull(response.getPackageFee()) ? response.getPackageFee() : BigDecimal.ZERO,
                Objects.nonNull(materielFeeDTO.getGuideProduceCosts()) ? materielFeeDTO.getGuideProduceCosts() : BigDecimal.ZERO,
        };
        BigDecimal feeTotal = Arrays.stream(fees).reduce(BigDecimal.ZERO, BigDecimal::add);
        response.setFeeTotal(feeTotal);
        return response;
    }

    public boolean hasDuplicateMaterielId(List<DiPreSaleManifest> preSaleManifests) {
        HashSet<Long> materielIds = new HashSet<>();
        for (DiPreSaleManifest manifest : preSaleManifests) {
            if (!materielIds.add(manifest.getMaterialVersion())) {
                // 如果 add 返回 false，说明 materielId 已经存在
                return true;
            }
        }
        return false;
    }


    /**
     * 保存物料清单
     *
     * @param preSaleId
     * @param preSaleManifests
     * @param labelParams
     */
    @Override
    public void savePreSaleManifest(Long preSaleId, List<DiPreSaleManifest> preSaleManifests, List<PreSaleAddRequest.LabelParam> labelParams) {
        if (hasDuplicateMaterielId(preSaleManifests)) {
            throw new ServiceException("物料清单中不能包含重复物料");
        }
        DiPreSale preSale = iDiPreSaleService.queryDiPreSaleById(preSaleId);
        List<DiPreSaleManifest> manifestList = iDiPreSaleManifestService.lambdaQuery()
                .eq(DiPreSaleManifest::getDiPreSaleId, preSaleId)
                .eq(DiPreSaleManifest::getManifestVersion, preSale.getVersion())
                .orderByDesc(DiPreSaleManifest::getId).list();
        if (CollectionUtils.isNotEmpty(manifestList)) {
            iDiPreSaleManifestService.remove(Wrappers.<DiPreSaleManifest>lambdaQuery()
                    .in(DiPreSaleManifest::getId, manifestList.stream().map(DiPreSaleManifest::getId).toList()));
        }
        preSaleManifests.stream().map(preSaleManifest -> {
            List<DiPreSaleManifest> tmpManifestList = manifestList.stream().filter(manifest -> {
                return manifest.getDiPreSaleId().equals(preSaleId) && manifest.getManifestVersion().equals(preSaleManifest.getManifestVersion());
            }).toList();
            DiPreSaleManifest curManifest = tmpManifestList.size() > 0 ? tmpManifestList.get(0) : new DiPreSaleManifest();

            preSaleManifest.setDiPreSaleId(preSaleId);
            preSaleManifest.setId(null);
            preSaleManifest.setManifestVersion(Integer.valueOf(preSale.getVersion()));
            if (Objects.isNull(preSaleManifest.getMaterialVersion())) {
                throw new BizValidException("物料ID不能为空");
            }

            MaterielInfoVo materielInfoVo = new MaterielInfoVo();
            materielInfoVo.setId(preSaleManifest.getMaterialVersion());
            MaterielInfoDTO materielInfoDTO = iDiMaterielService.materielInfo(materielInfoVo);
            if (Objects.nonNull(materielInfoDTO)) {
                preSaleManifest.setMaterielLength(materielInfoDTO.getMaterielLength());
                preSaleManifest.setMaterielWidth(materielInfoDTO.getMaterielWidth());
                preSaleManifest.setMaterielHigh(materielInfoDTO.getMaterielHigh());
            }
            PreSaleDetailResponse.Fee manifestFee = getFee(preSaleManifest);
            Optional.ofNullable(manifestFee.getFeeTotal()).ifPresent(fee -> preSaleManifest.setFeeTotal(fee));
            Optional.ofNullable(manifestFee.getMaterialFee()).ifPresent(fee -> preSaleManifest.setMaterialFee(fee));
            //Optional.ofNullable(manifestFee.getProductFee()).ifPresent(fee -> preSaleManifest.setProduceFee(fee));
            //Optional.ofNullable(manifestFee.getDeliveryDebugFee()).ifPresent(fee -> preSaleManifest.setImplementFee(fee));
            Optional.ofNullable(manifestFee.getPackageFee()).ifPresent(fee -> preSaleManifest.setPackageFee(fee));
            Optional.ofNullable(manifestFee.getOtherFee()).ifPresent(fee -> preSaleManifest.setOtherFee(fee));
            Optional.ofNullable(manifestFee.getQuantity()).ifPresent(fee -> preSaleManifest.setQuantity(fee));
//            preSaleManifest.setRdDay(manifestFee.getGuideDuration());
            Optional.ofNullable(manifestFee.getSupplyDay()).ifPresent(fee -> preSaleManifest.setSupplyDay(fee.intValue()));
            Optional.ofNullable(manifestFee.getProductDay()).ifPresent(fee -> preSaleManifest.setProduceDay(fee.intValue()));
            //Optional.ofNullable(manifestFee.getDeliveryDebugDay()).ifPresent(fee -> preSaleManifest.setImplementDay(fee));
            Optional.ofNullable(curManifest.getImplementFee()).ifPresent(fee -> preSaleManifest.setImplementFee(fee));
            Optional.ofNullable(curManifest.getImplementDay()).ifPresent(day -> preSaleManifest.setImplementDay(day));
            Optional.ofNullable(curManifest.getUseNum()).ifPresent(num -> preSaleManifest.setUseNum(num));
            PreSaleTypeEnum type = PreSaleTypeEnum.of(preSale.getPreSaleType());
            if (type == PreSaleTypeEnum.NON_STANDARD) {
                preSaleManifest.setBomDesignDay(DEFAULT_BOM_DESIGN_DAY);
            } else {
                preSaleManifest.setBomDesignDay(BigDecimal.ZERO);
            }
            preSaleManifest.setRiskCost(curManifest.getRiskCost());
            preSaleManifest.setDelFlag(0);
            return preSaleManifest;
        }).collect(Collectors.toList());
        diPreSaleManifestMapper.insertBatchSomeColumn(preSaleManifests);


        //传入标签值
        Map<Long, List<PreSaleAddRequest.LabelParam>> labelParamMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(labelParams)) {
            labelParamMap = labelParams.stream().collect(Collectors.groupingBy(PreSaleAddRequest.LabelParam::getLabelClassificationId));
        }
        for (DiPreSaleManifest diPreSaleManifest : preSaleManifests) {
            //根据物料号查询物料信息
            MaterielInfoVo materielInfoVo = new MaterielInfoVo();
            materielInfoVo.setId(diPreSaleManifest.getMaterialVersion());
            materielInfoVo.setNeedBom(1);
            MaterielInfoDTO materielInfoDTO = iDiMaterielService.materielInfo(materielInfoVo);

            if (Objects.isNull(materielInfoDTO)) {
                throw new RuntimeException("物料查询为空" + diPreSaleManifest.getMaterialCode());
            }

            //查询标签
            List<DiPreSaleLabel> diPreSaleLabels = diPreSaleLabelMapper.selectList(Wrappers.<DiPreSaleLabel>lambdaQuery().eq(DiPreSaleLabel::getPreSaleManifestId, diPreSaleManifest.getId()));
            if (CollectionUtils.isNotEmpty(diPreSaleLabels)) {
                diPreSaleLabelMapper.delete(Wrappers.<DiPreSaleLabel>lambdaQuery().eq(DiPreSaleLabel::getPreSaleManifestId, diPreSaleManifest.getId()));
            }
            List<LabelMaterielWarehouseListDTO> labelMaterielWarehouseListDTOS = new ArrayList<>();
            getLable(materielInfoDTO.getId(), labelMaterielWarehouseListDTOS, 1);
            if (CollectionUtils.isNotEmpty(labelMaterielWarehouseListDTOS)) {

                //标签map，key为标签分类id
                Map<Long, List<LabelMaterielWarehouseListDTO>> labelMaterielWarehouseMap = labelMaterielWarehouseListDTOS.stream().map(labelMaterielWarehouseListDTO -> {
                    if (StringUtils.isEmpty(labelMaterielWarehouseListDTO.getWebClassificationName())) {
                        labelMaterielWarehouseListDTO.setWebClassificationName(labelMaterielWarehouseListDTO.getClassificationName());
                        return labelMaterielWarehouseListDTO;
                    }
                    return labelMaterielWarehouseListDTO;
                }).collect(Collectors.toList()).stream().filter(labelMaterielWarehouseListDTO -> labelMaterielWarehouseListDTO.getIsShowWeb() == 1).collect(Collectors.groupingBy(LabelMaterielWarehouseListDTO::getLabelClassificationId));

                for (Long key : labelMaterielWarehouseMap.keySet()) {
                    //标签
                    Map<Long, List<PreSaleAddRequest.LabelParam>> finalLabelParamMap = labelParamMap;
                    List<DiPreSaleLabel> preSaleLabels = labelMaterielWarehouseMap.get(key).stream().map(labelMaterielWarehouseListDTO -> {
                        DiPreSaleLabel label = new DiPreSaleLabel();
                        label.setPreSaleManifestId(diPreSaleManifest.getId());
                        label.setMaterielId(labelMaterielWarehouseListDTO.getMaterielId());
                        label.setLabelParamType(labelMaterielWarehouseListDTO.getLabelParamType());
                        label.setLabelParamTypeName(labelMaterielWarehouseListDTO.getLabelParamTypeName());
                        label.setLabelClassificationId(labelMaterielWarehouseListDTO.getLabelClassificationId());
                        label.setClassificationName(labelMaterielWarehouseListDTO.getWebClassificationName());
                        label.setLabelId(labelMaterielWarehouseListDTO.getLabelId());
                        label.setLabelName(labelMaterielWarehouseListDTO.getLabelName());
                        label.setLabelParamMax(labelMaterielWarehouseListDTO.getLabelParamMax());
                        label.setLabelParamMin(labelMaterielWarehouseListDTO.getLabelParamMin());
                        label.setLabelUnit(labelMaterielWarehouseListDTO.getLabelUnit());
                        label.setLabelParam(labelMaterielWarehouseListDTO.getLabelParam());
                        label.setLabelParamClassification(labelMaterielWarehouseListDTO.getLabelParamClassification());
                        label.setDelFlag(0);
                        //获取传入标签参数
                        List<PreSaleAddRequest.LabelParam> tmplabelParams = finalLabelParamMap.containsKey(labelMaterielWarehouseListDTO.getLabelClassificationId()) ? finalLabelParamMap.get(labelMaterielWarehouseListDTO.getLabelClassificationId()) : Lists.newArrayList();
                        if (CollectionUtils.isNotEmpty(tmplabelParams)) {
                            //传入参数标签值
                            PreSaleAddRequest.LabelParam labelParam = new PreSaleAddRequest.LabelParam();
                            label.setLabelParamValueType(labelMaterielWarehouseListDTO.getLabelParamType());
                            label.setLabelParamValue(labelMaterielWarehouseListDTO.getLabelParam());
                            label.setLabelParamValueMax(labelMaterielWarehouseListDTO.getLabelParamMax());
                            label.setLabelParamValueMin(labelMaterielWarehouseListDTO.getLabelParamMin());
                        }
                        return label;
                    }).collect(Collectors.toList());
                    diPreSaleLabelMapper.insertBatchSomeColumn(preSaleLabels);

                }
            }
        }
    }

    @Override
    public void updateNum(String preSaleId, Integer num) {
        LambdaUpdateChainWrapper<DiPreSale> updateWrapper = new LambdaUpdateChainWrapper<>(diPreSaleMapper);
        updateWrapper.eq(DiPreSale::getId, preSaleId);
        updateWrapper.set(DiPreSale::getNum, num);
        updateWrapper.update();

        DiPreSale preSale = diPreSaleMapper.selectById(preSaleId);
        diPreSaleFeeService.updatePreSaleFee(preSale);
        preSaleHistoryService.saveDiPreSaleHistory(preSale, PreSalePhaseEnum.PRE_SALE.getCode());
    }

    private List<DiPreSaleManifest> queryPreSaleManifestList(Long preSaleId) {
        return diPreSaleManifestMapper.selectList(Wrappers.<DiPreSaleManifest>lambdaQuery().eq(DiPreSaleManifest::getDiPreSaleId, preSaleId));
    }

    /**
     * 启动复核交期
     *
     * @param diPreSale
     */
    @Override
    public void startDeliveryTimeReview(DiPreSale diPreSale) {
        DiPreSale preSale = diPreSaleMapper.selectById(diPreSale.getId());
        if (Objects.isNull(preSale)) {
            throw new ServiceException("产品方案不存在");
        }
        LambdaUpdateChainWrapper<DiPreSale> updateWrapper = new LambdaUpdateChainWrapper<>(diPreSaleMapper);
        updateWrapper.eq(DiPreSale::getId, diPreSale.getId());
        updateWrapper.set(DiPreSale::getPreSaleStatus, PreSaleStatusEnum.DELIVERY_REVIEW.getCode());
        updateWrapper.set(DiPreSale::getDeliveryReviewFlag, PreSaleDeliveryFlagEnum.NEED.getCode());
        updateWrapper.set(DiPreSale::getDeliveryReviewSupplyFlag, PreSaleDeliveryTaskFlagEnum.PENDING.getCode());
        updateWrapper.set(DiPreSale::getDeliveryReviewDeliveryFlag, PreSaleDeliveryTaskFlagEnum.PENDING.getCode());
        if (PreSaleTypeEnum.STANDARD.getCode().equals(diPreSale.getPreSaleType()) || PreSaleTypeEnum.TRADE.getCode().equals(diPreSale.getPreSaleType())) {
            //标品无技术支持
            updateWrapper.set(DiPreSale::getDeliveryReviewTechSupportFlag, PreSaleDeliveryTaskFlagEnum.NO_NEED.getCode());
            if (PreSaleTypeEnum.TRADE.getCode().equals(diPreSale.getPreSaleType())) {
                //贸易类，跳过交付的交期复核
                updateWrapper.set(DiPreSale::getDeliveryReviewDeliveryFlag, PreSaleDeliveryTaskFlagEnum.NO_NEED.getCode());
            }
            startPurchaseReview(diPreSale, "startDeliveryTimeReview");
        } else {
            updateWrapper.set(DiPreSale::getDeliveryReviewTechSupportFlag, PreSaleDeliveryTaskFlagEnum.PENDING.getCode());
        }
        updateWrapper.update();
        //获取技术支持信息
        List<DiPreSaleCustomized> customizedList = diPreSaleCustomizedMapper.selectList(new LambdaQueryWrapper<DiPreSaleCustomized>().eq(DiPreSaleCustomized::getPreSaleId, preSale.getId()));
        if (CollectionUtil.isNotEmpty(customizedList)) {
            //发送代办和钉钉
            orderCommonService.saveAgencyTask(preSale.getId().toString(), null, preSale.getPreSaleCode(), getProjectNo(preSale.getNicheCode()), AgencyTaskTypeEnum.DELIVERY_TIME_CHECK,
                    StringUtils.isNotEmpty(preSale.getPreSaleName()) ? preSale.getPreSaleName() : preSale.getPreSaleCode(), DELIVERY_TIME_REVIEW.getDesc(),
                    Collections.singletonList(customizedList.get(0).getTechSupportOwnerCode()), null);
            String title = StrUtil.format(DingTalkEnum.DELIVERY_TIME_CHECK_NOTICE.getTitle(), preSale.getPreSaleCode());
            String content = StrUtil.format(DingTalkEnum.DELIVERY_TIME_CHECK_NOTICE.getMessage(), preSale.getNicheCode(), preSale.getPreSaleCode(), SecurityUtils.getLoginUser().getSysUser().getNickName());
            orderCommonService.sendDingTalkMessage("产品方案", title, content, null, customizedList.get(0).getTechSupportOwnerCode());
        }
    }

    private String getProjectNo(String nicheCode) {
        //获取项目编号
        List<DiProjectRelation> relationList = diProjectRelationService.list(new LambdaQueryWrapper<DiProjectRelation>()
                .eq(DiProjectRelation::getRelationType, RelationTypeEnum.BUSINESS)
                .eq(DiProjectRelation::getRelationNo, nicheCode));
        if (CollectionUtil.isNotEmpty(relationList)) {
            return relationList.get(0).getProjectNo();
        }
        return "";
    }

    /**
     * 交期复核
     *
     * @param request
     */
    @Override
    public void deliveryReview(DeliveryReviewRequest request) {
        /**
         *  1、先取方案的交期信息
         *  2、查询调整过的交期信息
         *  3、把初始值 替换成 调整后的值
         *  4、重新计算核算交期
         *  5、保存
         */
        //List<DiPreSaleManifest> manifestList = iDiPreSaleManifestService.queryByPreSaleId(request.getPreSaleId());
        //交期复核备注
        this.updateDeliveryReviewNote(request.getPreSaleId(), request.getDeliveryReviewNote());
        // 调整交期
        List<DiPreSaleDeliveryReview> reviewList = new ArrayList<>();
        Map<PreSaleDeliveryAdjustTypeEnum, DeliveryReviewDuration> map =
                request.getDuration().stream().collect(Collectors.toMap(x -> PreSaleDeliveryAdjustTypeEnum.of(x.getAdjustType()), x -> x, (a, b) -> a));

        List<DeliveryReviewDuration> durationList = new ArrayList<>();
        if (request.getStage() == 1) {
            durationList = map.entrySet().stream().filter(x -> x.getKey().getStage() == 1)
                    .map(x -> x.getValue()).toList();
        } else {
            durationList = map.values().stream().toList();
        }
        DeliveryReviewSummaryRequest summaryRequest = new DeliveryReviewSummaryRequest();
        summaryRequest.setPreSaleId(request.getPreSaleId());

        Map<PreSaleDeliveryAdjustTypeEnum, DeliveryReviewDuration> summaryMap = this.deliveryReviewSummary(summaryRequest).getDuration()
                .stream().collect(Collectors.toMap(x -> PreSaleDeliveryAdjustTypeEnum.of(x.getAdjustType()), x -> x, (a, b) -> a));

        for (DeliveryReviewDuration duration : durationList) {
            PreSaleDeliveryAdjustTypeEnum type = PreSaleDeliveryAdjustTypeEnum.of(duration.getAdjustType());
            if (type == null) {
                continue;
            }

            List<DiPreSaleDeliveryReview> preSaleDeliveryReviews = deliveryReviewService.listByPreSaleId(request.getPreSaleId());
            BigDecimal beFore = null;
            BigDecimal after = duration.getBefore().subtract(duration.getAdjust());
            if (CollectionUtil.isNotEmpty(preSaleDeliveryReviews)) {
                Optional<DiPreSaleDeliveryReview> lastDeliveryReview = preSaleDeliveryReviews.stream()
                        .filter(review -> type.getType().equals(review.getAdjustType()))
                        .sorted(Comparator.comparing(DiPreSaleDeliveryReview::getId).reversed())
                        .findFirst();
                if (lastDeliveryReview.isPresent()) {
                    beFore = lastDeliveryReview.get().getAdjust();
                }

            }

            DiPreSaleDeliveryReview review = new DiPreSaleDeliveryReview();
            review.setPreSaleId(Long.valueOf(request.getPreSaleId()));
            review.setAdjustType(type.getType());
            review.setAdjustBefore(beFore != null ? beFore : duration.getBefore());
            review.setAdjust(after);
            if (review.getAdjustBefore().equals(review.getAdjust())) {
                continue;
            }
            reviewList.add(review);
        }
        // 保存日志
        //deliveryReviewService.saveBatch(reviewList);
        for (DiPreSaleDeliveryReview review : reviewList) {
            deliveryReviewService.save(review);
        }
        // 重新计算核算交期
        BigDecimal supportDay = map.getOrDefault(PreSaleDeliveryAdjustTypeEnum.techSupport, new DeliveryReviewDuration()).getAfter();
        BigDecimal electricDay = map.getOrDefault(PreSaleDeliveryAdjustTypeEnum.electric, new DeliveryReviewDuration()).getAfter();
        BigDecimal mechineDay = map.getOrDefault(PreSaleDeliveryAdjustTypeEnum.machine, new DeliveryReviewDuration()).getAfter();
        BigDecimal supplyDay = map.getOrDefault(PreSaleDeliveryAdjustTypeEnum.supply, new DeliveryReviewDuration()).getAfter();
        BigDecimal productDay = map.getOrDefault(PreSaleDeliveryAdjustTypeEnum.product, new DeliveryReviewDuration()).getAfter();
        BigDecimal deliveryDebugDay = BigDecimal.ZERO;
        BigDecimal bomDesignDay = map.getOrDefault(PreSaleDeliveryAdjustTypeEnum.bomDesign, new DeliveryReviewDuration()).getAfter();
        BigDecimal days = DiHelper.computeGuideDurationForProject(supportDay, electricDay, mechineDay, supplyDay, productDay, deliveryDebugDay, bomDesignDay);

        //标记为已复核，// 退出待办
        DiPreSale preSale = iDiPreSaleService.queryDiPreSaleById(request.getPreSaleId());
        PreSaleTypeEnum saleTypeEnum = PreSaleTypeEnum.of(preSale.getPreSaleType());
        preSale.setFinallyDeliveryWeek(days.divide(new BigDecimal("7"), 0, RoundingMode.CEILING));
        iDiPreSaleService.updateById(preSale);
        PreSaleDeliveryStageEnum stageEnum = PreSaleDeliveryStageEnum.getEnumByCode(request.getStage());
        if (stageEnum == PreSaleDeliveryStageEnum.SUPPLY && request.getFinished()) {
            //采购复核完成
            preSale.setDeliveryReviewSupplyFlag(PreSaleDeliveryTaskFlagEnum.DONE.getCode());
            iDiPreSaleService.updateById(preSale);
            //删除采购复核待办任务
            AgencyTaskInfoDTO agencyTaskInfoDto = new AgencyTaskInfoDTO();
            agencyTaskInfoDto.setTaskTypeEnum(AgencyTaskTypeEnum.DELIVERY_TIME_CHECK_PURCHASE);
            agencyTaskInfoDto.setBusinessKey(request.getPreSaleId().toString());
            agencyTaskInfoDto.setJumpKey(request.getPreSaleId().toString());
            agencyTaskInfoDto.setType("2");
            String topic = AgencyConstants.AGENCY_TASK_TOPIC + AgencyConstants.DELIMITER_COLON + AgencyConstants.AGENCY_TASK_TAG;
            rocketMQTemplate.syncSend(topic, agencyTaskInfoDto);

            PreSaleDeliveryReviewSupplyDoneEvent event = new PreSaleDeliveryReviewSupplyDoneEvent();
            event.setPreSaleId(Long.valueOf(request.getPreSaleId()));
            EventBusUtils.publishEvent(event);
        }

        if (stageEnum == PreSaleDeliveryStageEnum.DELIVERY && request.getFinished()) {
            preSale.setDeliveryReviewDeliveryFlag(PreSaleDeliveryTaskFlagEnum.DONE.getCode());
            if (!preSale.getDeliveryReviewSupplyFlag().equals(PreSaleDeliveryTaskFlagEnum.PENDING.getCode())
                    && !preSale.getDeliveryReviewDeliveryFlag().equals(PreSaleDeliveryTaskFlagEnum.PENDING.getCode())
            ) {
                if (PreSaleTypeEnum.TRADE == saleTypeEnum || PreSaleTypeEnum.STANDARD == saleTypeEnum) {
                    preSale.setPreSaleStatus(PreSaleStatusEnum.BUSINESS_QUOTES.getCode());
                } else {
                    preSale.setPreSaleStatus(PreSaleStatusEnum.LOCKED.getCode());
                }
            }
            iDiPreSaleService.updateById(preSale);
            //删除交付复核待办任务
            AgencyTaskInfoDTO agencyTaskInfoDto = new AgencyTaskInfoDTO();
            agencyTaskInfoDto.setTaskTypeEnum(AgencyTaskTypeEnum.DELIVERY_TIME_CHECK_DELIVER);
            agencyTaskInfoDto.setBusinessKey(request.getPreSaleId().toString());
            agencyTaskInfoDto.setJumpKey(request.getPreSaleId().toString());
            agencyTaskInfoDto.setType("2");
            String topic = AgencyConstants.AGENCY_TASK_TOPIC + AgencyConstants.DELIMITER_COLON + AgencyConstants.AGENCY_TASK_TAG;
            rocketMQTemplate.syncSend(topic, agencyTaskInfoDto);

            PreSaleDeliveryReviewDoneEvent event = new PreSaleDeliveryReviewDoneEvent();
            event.setPreSaleId(Long.valueOf(request.getPreSaleId()));
            EventBusUtils.publishEvent(event);
        }

        //notifyPreSaleQuote(Long.valueOf(request.getPreSaleId()));
    }


    /**
     * 交期复核汇总
     *
     * @param request
     * @return
     */
    @Override
    public DeliveryReviewSummaryResponse deliveryReviewSummary(DeliveryReviewSummaryRequest request) {

        DiPreSale preSale = iDiPreSaleService.queryDiPreSaleById(request.getPreSaleId());
        //有标签 需要交期复核 且 状态不在 交期复核中， 采购状态处于 待复核 则 允许编辑
        boolean isSupplyStatus = PreSaleDeliveryFlagEnum.NEED.getCode().equals(preSale.getDeliveryReviewFlag())
                && PreSaleStatusEnum.DELIVERY_REVIEW.getCode().equals(preSale.getPreSaleStatus()) //处于技术支持复核
                && PreSaleDeliveryTaskFlagEnum.PENDING.getCode().equals(preSale.getDeliveryReviewSupplyFlag())
                && !PreSaleDeliveryTaskFlagEnum.PENDING.getCode().equals(preSale.getDeliveryReviewTechSupportFlag());
        ;
        boolean isSupplyUser = false;
        if (PreSaleTypeEnum.BIG_NON_STANDARD == PreSaleTypeEnum.of(preSale.getPreSaleType())) {
            DiPreSaleCustomized customized = iDiPreSaleService.queryCustomized(preSale.getId());
            isSupplyUser = request.getCurrentUserCode() != null && request.getCurrentUserCode().equals(customized.getTechSupportOwnerCode());
        } else {
            isSupplyUser = this.inRole(request.getCurrentUserCode(), purchaseManagerApproveRoleKey);
        }

        boolean isDeliveryStatus = PreSaleDeliveryFlagEnum.NEED.getCode().equals(preSale.getDeliveryReviewFlag())
                && PreSaleStatusEnum.DELIVERY_REVIEW.getCode().equals(preSale.getPreSaleStatus()) //处于技术支持复核
                && PreSaleDeliveryTaskFlagEnum.PENDING.getCode().equals(preSale.getDeliveryReviewDeliveryFlag())
                && !PreSaleDeliveryTaskFlagEnum.PENDING.getCode().equals(preSale.getDeliveryReviewSupplyFlag());

        boolean isDeliveryUser = this.inRole(request.getCurrentUserCode(), deliverManagerApproveRoleKey);

        List<DeliveryReviewDuration> list = new ArrayList<>();
        Map<PreSaleDeliveryAdjustTypeEnum, DiPreSaleDeliveryReview> old = deliveryReviewService.queryAdjustment(request.getPreSaleId());
        Map<PreSaleDeliveryAdjustTypeEnum, DiPreSaleDeliveryReview> init = this.queryInitAdjustment(request.getPreSaleId());
        for (PreSaleDeliveryAdjustTypeEnum item : PreSaleDeliveryAdjustTypeEnum.values()) {
            DiPreSaleDeliveryReview start = init.get(item);

            DeliveryReviewDuration duration = new DeliveryReviewDuration();
//            duration.setAdjust(adjust.getAdjust());
            duration.setBefore(start.getAdjustBefore()); // 这里始终显示原始值

            duration.setAdjustType(item.getCode());
            DiPreSaleDeliveryReview adjust = old.get(item);
            if (adjust == null) { // 没有调整过
                duration.setAdjust(BigDecimal.ZERO);
            } else {
                //有调整记录
                duration.setAdjust(duration.getBefore().subtract(adjust.getAdjust()));
            }

            if (item == PreSaleDeliveryAdjustTypeEnum.supply) {
                //交付阶段和采购阶段 都可以编辑
                if ((isSupplyStatus && isSupplyUser) || (isDeliveryStatus && isDeliveryUser)) {
                    duration.setCanEdit(true);
                } else {
                    duration.setCanEdit(false);
                }
            } else {
                //只有交付阶段可以编辑
                if (isDeliveryStatus && isDeliveryUser) {
                    duration.setCanEdit(true);
                } else {
                    duration.setCanEdit(false);
                }
            }
            list.add(duration);
        }
        DeliveryReviewSummaryResponse response = new DeliveryReviewSummaryResponse();
        response.setDuration(list);
        response.setDeliveryReviewNote(preSale.getDeliveryReviewNote());
        if (isSupplyStatus) {
            response.setStage(1);
        } else if (isDeliveryStatus) {
            response.setStage(2);
        }
        return response;
    }


    /**
     * 复核日志
     *
     * @param preSaleQuoteId
     * @return
     */
    @Override
    public List<DeliveryReviewLogResponse> deliveryReviewLog(Integer preSaleQuoteId) {
        List<DiPreSaleQuoteDetail> quoteDetailList = quoteService.queryPreSaleQuoteDetailList(preSaleQuoteId);
        List<DeliveryReviewLogResponse> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(quoteDetailList)) {
            return result;
        }
        List<Long> preSaleIdList = quoteDetailList.stream().map(DiPreSaleQuoteDetail::getPreSaleId).collect(Collectors.toList());
        Map<Long, DiPreSale> diPreSaleMap = listByIds(preSaleIdList).stream().collect(Collectors.toMap(DiPreSale::getId, Function.identity()));
        List<DiPreSaleDeliveryReview> list = deliveryReviewService.listByPreSaleIdList(preSaleIdList);
        List<DeliveryReviewLogResponse> data = preSaleConverUtil.toReviewResponse(list);
        if (CollectionUtils.isNotEmpty(data)) {
            data.forEach(x -> {
                if (diPreSaleMap.containsKey(x.getPreSaleId())) {
                    x.setPreSaleName(diPreSaleMap.get(x.getPreSaleId()).getPreSaleName());
                    x.setPreSaleCode(diPreSaleMap.get(x.getPreSaleId()).getPreSaleCode());
                }
            });
        }
        return data.stream().filter(x -> x.getAdjustBefore().compareTo(x.getAdjustAfter()) != 0

        ).toList();
    }


    private Map<PreSaleDeliveryAdjustTypeEnum, DiPreSaleDeliveryReview> queryInitAdjustment(Integer preSaleId) {

        List<DiPreSaleManifest> manifests = iDiPreSaleManifestService.queryByPreSaleId(Math.toIntExact(preSaleId));
        DiPreSaleManifest manifest = manifests.get(0);
        Map<PreSaleDeliveryAdjustTypeEnum, DiPreSaleDeliveryReview> map = new HashMap<>();
        DiPreSale preSale = this.queryDiPreSaleById(preSaleId);
        if (PreSaleTypeEnum.of(preSale.getPreSaleType()) == PreSaleTypeEnum.BIG_NON_STANDARD) {
            map.put(PreSaleDeliveryAdjustTypeEnum.supply, init(manifest.getRealSupplyDay(), PreSaleDeliveryAdjustTypeEnum.supply));
            map.put(PreSaleDeliveryAdjustTypeEnum.techSupport, init(manifest.getRealTechSupportDay(), PreSaleDeliveryAdjustTypeEnum.techSupport));
            map.put(PreSaleDeliveryAdjustTypeEnum.product, init(manifest.getRealProductDay(), PreSaleDeliveryAdjustTypeEnum.product));
            map.put(PreSaleDeliveryAdjustTypeEnum.electric, init(manifest.getRealElectricDay(), PreSaleDeliveryAdjustTypeEnum.electric));
            map.put(PreSaleDeliveryAdjustTypeEnum.machine, init(manifest.getRealMachineDay(), PreSaleDeliveryAdjustTypeEnum.machine));
            map.put(PreSaleDeliveryAdjustTypeEnum.bomDesign, init(manifest.getRealBomDesignDay() == null ? BigDecimal.ZERO : manifest.getRealBomDesignDay(), PreSaleDeliveryAdjustTypeEnum.bomDesign));
        } else if (PreSaleTypeEnum.of(preSale.getPreSaleType()) == PreSaleTypeEnum.TRADE) {
            Integer max = manifests.stream().filter(x -> x.getSupplyDay() != null).map(x -> x.getSupplyDay()).max(Integer::compare).orElse(0);
            map.put(PreSaleDeliveryAdjustTypeEnum.supply, init(max, PreSaleDeliveryAdjustTypeEnum.supply));
            map.put(PreSaleDeliveryAdjustTypeEnum.techSupport, init(BigDecimal.ZERO, PreSaleDeliveryAdjustTypeEnum.techSupport));
            map.put(PreSaleDeliveryAdjustTypeEnum.product, init(BigDecimal.ZERO, PreSaleDeliveryAdjustTypeEnum.product));
            map.put(PreSaleDeliveryAdjustTypeEnum.electric, init(BigDecimal.ZERO, PreSaleDeliveryAdjustTypeEnum.electric));
            map.put(PreSaleDeliveryAdjustTypeEnum.machine, init(BigDecimal.ZERO, PreSaleDeliveryAdjustTypeEnum.machine));
            map.put(PreSaleDeliveryAdjustTypeEnum.bomDesign, init(BigDecimal.ZERO, PreSaleDeliveryAdjustTypeEnum.bomDesign));
        } else {
            map.put(PreSaleDeliveryAdjustTypeEnum.supply, init(manifest.getSupplyDay(), PreSaleDeliveryAdjustTypeEnum.supply));
            map.put(PreSaleDeliveryAdjustTypeEnum.techSupport, init(manifest.getTechnicalSupportDuration(), PreSaleDeliveryAdjustTypeEnum.techSupport));
            map.put(PreSaleDeliveryAdjustTypeEnum.product, init(manifest.getProduceDay(), PreSaleDeliveryAdjustTypeEnum.product));
            map.put(PreSaleDeliveryAdjustTypeEnum.electric, init(manifest.getElectricalDesignPeriod(), PreSaleDeliveryAdjustTypeEnum.electric));
            map.put(PreSaleDeliveryAdjustTypeEnum.machine, init(manifest.getMechanicalDesignDuration(), PreSaleDeliveryAdjustTypeEnum.machine));
            map.put(PreSaleDeliveryAdjustTypeEnum.bomDesign, init(manifest.getBomDesignDay() == null ? DEFAULT_BOM_DESIGN_DAY : manifest.getBomDesignDay(), PreSaleDeliveryAdjustTypeEnum.bomDesign));
        }
        return map;
    }

    static DiPreSaleDeliveryReview init(Integer day, PreSaleDeliveryAdjustTypeEnum type) {
        return init(day == null ? BigDecimal.ZERO : new BigDecimal(day), type);
    }

    static DiPreSaleDeliveryReview init(BigDecimal day, PreSaleDeliveryAdjustTypeEnum type) {
        if (day == null) {
            day = BigDecimal.ZERO;
        }
        DiPreSaleDeliveryReview item = new DiPreSaleDeliveryReview();
        item.setAdjustBefore(day);
        item.setAdjust(BigDecimal.ZERO);
        item.setAdjustType(type.getType());
        return item;
    }

    @Override
    public String ApproverListByPreSaleReview(DiPreSale preSale) {
        List<String> userName = new ArrayList<>();
        if (PreSaleStatusEnum.DELIVERY_REVIEW.getCode().equals(preSale.getPreSaleStatus())
                && PreSaleDeliveryTaskFlagEnum.PENDING.getCode().equals(preSale.getDeliveryReviewTechSupportFlag())
        ) {
            //技术支持阶段
            List<DiPreSaleCustomized> list = diPreSaleCustomizedMapper.selectList(Wrappers.<DiPreSaleCustomized>lambdaQuery()
                    .eq(DiPreSaleCustomized::getPreSaleId, preSale.getId()));
            if (CollectionUtils.isNotEmpty(list)) {
                DiPreSaleCustomized config = list.get(0);
                if (org.apache.commons.lang3.StringUtils.isNotEmpty(config.getTechSupportOwnerCode())) {
                    userName.add(remoteUserService.queryUserNickName(config.getTechSupportOwnerCode()));
                }
            }
        } else {
            if (PreSaleDeliveryTaskFlagEnum.PENDING.getCode().equals(preSale.getDeliveryReviewSupplyFlag()) &&
                    !PreSaleDeliveryTaskFlagEnum.PENDING.getCode().equals(preSale.getDeliveryReviewTechSupportFlag())
            ) {
                //采购阶段
                if (PreSaleTypeEnum.of(preSale.getPreSaleType()) == PreSaleTypeEnum.BIG_NON_STANDARD) {
                    DiPreSaleCustomized config = this.queryCustomized(preSale.getId());
                    if (org.apache.commons.lang3.StringUtils.isNotEmpty(config.getTechSupportOwnerCode())) {
                        userName.add(remoteUserService.queryUserNickName(config.getTechSupportOwnerCode()));
                    }
                } else {
                    userName.addAll(this.getApproverList(purchaseManagerApproveRoleKey));
                }
//                userName.add(remoteUserService.queryUserNickName(purchaseManagerUserId));
            } else if (PreSaleDeliveryTaskFlagEnum.PENDING.getCode().equals(preSale.getDeliveryReviewDeliveryFlag())) {
                //交期复核阶段
                userName.addAll(this.getApproverList(deliverManagerApproveRoleKey));
//                userName.add(remoteUserService.queryUserNickName(deliverManagerUserId));
            }
        }
        if (StringUtils.isEmpty(userName)) {
            return "";
        }
        return userName.stream().distinct().collect(Collectors.joining(","));
    }

    /**
     * 查询复核后交期
     *
     * @param diPreSale
     * @return
     */
    @Override
    public BigDecimal queryAfterCheckDeliveryTimeUnitWeek(DiPreSale diPreSale) {
        BigDecimal days = BigDecimal.ZERO;

        BigDecimal guideDuration = diPreSale.getGuideDuration();
        if (!Objects.equals(diPreSale.getDeliveryReviewFlag(), PreSaleDeliveryFlagEnum.NEED.getCode())) {
            days = guideDuration;
        } else {
            DeliveryReviewSummaryRequest request = new DeliveryReviewSummaryRequest();
            request.setPreSaleId(diPreSale.getId().intValue());
            DeliveryReviewSummaryResponse resp = this.deliveryReviewSummary(request);
            Map<PreSaleDeliveryAdjustTypeEnum, DeliveryReviewDuration> map = resp.getDuration().stream().collect(Collectors.toMap(x -> PreSaleDeliveryAdjustTypeEnum.of(x.getAdjustType()), Function.identity()));

            BigDecimal supportDay = map.getOrDefault(PreSaleDeliveryAdjustTypeEnum.techSupport, new DeliveryReviewDuration()).getAfter();
            BigDecimal electricDay = map.getOrDefault(PreSaleDeliveryAdjustTypeEnum.electric, new DeliveryReviewDuration()).getAfter();
            BigDecimal mechineDay = map.getOrDefault(PreSaleDeliveryAdjustTypeEnum.machine, new DeliveryReviewDuration()).getAfter();
            BigDecimal supplyDay = map.getOrDefault(PreSaleDeliveryAdjustTypeEnum.supply, new DeliveryReviewDuration()).getAfter();
            BigDecimal productDay = map.getOrDefault(PreSaleDeliveryAdjustTypeEnum.product, new DeliveryReviewDuration()).getAfter();
            BigDecimal bomDesignDay = map.getOrDefault(PreSaleDeliveryAdjustTypeEnum.bomDesign, new DeliveryReviewDuration()).getAfter();
            BigDecimal deliveryDebugDay = BigDecimal.ZERO;
            if (PreSaleTypeEnum.TRADE.getCode().equals(diPreSale.getPreSaleType())) {
                days = supplyDay;
            } else {
                days = DiHelper.computeGuideDurationForProject(supportDay, electricDay, mechineDay, supplyDay, productDay, deliveryDebugDay, bomDesignDay);
            }
        }

        if (days == null) {
            days = BigDecimal.ZERO;
        }

        return days.divide(new BigDecimal(7), 0, RoundingMode.CEILING);

    }

    /**
     * 查询历史方案
     *
     * @return
     */
    @Override
    public PreSaleDetailResponse.Fee queryHistoryFeeFirst(Long preSaleId, Integer version) {

        PreSaleDetailResponse history = this.selectHistoryOfDiPreSale(preSaleId, 1, false);
        if (history == null) {
            return null;
        }
        if (CollectionUtils.isEmpty(history.getFees())) {
            return null;
        }
        return history.getFees().stream()
                .filter(x -> Objects.equals(x.getPreSaleVersion(), version.toString()))
                .findFirst().orElse(null);
    }

    /**
     * 保存物料实际费用
     *
     * @param request
     */
    @Override
    public void saveManifestRealFee(SaveManifestRealFeeRequest request) {
        this.diPreSaleFeeService.saveRealFee(request);
    }


    /**
     * 更新成商务报价
     *
     * @param preSaleId
     */
    @Override
    public void updateToBusinessQuotes(Long preSaleId) {
        DiPreSale diPreSale = this.lambdaQuery().eq(DiPreSale::getId, preSaleId).one();
        diPreSale.setPreSaleStatus(PreSaleStatusEnum.BUSINESS_QUOTES.getCode());
        this.updateById(diPreSale);
        PreSaleTypeEnum type = PreSaleTypeEnum.of(diPreSale.getPreSaleType());
        if (type == PreSaleTypeEnum.TRADE || type == PreSaleTypeEnum.BIG_NON_STANDARD) {
            diPreSaleFeeService.updatePreSaleFee(diPreSale);
            preSaleHistoryService.saveDiPreSaleHistory(diPreSale, PreSalePhaseEnum.PRE_SALE.getCode());
        }
    }

    /**
     * 查询物料计划审核列表
     *
     * @param materielVersionId
     * @param preSaleId
     * @return
     */
    @Override
    public List<MaterielChecklistNewDTO> queryMaterielPlanCheckListByVersion(Long materielVersionId, Long preSaleId) {
        DiPreSale preSale = this.getById(preSaleId);
        if (diPreSaleFeeService.reCalcStatus(preSale)) {
            return diMaterielService.queryMaterielPlanCheckListByVersion(materielVersionId);
        } else {
            //读缓存
            return preSaleHistoryService.queryMaterielPlanCheckList(preSaleId, PreSalePhaseEnum.ofPreSaleOrderStatus(preSale.getOrderPreSaleStatus()));
        }
    }

    /**
     * 查询物料计划列表
     *
     * @param materielVersionId
     * @return
     */
    @Override
    public MaterielVersionBomDTO bomByVersion(Long materielVersionId, Long preSaleId) {
        DiPreSale preSale = this.getById(preSaleId);
        if (diPreSaleFeeService.reCalcStatus(preSale)) {
            MaterielBomVersionVo vo = new MaterielBomVersionVo(materielVersionId, null);
            return diMaterielBomService.bomByVersion(vo);
        } else {
            //读缓存
            return preSaleHistoryService.bomByVersion(preSaleId, PreSalePhaseEnum.ofPreSaleOrderStatus(preSale.getOrderPreSaleStatus()));
        }
    }

    /**
     * 导出物料新
     *
     * @param materiel
     * @param preSaleId
     * @return
     */
    @Override
    public List<DiMaterielBomExportDTO> queryExportBom(Long materiel, Long preSaleId) {
        DiPreSale preSale = this.getById(preSaleId);
        if (diPreSaleFeeService.reCalcStatus(preSale) || PreSaleStatusEnum.LOCKED.getCode().equals(preSale.getPreSaleStatus())) {
            return iMultiVersionMaterielService.queryExportBom(materiel);
        } else {
            //读缓存
            return preSaleHistoryService.queryExportBom(preSaleId, PreSalePhaseEnum.ofPreSaleOrderStatus(preSale.getOrderPreSaleStatus()));
        }
    }

    /**
     * @param preSaleId
     * @return
     */
    @Override
    public DiPreSaleManifest queryFirstManifest(Long preSaleId) {
        List<DiPreSaleManifest> manifests = iDiPreSaleManifestService.queryByPreSaleId(Math.toIntExact(preSaleId));
        if (CollectionUtils.isEmpty(manifests)) {
            return null;
        }
        return manifests.get(0);
    }

    /**
     * 更新物料数量
     *
     * @param request
     */
    @Override
    public void updateManifestQuantity(UpdateManifestQuantityRequest request) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(request.getManifestList())) {
            log.warn("更新物料数量失败，manifestList为空");
            return;
        }
        Map<Long, UpdateManifestQuantityRequest.Manifest> manifestMap = request.getManifestList().stream().collect(Collectors.toMap(UpdateManifestQuantityRequest.Manifest::getMaterielId, Function.identity(), (a, b) -> a));

        List<DiPreSaleManifest> manifests = this.iDiPreSaleManifestService.queryByPreSaleId(Math.toIntExact(request.getPreSaleId()));

        manifests.forEach(manifest -> {
            if (manifestMap.containsKey(manifest.getMaterialVersion())) {
                Integer quantity = manifestMap.get(manifest.getMaterialVersion()).getUseNum();
                if (quantity == null) {
                    quantity = manifestMap.get(manifest.getMaterialVersion()).getQuantity();
                }
                manifest.setUseNum(quantity);
                manifest.setQuoteTotal(manifestMap.get(manifest.getMaterialVersion()).getQuoteTotal());
                manifest.setSingleSaleQuote(manifestMap.get(manifest.getMaterialVersion()).getSingleSaleQuote());
            }
        });
        iDiPreSaleManifestService.remove(Wrappers.<DiPreSaleManifest>lambdaQuery()
                .eq(DiPreSaleManifest::getDiPreSaleId, request.getPreSaleId()));
        this.savePreSaleManifest(request.getPreSaleId(), manifests, null);
        DiPreSale curDiPreSale = this.getById(request.getPreSaleId());
        diPreSaleFeeService.updatePreSaleFee(curDiPreSale);
        preSaleHistoryService.saveDiPreSaleHistory(curDiPreSale, PreSalePhaseEnum.PRE_SALE.getCode());
    }

    @Override
    public void updatePreSaleManifest(UpdatePreSaleManifestRequest request) {
        DiPreSaleManifest diPreSaleManifest = new DiPreSaleManifest();
        diPreSaleManifest.setMaterialsRequisition(request.getMaterialsRequisition());
        diPreSaleManifestMapper.update(diPreSaleManifest, Wrappers.<DiPreSaleManifest>lambdaUpdate().in(DiPreSaleManifest::getId, request.getManifestIds()));
        //刷新方案状态
        List<DiPreSaleManifest> diPreSaleManifestList = diPreSaleManifestMapper.selectBatchIds(request.getManifestIds());
        List<Long> preSaleIdList = diPreSaleManifestList.stream().map(DiPreSaleManifest::getDiPreSaleId).toList();
        if (CollectionUtils.isEmpty(preSaleIdList)) {
            return;
        }
        for (Long preSaleId : preSaleIdList) {
            DiPreSale diPreSale = this.getById(preSaleId);
            DiOrder order = diOrderService.queryByNicheNo(diPreSale.getNicheCode());
            if (diPreSale.getOrderPreSaleStatus() != null) {
                OrderPreSaleStatusEnum statusEnum = this.calcOrderPreSaleStatus(diPreSale, order);
                log.info("DiPreSaleServiceImpl---updatePreSaleManifest()---刷新方案状态，方案ID：{}，状态：{}", diPreSale.getId(), statusEnum.getCode());
                this.updatePreSaleOrderStatus(diPreSale.getId(), statusEnum.getCode());
            }
        }

    }

    /**
     * 更新复核交期备注
     *
     * @param preSaleId
     * @param note
     */
    @Override
    public void updateDeliveryReviewNote(Integer preSaleId, String note) {
        this.lambdaUpdate().eq(DiPreSale::getId, preSaleId).set(DiPreSale::getDeliveryReviewNote, note).update();
    }

    /**
     * 查询定制方案
     *
     * @param preSaleId
     * @return
     */
    @Override
    public DiPreSaleCustomized queryCustomized(Long preSaleId) {
        return diPreSaleCustomizedMapper.selectOne(Wrappers.<DiPreSaleCustomized>lambdaQuery()
                .eq(DiPreSaleCustomized::getPreSaleId, preSaleId)
                .orderByDesc(DiPreSaleCustomized::getId).last("limit 1"));
    }

    /**
     * 用户是否在角色组中
     *
     * @param currentUserCode
     * @param purchaseManagerApproveRoleKey
     * @return
     */
    @Override
    public boolean inRole(String currentUserCode, String purchaseManagerApproveRoleKey) {
        if (org.apache.commons.lang3.StringUtils.isBlank(currentUserCode)) {
            return false;
        }
        List<SysUser> userList = remoteRoleService.internalGetUserByRoleKey(purchaseManagerApproveRoleKey);
        if (CollectionUtils.isEmpty(userList)) {
            return false;
        }
        return userList.stream().anyMatch(user -> currentUserCode.equals(user.getUserName()));
    }

    /**
     * 开始 交付阶段 交期复核
     *
     * @param diPreSale
     */
    @Override
    public void startDeliveryReviewStageDelivery(DiPreSale diPreSale) {
        if (!PreSaleTypeEnum.TRADE.getCode().equals(diPreSale.getPreSaleType())) {

            this.lambdaUpdate().set(DiPreSale::getDeliveryReviewDeliveryFlag, PreSaleDeliveryTaskFlagEnum.PENDING.getCode())
                    .eq(DiPreSale::getId, diPreSale.getId())
                    .update();

            //获取交付复核人
            List<SysUser> deliverManagerUserList = remoteRoleService.internalGetUserByRoleKey(deliverManagerApproveRoleKey);
            if (CollectionUtil.isNotEmpty(deliverManagerUserList)) {
                List<String> deliverManagerUserIds = deliverManagerUserList.stream().map(SysUser::getUserName).toList();
                //新增交付复核待办任务和钉钉通知
                orderCommonService.saveAgencyTask(diPreSale.getId().toString(), null, diPreSale.getPreSaleCode(),
                        getProjectNo(diPreSale.getNicheCode()),
                        AgencyTaskTypeEnum.DELIVERY_TIME_CHECK_DELIVER,
                        StringUtils.isNotEmpty(diPreSale.getPreSaleName()) ? diPreSale.getPreSaleName() : diPreSale.getPreSaleCode(), DELIVERY_TIME_REVIEW.getDesc(),
                        deliverManagerUserIds, null);
                String deliverTitle = StrUtil.format(DingTalkEnum.DELIVERY_TIME_CHECK_DELIVER_NOTICE.getTitle(), diPreSale.getPreSaleCode());
                String deliverContent = StrUtil.format(DingTalkEnum.DELIVERY_TIME_CHECK_DELIVER_NOTICE.getMessage(), diPreSale.getNicheCode(), diPreSale.getPreSaleCode(), SecurityUtils.getLoginUser().getSysUser().getNickName());
                orderCommonService.sendDingTalkMessage("产品方案", deliverTitle, deliverContent, deliverManagerUserIds, null);
            } else {
                log.info("DiPreSaleServiceImpl---{}()---交付主管审批角色未绑定用户", diPreSale.getId());
            }
        } else {
            log.info("DiPreSaleServiceImpl---{}()---交易方案不需要交付阶段 交期复核", diPreSale.getId());
        }
    }

    /**
     * 不需要交付阶段 交期复核
     *
     * @param preSale
     */
    @Override
    public void noNeedDeliveryReviewStageDelivery(DiPreSale preSale) {
        PreSaleTypeEnum saleTypeEnum = PreSaleTypeEnum.of(preSale.getPreSaleType());
        PreSaleStatusEnum nextStatus = PreSaleStatusEnum.LOCKED;
        if (PreSaleTypeEnum.TRADE == saleTypeEnum || PreSaleTypeEnum.STANDARD == saleTypeEnum) {
            nextStatus = PreSaleStatusEnum.BUSINESS_QUOTES;
        }
        this.lambdaUpdate()
                .set(DiPreSale::getDeliveryReviewDeliveryFlag, PreSaleDeliveryTaskFlagEnum.NO_NEED.getCode())
                .set(DiPreSale::getPreSaleStatus, nextStatus.getCode())
                .eq(DiPreSale::getId, preSale.getId())
                .update();

        PreSaleDeliveryReviewDoneEvent event = new PreSaleDeliveryReviewDoneEvent();
        event.setPreSaleId(preSale.getId());
        EventBusUtils.publishEvent(event);
    }

    @Override
    public void noNeedPurchaseReview(DiPreSale curPreSale, String lockingscheme) {

        PreSaleTypeEnum saleTypeEnum = PreSaleTypeEnum.of(curPreSale.getPreSaleType());
        PreSaleStatusEnum nextStatus = PreSaleStatusEnum.LOCKED;
        if (PreSaleTypeEnum.TRADE == saleTypeEnum || PreSaleTypeEnum.STANDARD == saleTypeEnum) {
            nextStatus = PreSaleStatusEnum.BUSINESS_QUOTES;
        }
        this.lambdaUpdate()
                .set(DiPreSale::getDeliveryReviewDeliveryFlag, PreSaleDeliveryTaskFlagEnum.NO_NEED.getCode())
                .set(DiPreSale::getDeliveryReviewSupplyFlag, PreSaleDeliveryTaskFlagEnum.NO_NEED.getCode())
                .set(DiPreSale::getPreSaleStatus, nextStatus.getCode())
                .eq(DiPreSale::getId, curPreSale.getId())
                .update();

    }

    List<String> getApproverList(String rolekey) {
        // 查询用户列表
        List<SysUser> userList = remoteRoleService.internalGetUserByRoleKey(rolekey);
        if (CollectionUtils.isEmpty(userList)) {
            return new ArrayList<>();
        }
        return userList.stream().map(SysUser::getNickName).collect(Collectors.toList());

    }

}
