package com.dyd.di.matrix.service;

import com.dyd.di.matrix.entity.DiMatrixTerritoryComparisonProduct;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 业务版图对比产品 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
public interface IDiMatrixTerritoryComparisonProductService extends IService<DiMatrixTerritoryComparisonProduct> {

    default List<DiMatrixTerritoryComparisonProduct> queryByTerritoryId(Long territoryId, List<Long> groupIdList) {
        return this.lambdaQuery().in(DiMatrixTerritoryComparisonProduct::getGroupId, groupIdList)
                .eq(DiMatrixTerritoryComparisonProduct::getDelFlag, 0)
                .orderByAsc(DiMatrixTerritoryComparisonProduct::getWeight)
                .list();
    }

    default void removeByGroupId(List<Long> groupIds) {

        if (groupIds == null || groupIds.size() == 0) {
            return;
        }
        this.lambdaUpdate().in(DiMatrixTerritoryComparisonProduct::getGroupId, groupIds).eq(DiMatrixTerritoryComparisonProduct::getDelFlag, 0).remove();
    }
}
