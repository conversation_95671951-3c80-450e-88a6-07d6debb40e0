package com.dyd.di.marketing.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dyd.common.core.domain.R;
import com.dyd.common.core.enums.OssSysCodeEnum;
import com.dyd.common.core.enums.PreSaleQuoteStatusEnum;
import com.dyd.common.core.enums.RelationTypeEnum;
import com.dyd.common.core.exception.ServiceException;
import com.dyd.common.core.exception.auth.NotPermissionException;
import com.dyd.common.core.utils.AfterCommitExecutor;
import com.dyd.common.core.utils.DateUtils;
import com.dyd.common.core.utils.PageHelp;
import com.dyd.common.core.utils.PageWrapper;
import com.dyd.common.datascope.annotation.DataScope;
import com.dyd.common.security.utils.SecurityUtils;
import com.dyd.dbc.api.RemoteDbcService;
import com.dyd.dbc.api.model.DdUserDTO;
import com.dyd.dbc.api.model.QueryDdUserDTO;
import com.dyd.di.agency.constants.AgencyConstants;
import com.dyd.di.agency.domain.dto.AgencyTaskInfoDTO;
import com.dyd.di.agency.enums.AgencyTaskTypeEnum;
import com.dyd.di.agency.service.DiAgencyTaskService;
import com.dyd.di.api.model.DiMarketingNicheListResponse;
import com.dyd.di.contract.iservice.IDiContractService;
import com.dyd.di.marketing.constants.NicheApprovalConstants;
import com.dyd.di.marketing.conver.CusConverUtil;
import com.dyd.di.marketing.domain.*;
import com.dyd.di.marketing.domain.dto.FullAddressDTO;
import com.dyd.di.marketing.domain.dto.UpdateNicheImportanceRequest;
import com.dyd.di.marketing.domain.event.NicheRollback2SolutionStageEvent;
import com.dyd.di.marketing.enums.NicheDingTalkEnum;
import com.dyd.di.marketing.enums.NicheStatusEnum;
import com.dyd.di.marketing.mapper.DiMarketingCustomerMapper;
import com.dyd.di.marketing.mapper.DiMarketingNicheMapper;
import com.dyd.di.marketing.service.*;
import com.dyd.di.material.service.CommonService;
import com.dyd.di.message.domain.DiMessageList;
import com.dyd.di.message.service.IDiMessageListService;
import com.dyd.di.oss.FileVo;
import com.dyd.di.oss.OssService;
import com.dyd.di.pre.entity.DiPreSaleQuote;
import com.dyd.di.pre.mapper.DiPreSaleQuoteMapper;
import com.dyd.di.process.domain.DiProjectRelation;
import com.dyd.di.process.job.CheckProcessProjectStatusJob;
import com.dyd.di.process.pojo.vo.ProcessProjectAddVO;
import com.dyd.di.process.service.IDiProcessProjectService;
import com.dyd.di.process.service.IDiProjectRelationService;
import com.dyd.di.sequence.SequenceService;
import com.dyd.di.syscategory.entity.SysCategory;
import com.dyd.di.syscategory.service.ISysCategoryService;
import com.dyd.jdy.JdyClient;
import com.dyd.jdy.bean.TechnicalProposal.ProjectTechnicalProposalRequest;
import com.dyd.jdy.bean.common.Cond;
import com.dyd.jdy.bean.common.Filter;
import com.dyd.jdy.bean.jdy.JdyCommonDto;
import com.dyd.jdy.bean.stockUp.*;
import com.dyd.jdy.constant.JdyConstants;
import com.dyd.system.api.RemoteDictDataService;
import com.dyd.system.api.RemoteUserService;
import com.dyd.system.api.domain.SysDept;
import com.dyd.system.api.domain.SysDictData;
import com.dydtec.base.oss.api.dto.response.OssPreviewDTO;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 商机Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-19
 */
@Service
@Slf4j
public class DiMarketingNicheServiceImpl extends ServiceImpl<DiMarketingNicheMapper, DiMarketingNiche> implements IDiMarketingNicheService {
    @Autowired
    private DiMarketingNicheMapper diMarketingNicheMapper;

    @Autowired
    private IDiMarketingNicheService diMarketingNicheService;

    @Autowired
    private IDiMarketingContactsService diMarketingContactsService;

    @Autowired
    private SequenceService sequenceService;

    @Autowired
    private RemoteDbcService remoteDbcService;

    @Autowired
    private RemoteUserService remoteUserService;

    @Resource
    private IDiProjectRelationService diProjectRelationService;

    @Resource
    private CommonService commonService;

    @Autowired
    private OssService ossService;

    @Autowired
    private IDiMarketingCustomerService diMarketingCustomerService;

    @Autowired
    private DiMarketingCustomerMapper diMarketingCustomerMapper;

    @Autowired
    private RemoteDictDataService remoteDictDataService;

    @Autowired
    private RocketMQTemplate rocketMQTemplate;

    @Autowired
    private IDiProcessProjectService diProcessProjectService;

    @Autowired
    private IDiContractService iDiContractService;

    @Autowired
    private IDiMessageListService iDiMessageListService;

    @Autowired
    private ISysCategoryService sysCategoryService;

    @Autowired
    private DiFeasibilityVerifyProcessService diFeasibilityVerifyProcessService;

    @Autowired
    private CusConverUtil cusConverUtil;

    @Autowired
    private DiMarketingNicheDemandService diMarketingNicheDemandService;

    @Autowired
    private DiMarketingNicheDemandFileService diMarketingNicheDemandFileService;

    @Autowired
    private DiMarketingNicheDemandIntentionService diMarketingNicheDemandIntentionService;

    @Autowired
    private DiMarketingNicheRequirementAccessoryService diMarketingNicheRequirementAccessoryService;

    @Autowired
    private DiMarketingNicheRequirementCombustorService diMarketingNicheRequirementCombustorService;

    @Autowired
    private DiMarketingNicheRequirementElectrollService diMarketingNicheRequirementElectrollService;

    @Autowired
    private DiMarketingNicheRequirementHotstoveService diMarketingNicheRequirementHotstoveService;

    @Autowired
    private DiMarketingNicheRequirementValveService diMarketingNicheRequirementValveService;

    @Autowired
    private DiMarketingNicheFlameAccessoryService diMarketingNicheFlameAccessoryService;

    @Autowired
    private DiPreSaleQuoteMapper diPreSaleQuoteMapper;
    @Autowired
    private JdyClient jdyClient;

    @Autowired
    private ApplicationEventPublisher eventPublisher;

    @Autowired
    private DiAgencyTaskService diAgencyTaskService;

    @Autowired
    private IDiProcessProjectService iDiProcessProjectService;

    @Autowired
    private CheckProcessProjectStatusJob checkProcessProjectStatusJob;


    /**
     * 查询商机
     *
     * @param id 商机主键
     * @return 商机
     */
    @Override
    public DiMarketingNiche selectDiMarketingNicheById(String id, Boolean isAuth) {
        DiMarketingNiche diMarketingNiche = diMarketingNicheMapper.selectDiMarketingNicheById(id);
        if (null != diMarketingNiche) {

            DiMarketingNiche qxDiMarketingNiche = new DiMarketingNiche();
            qxDiMarketingNiche.setNicheNo(diMarketingNiche.getNicheNo());
            if (isAuth) {
                List<DiMarketingNiche> qxList = diMarketingNicheService.selectDiMarketingNicheList(qxDiMarketingNiche);
                if (CollectionUtils.isEmpty(qxList)) {
                    throw new NotPermissionException("无权限访问！");
                }
            }

            DiMarketingContacts diMarketingContacts = new DiMarketingContacts();
            diMarketingContacts.setBusinessId(diMarketingNiche.getNicheNo());
            diMarketingNiche.setDiMarketingContactsList(diMarketingContactsService.selectDiMarketingContactsList(diMarketingContacts));

            Map<String, String> userNameMap = Maps.newHashMap();
            List<String> list = new ArrayList<>();
            list.add(Optional.ofNullable(diMarketingNiche.getCreateBy()).orElse(""));
            if (StringUtils.isNotBlank(diMarketingNiche.getUpdateBy())) {
                list.add(diMarketingNiche.getUpdateBy());
            }
            if (StringUtils.isNotBlank(diMarketingNiche.getNicheOwner())) {
                list.add(diMarketingNiche.getNicheOwner());
            }

            List<String> checkList = ListUtils.emptyIfNull(list).stream().filter(StringUtils::isNoneEmpty).toList();
            R<List<DdUserDTO>> userListResult = remoteDbcService.queryDdUserList(QueryDdUserDTO.builder().jobNumberList(checkList).build());
            if (userListResult.isSuccess()) {
                userNameMap = userListResult.getData().stream().filter(item -> Objects.nonNull(item.getJobNumber()))
                        .collect(Collectors.toMap(DdUserDTO::getJobNumber, DdUserDTO::getName));
                diMarketingNiche.setUpdateBy(userNameMap.get(diMarketingNiche.getUpdateBy()));
                diMarketingNiche.setCreateBy(userNameMap.get(diMarketingNiche.getCreateBy()));
                diMarketingNiche.setNicheOwnerName(userNameMap.get(diMarketingNiche.getNicheOwner()));
            }

            //部门信息
            if (StringUtils.isNotBlank(diMarketingNiche.getNicheOwnerDept())) {
                R<SysDept> deptResult = remoteUserService.getInfoByDeptId(Long.valueOf(diMarketingNiche.getNicheOwnerDept()));
                if (null != deptResult.getData())
                    diMarketingNiche.setNicheOwnerDeptName(deptResult.getData().getDeptName());
            }

            //客户信息
            if (StringUtils.isNotBlank(diMarketingNiche.getCustomerNo())) {
                diMarketingNiche.setDiMarketingCustomer(diMarketingCustomerMapper.selectDiMarketingCustomerById(diMarketingNiche.getCustomerNo()));
            }

            //附件处理
            if (StringUtils.isNotBlank(diMarketingNiche.getNicheFile())) {
                List<FileVo> fileVoList = new ArrayList<>();
                List<String> fileList = Arrays.asList(diMarketingNiche.getNicheFile().split(","));
                List<OssPreviewDTO> previewDTOMap = ossService.getOssFileByList(OssSysCodeEnum.MATERIEL.getType(), fileList, 0);
                for (OssPreviewDTO ossPreviewDTO : previewDTOMap) {
                    FileVo fileVo = new FileVo();
                    fileVo.setFileKey(ossPreviewDTO.getOssKey());
                    fileVo.setFileName(ossPreviewDTO.getMimeName());
                    fileVo.setIsPic(ossPreviewDTO.getMimeType().contains("image") ? 1 : 0);
                    fileVo.setFileUrl(ossPreviewDTO.getShowUrl());
                    fileVoList.add(fileVo);
                }
                diMarketingNiche.setFileList(fileVoList);
            }

            // 查询需求信息
            DiMarketingNicheDemand demand = diMarketingNicheDemandService
                    .getOne(Wrappers.<DiMarketingNicheDemand>lambdaQuery().eq(DiMarketingNicheDemand::getNicheNo, diMarketingNiche.getNicheNo())
                            .last("limit 1"));
            if (demand != null) {
                FullAddressDTO useFullAddress = new FullAddressDTO();

                if (StringUtils.isNoneEmpty(demand.getUseAddressNation()) && demand.getUseAddressNation().contains("/")) {
                    List<String> nationPart = Splitter.on("/").splitToList(demand.getUseAddressNation());
                    useFullAddress.setCountryCode(nationPart.get(0));
                    useFullAddress.setCountryName(nationPart.get(1));
                }
                if (StringUtils.isNoneEmpty(demand.getUseAddressDist()) && demand.getUseAddressDist().contains("/")) {
                    List<String> areaPart = Splitter.on("/").splitToList(demand.getUseAddressDist());
                    useFullAddress.setProvinceCode(areaPart.get(0));
                    useFullAddress.setProvinceName(areaPart.get(1));
                    useFullAddress.setCityCode(areaPart.get(2));
                    useFullAddress.setCityName(areaPart.get(3));
                    useFullAddress.setAreaCode(areaPart.get(4));
                    useFullAddress.setAreaName(areaPart.get(5));
                }
                useFullAddress.setAddress(demand.getUseAddressInfo());
                demand.setUseFullAddress(useFullAddress);

                FullAddressDTO deliveryFullAddress = new FullAddressDTO();
                if (StringUtils.isNoneEmpty(demand.getDeliveryAddressNation()) && demand.getDeliveryAddressNation().contains("/")) {
                    List<String> nationPart = Splitter.on("/").splitToList(demand.getDeliveryAddressNation());
                    deliveryFullAddress.setCountryCode(nationPart.get(0));
                    deliveryFullAddress.setCountryName(nationPart.get(1));
                }
                if (StringUtils.isNoneEmpty(demand.getDeliveryAddressDist()) && demand.getDeliveryAddressDist().contains("/")) {
                    List<String> areaPart = Splitter.on("/").splitToList(demand.getDeliveryAddressDist());
                    deliveryFullAddress.setProvinceCode(areaPart.get(0));
                    deliveryFullAddress.setProvinceName(areaPart.get(1));
                    deliveryFullAddress.setCityCode(areaPart.get(2));
                    deliveryFullAddress.setCityName(areaPart.get(3));
                    deliveryFullAddress.setAreaCode(areaPart.get(4));
                    deliveryFullAddress.setAreaName(areaPart.get(5));
                }
                deliveryFullAddress.setAddress(demand.getDeliveryAddressInfo());
                demand.setDeliveryFullAddress(deliveryFullAddress);

                diMarketingNiche.setDiMarketingNicheDemand(demand);

                List<String> demandScopeList = Splitter.on(",").splitToList(Optional.ofNullable(demand.getDemandScope()).orElse(""))
                        .stream().filter(StringUtils::isNoneEmpty).toList();
                demand.setDemandScopeList(demandScopeList);

                if (1 == demand.getDemandType()) {
                    String val = null;
                    if (StringUtils.isNotBlank(demand.getScene())) {
                        val = demand.getScene();
                    } else if (StringUtils.isNotBlank(demand.getApplication())) {
                        val = demand.getApplication();
                    } else if (StringUtils.isNotBlank(demand.getIndustry())) {
                        val = demand.getIndustry();
                    }
                    if (StringUtils.isNotBlank(val)) {
                        DictPersonCharge dictPersonCharge = new DictPersonCharge();
                        dictPersonCharge.setDictType("industry_owner");
                        dictPersonCharge.setDictValue(val);
                        if (StringUtils.isBlank(demand.getDictPersonChargeName())) {
                            DictPersonChargeVo dictPersonChargeVo = diMarketingNicheService.getDictPersonChargeName(dictPersonCharge);
                            demand.setDictPersonChargeName(dictPersonChargeVo.getDictPersonChargeName());
                            demand.setDictPersonChargeNo(dictPersonChargeVo.getDictPersonChargeNo());
                        }
                    }
                } else if (2 == demand.getDemandType()) {
                    if (StringUtils.isNotBlank(demand.getFuelAccessoryCategory())) {
                        DictPersonCharge dictPersonCharge = new DictPersonCharge();
                        dictPersonCharge.setDictType("fuel_accessory_owner");
                        SysDictData dictData = new SysDictData();
                        dictData.setDictType("fuel_accessory_category");
                        dictData.setDictValue(demand.getFuelAccessoryCategory());
                        String clueStatusDesc = remoteDictDataService.selectDictLabel(dictData);
                        dictPersonCharge.setDictValue(clueStatusDesc);
                        if (StringUtils.isBlank(demand.getDictPersonChargeName())) {
                            DictPersonChargeVo dictPersonChargeVo = diMarketingNicheService.getDictPersonChargeName(dictPersonCharge);
                            demand.setDictPersonChargeName(dictPersonChargeVo.getDictPersonChargeName());
                            demand.setDictPersonChargeNo(dictPersonChargeVo.getDictPersonChargeNo());
                        }
                    }
                } else if (3 == demand.getDemandType()) {
                    if (StringUtils.isNotBlank(demand.getFlameCategory())) {
                        DictPersonCharge dictPersonCharge = new DictPersonCharge();
                        dictPersonCharge.setDictType("flame_owner");
                        SysDictData dictData = new SysDictData();
                        dictData.setDictType("flame_category");
                        dictData.setDictValue(demand.getFlameCategory());
                        String clueStatusDesc = remoteDictDataService.selectDictLabel(dictData);
                        dictPersonCharge.setDictValue(clueStatusDesc);
                        if (StringUtils.isBlank(demand.getDictPersonChargeName())) {
                            DictPersonChargeVo dictPersonChargeVo = diMarketingNicheService.getDictPersonChargeName(dictPersonCharge);
                            demand.setDictPersonChargeName(dictPersonChargeVo.getDictPersonChargeName());
                            demand.setDictPersonChargeNo(dictPersonChargeVo.getDictPersonChargeNo());
                        }
                    }
                }
                List<String> industryInfo = Lists.newArrayList(demand.getIndustry(), demand.getApplication(), demand.getScene()).stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
                demand.setIndustryInfo(industryInfo);
            }

            diMarketingNiche.setDiMarketingNicheDemandFileList(diMarketingNicheDemandFileService.list(Wrappers.<DiMarketingNicheDemandFile>lambdaQuery().eq(DiMarketingNicheDemandFile::getNicheNo, diMarketingNiche.getNicheNo())));

            List<DiMarketingNicheDemandIntention> intentionList = diMarketingNicheDemandIntentionService.list(Wrappers.<DiMarketingNicheDemandIntention>lambdaQuery().eq(DiMarketingNicheDemandIntention::getNicheNo, diMarketingNiche.getNicheNo()));
            ListUtils.emptyIfNull(intentionList).stream().filter(Objects::nonNull).forEach(item -> {
                List<String> industryInfo = Lists.newArrayList(item.getIndustry(), item.getApplication(), item.getScene()).stream().filter(Objects::nonNull).collect(Collectors.toList());
                item.setIndustryInfo(industryInfo);

                List<String> industryDesc =
                        Lists.newArrayList(
                                Optional.ofNullable(StringUtils.trimToNull(item.getIndustrySpecial())).orElse(item.getIndustryName()),
                                Optional.ofNullable(StringUtils.trimToNull(item.getApplicationSpecial())).orElse(item.getApplicationName()),
                                Optional.ofNullable(StringUtils.trimToNull(item.getSceneSpecial())).orElse(item.getSceneName())
                        ).stream().filter(StringUtils::isNoneEmpty).toList();
                String industryChain = Joiner.on("/").join(industryDesc);
                item.setIndustryChain(industryChain);
            });
            diMarketingNiche.setDiMarketingNicheDemandIntentionList(intentionList);

            diMarketingNiche.setDiMarketingNicheRequirementAccessoryList(diMarketingNicheRequirementAccessoryService.list(Wrappers.<DiMarketingNicheRequirementAccessory>lambdaQuery().eq(DiMarketingNicheRequirementAccessory::getNicheNo, diMarketingNiche.getNicheNo())));

            List<DiMarketingNicheRequirementCombustor> combustorList = diMarketingNicheRequirementCombustorService.list(Wrappers.<DiMarketingNicheRequirementCombustor>lambdaQuery().eq(DiMarketingNicheRequirementCombustor::getNicheNo, diMarketingNiche.getNicheNo()));
            ListUtils.emptyIfNull(combustorList).stream().filter(Objects::nonNull).forEach(item -> {
                List<String> typeInfo = Lists.newArrayList(item.getBrand(), item.getType(), item.getSeries()).stream().filter(Objects::nonNull).collect(Collectors.toList());
                item.setTypeInfo(typeInfo);

                List<String> typeDesc =
                        Lists.newArrayList(
                                Optional.ofNullable(StringUtils.trimToNull(item.getBrandSpecial())).orElse(item.getBrandName()),
                                Optional.ofNullable(StringUtils.trimToNull(item.getTypeSpecial())).orElse(item.getTypeName()),
                                Optional.ofNullable(StringUtils.trimToNull(item.getSeriesSpecial())).orElse(item.getSeriesName())
                        ).stream().filter(StringUtils::isNoneEmpty).toList();
                String typeChain = Joiner.on("/").join(typeDesc);
                item.setTypeChain(typeChain);
            });
            diMarketingNiche.setDiMarketingNicheRequirementCombustorList(combustorList);

            diMarketingNiche.setDiMarketingNicheRequirementElectrollList(diMarketingNicheRequirementElectrollService.list(Wrappers.<DiMarketingNicheRequirementElectroll>lambdaQuery().eq(DiMarketingNicheRequirementElectroll::getNicheNo, diMarketingNiche.getNicheNo())));

            diMarketingNiche.setDiMarketingNicheRequirementHotstoveList(diMarketingNicheRequirementHotstoveService.list(Wrappers.<DiMarketingNicheRequirementHotstove>lambdaQuery().eq(DiMarketingNicheRequirementHotstove::getNicheNo, diMarketingNiche.getNicheNo())));

            diMarketingNiche.setDiMarketingNicheRequirementValveList(diMarketingNicheRequirementValveService.list(Wrappers.<DiMarketingNicheRequirementValve>lambdaQuery().eq(DiMarketingNicheRequirementValve::getNicheNo, diMarketingNiche.getNicheNo())));

            diMarketingNiche.setDiMarketingNicheFlameAccessoryList(diMarketingNicheFlameAccessoryService.list(Wrappers.<DiMarketingNicheFlameAccessory>lambdaQuery().eq(DiMarketingNicheFlameAccessory::getNicheNo, diMarketingNiche.getNicheNo())));
            if (CollectionUtils.isEmpty(diMarketingNiche.getDiMarketingNicheFlameAccessoryList())) {
                List<DiMarketingNicheFlameAccessory> flameAccessoryList = new ArrayList<>();
                diMarketingNiche.setDiMarketingNicheFlameAccessoryList(flameAccessoryList);
            }
            diMarketingNiche.setIsUpdateNiche(true);
            //判断是否可以编辑
            if (!diMarketingNiche.getNicheStatus().equals("0") && !diMarketingNiche.getNicheStatus().equals("1")) {
                diMarketingNiche.setIsUpdateNiche(false);
                return diMarketingNiche;
            }
            //获取销售报价单
            List<DiPreSaleQuote> preSaleQuoteList = diPreSaleQuoteMapper.selectList(new LambdaQueryWrapper<DiPreSaleQuote>()
                    .eq(DiPreSaleQuote::getNicheCode, diMarketingNiche.getNicheNo()));
            if (CollectionUtil.isNotEmpty(preSaleQuoteList)) {
                //判断销售报价单状态
                for (DiPreSaleQuote preSaleQuote : preSaleQuoteList) {
                    if (preSaleQuote.getApprovalStatus().equals(1) || preSaleQuote.getApprovalStatus().equals(2) || PreSaleQuoteStatusEnum.DELIVERY_TIME_REVIEW.getCode().equals(preSaleQuote.getQuoteStatus())) {
                        diMarketingNiche.setIsUpdateNiche(false);
                        return diMarketingNiche;
                    }
                }
            }
        }
        return diMarketingNiche;
    }

    /**
     * 根据id集合批量查询
     *
     * @param ids
     * @return
     */
    @Override
    public List<DiMarketingNiche> selectDiMarketingNicheListByIds(List<Integer> ids) {
        List<DiMarketingNiche> diMarketingNiches = diMarketingNicheMapper.selectList(Wrappers.<DiMarketingNiche>lambdaQuery().in(DiMarketingNiche::getId, ids));
        return diMarketingNiches;
    }

    /**
     * 查询商机列表（无鉴权）
     *
     * @param diMarketingNiche
     * @return
     */
    public List<DiMarketingNiche> selectDiMarketingNicheListNoAuth(DiMarketingNiche diMarketingNiche) {
        return queryDiMarketingNicheList(diMarketingNiche);
    }

    /**
     * 查询商机列表
     *
     * @param diMarketingNiche 商机
     * @return 商机
     */
    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<DiMarketingNiche> selectDiMarketingNicheList(DiMarketingNiche diMarketingNiche) {

        if (null != diMarketingNiche.getParams() && ((String) diMarketingNiche.getParams().get("dataScope")).contains("AND")) {
            diMarketingNiche.getParams().put("dataScope", ((String) diMarketingNiche.getParams().get("dataScope")).replace("AND", "OR"));
        } else {
            diMarketingNiche.getParams().put("dataScope", " OR 2 = 2");
        }

        diMarketingNiche.getParams().put("loginUser", "'" + SecurityUtils.getUsername() + "'");

        return queryDiMarketingNicheList(diMarketingNiche);
    }

    private List<DiMarketingNiche> queryDiMarketingNicheList(DiMarketingNiche diMarketingNiche) {
        List<DiMarketingNiche> list = diMarketingNicheMapper.selectDiMarketingNicheList(diMarketingNiche);
        if (!CollectionUtils.isEmpty(list)) {
            Map<String, String> userNameMap = Maps.newHashMap();
            List<String> jobNumListc = Lists.newArrayList(list.stream().map(DiMarketingNiche::getCreateBy).filter(StringUtils::isNotBlank).toList());
            List<String> jobNumListu = Lists.newArrayList(list.stream().map(DiMarketingNiche::getUpdateBy).filter(StringUtils::isNotBlank).toList());
            List<String> jobNumListo = Lists.newArrayList(list.stream().map(DiMarketingNiche::getNicheOwner).filter(StringUtils::isNotBlank).toList());
            jobNumListc.addAll(jobNumListu);
            jobNumListc.addAll(jobNumListo);
            Set<String> set = new HashSet<>(jobNumListc);
            R<List<DdUserDTO>> userListResult = remoteDbcService.queryDdUserList(QueryDdUserDTO.builder().jobNumberList(new ArrayList<>(set)).build());
            if (userListResult.isSuccess()) {
                userNameMap = userListResult.getData().stream().collect(Collectors.toMap(DdUserDTO::getJobNumber, DdUserDTO::getName));
            }
            for (DiMarketingNiche diMarketingNiche1 : list) {
                diMarketingNiche1.setUpdateBy(userNameMap.get(diMarketingNiche1.getUpdateBy()));
                diMarketingNiche1.setCreateBy(userNameMap.get(diMarketingNiche1.getCreateBy()));
                diMarketingNiche1.setNicheOwner(userNameMap.get(diMarketingNiche1.getNicheOwner()));

                //部门信息
                if (StringUtils.isNotBlank(diMarketingNiche1.getNicheOwnerDept())) {
                    R<SysDept> deptResult = remoteUserService.getInfoByDeptId(Long.valueOf(diMarketingNiche1.getNicheOwnerDept()));
                    if (null != deptResult.getData())
                        diMarketingNiche1.setNicheOwnerDept(deptResult.getData().getDeptName());
                }

                if (StringUtils.isNotBlank(diMarketingNiche1.getIndustryCategories())) {
                    diMarketingNiche1.setIndustryCategories(getCategoryName(diMarketingNiche1.getIndustryCategories()));
                }

                if (StringUtils.isNotBlank(diMarketingNiche1.getApplicationCategories())) {
                    diMarketingNiche1.setApplicationCategories(getCategoryName(diMarketingNiche1.getApplicationCategories()));
                }

                if (StringUtils.isNotBlank(diMarketingNiche1.getApplicationSubcategories())) {
                    diMarketingNiche1.setApplicationSubcategories(getCategoryName(diMarketingNiche1.getApplicationSubcategories()));
                }

                if (StringUtils.isNotBlank(diMarketingNiche1.getFourLevelClassification())) {
                    diMarketingNiche1.setFourLevelClassification(getCategoryName(diMarketingNiche1.getFourLevelClassification()));
                }

            }
        }

        return list;
    }

    public String getCategoryName(String id) {
        SysCategory sysCategory = sysCategoryService.getById(id);
        if (null == sysCategory) {
            return "";
        }
        return sysCategory.getName();
    }

    /**
     * 新增商机
     *
     * @param diMarketingNiche 商机
     * @return 结果
     */
    @Override
    @Transactional
    public Map<String, String> insertDiMarketingNiche(DiMarketingNiche diMarketingNiche) {
        if (StringUtils.isBlank(diMarketingNiche.getNicheOwner())) {
            throw new ServiceException("主要销售不能为空");
        }
        diMarketingNiche.setCreateBy(SecurityUtils.getUsername());
        diMarketingNiche.setCreateTime(DateUtils.getNowDate());
        String nicheNo = sequenceService.getSequenceNo("DYD-SJ");
        diMarketingNiche.setNicheNo(nicheNo);

        //记录共享销售和主要销售(待办任务，勿删)
        List<String> byList = new ArrayList<>();
        byList.add(diMarketingNiche.getNicheOwner());
        List<DiMarketingContacts> contactsList = diMarketingNiche.getDiMarketingContactsList();
        String sharedBy = "";
        for (int i = 0; i < contactsList.size(); i++) {
            contactsList.get(i).setBelonging("商机");
            contactsList.get(i).setBusinessId(nicheNo);
            if (StringUtils.isNotBlank(contactsList.get(i).getContactsOwner())) {
                sharedBy += contactsList.get(i).getContactsOwner() + ",";
                byList.add(contactsList.get(i).getContactsOwner());
            }
        }
        diMarketingNiche.setSharedBy(sharedBy);
        /*if (StringUtils.isEmpty(diMarketingNiche.getProjectName())) {
            String nicheSuffix = nicheNo.split("-")[nicheNo.split("-").length - 1];
            String companyName = "";
            if (Objects.nonNull(diMarketingNiche.getDiMarketingCustomer()) && StringUtils.isNotBlank(diMarketingNiche.getDiMarketingCustomer().getCustomerNo())) {
                companyName = diMarketingNiche.getDiMarketingCustomer().getCompanyName();
            }
            String MMDD = getCurDateFormatter("MM") + getCurDateFormatter("dd");
            String projectName = String.format("%s项目%s-%s", "未命名", MMDD, nicheSuffix);
            diMarketingNiche.setProjectName(projectName);
        }*/

        int result = diMarketingNicheMapper.insertDiMarketingNiche(diMarketingNiche);
        Map<String, String> map = new HashMap<>();
        map.put("id", diMarketingNiche.getId());
        map.put("nicheNo", nicheNo);

        //todo 新增需求信息
        if (null != diMarketingNiche.getDiMarketingNicheDemand()) {
            addNicheDemand(diMarketingNiche);
            technicalSupportDingTalk(diMarketingNiche);
        }

        //新增联系人
        if (CollectionUtils.isEmpty(contactsList)) {
            return map;
        }
        diMarketingContactsService.editDiMarketingContacts(contactsList);
        //增加待办任务(待办任务，勿删)
        saveAgencyTaskSendMessage(byList, diMarketingNiche, 1);
        //判断是否是线索转商机
        if (!"3".equals(diMarketingNiche.getNicheSource())) {
            saveSendDingTalkMessage(diMarketingNiche);
        }


        if (diProjectRelationService.count(Wrappers.lambdaQuery(DiProjectRelation.class).eq(DiProjectRelation::getRelationNo, diMarketingNiche.getNicheNo()).eq(DiProjectRelation::getRelationType, RelationTypeEnum.BUSINESS.getRelationType())) == 0) {
            try {
                ProcessProjectAddVO processProjectAddVO = new ProcessProjectAddVO();
                processProjectAddVO.setSource(1);
                processProjectAddVO.setProductType(0L);
                processProjectAddVO.setNicheNo(diMarketingNiche.getNicheNo());
                processProjectAddVO.setSalesDeptId(NumberUtils.toLong(diMarketingNiche.getNicheOwnerDept()));
                processProjectAddVO.setSaleUserId(diMarketingNiche.getNicheOwner());
                processProjectAddVO.setProjectName(diMarketingNiche.getProjectName());
                iDiProcessProjectService.addProcessProject(processProjectAddVO);
                AfterCommitExecutor.submit(() ->
                        checkProcessProjectStatusJob.check(processProjectAddVO.getNewProjectNo()), null);
            } catch (Exception e) {
                log.error("立项失败", e);
            }
        }

        return map;
    }

    private String getCurDateFormatter(String formatter) {
        DateTimeFormatter monthFormatter = DateTimeFormatter.ofPattern(formatter);
        LocalDate currentDate = LocalDate.now();
        return currentDate.format(monthFormatter);
    }

    public void technicalSupportDingTalk(DiMarketingNiche diMarketingNiche) {
        //发送钉钉消息并且生成待办任务
        if (CollectionUtil.isEmpty(diMarketingNiche.getDiMarketingNicheDemandIntentionList())) {
            return;
        }
        List<String> codeList = new ArrayList<>();
        for (DiMarketingNicheDemandIntention nicheDemandIntention : diMarketingNiche.getDiMarketingNicheDemandIntentionList()) {
            if (CollectionUtil.isEmpty(nicheDemandIntention.getIndustryInfo())) {
                continue;
            }
            codeList.add(nicheDemandIntention.getIndustryInfo().get(nicheDemandIntention.getIndustryInfo().size() - 1));
        }
        if (CollectionUtil.isEmpty(codeList)) {
            return;
        }
        Map<String, String> dictMap = remoteDictDataService.queryAsMap("industry_owner");
        if (CollectionUtil.isEmpty(dictMap)) {
            return;
        }
        List<String> sendUserList = new ArrayList<>();
        codeList.forEach(code -> {
            if (StringUtils.isNotBlank(dictMap.get(code))) {
                sendUserList.addAll(Arrays.stream(dictMap.get(code).split(",")).toList());
            }
        });
        if (CollectionUtil.isNotEmpty(sendUserList)) {
            return;
        }
        List<String> sendUsers = sendUserList.stream().distinct().toList();
        sendUsers.forEach(sendUser -> {
            String title = StrUtil.format(NicheDingTalkEnum.NICHE_TECHNICAL_SUPPORT_APPLY.getTitle(), diMarketingNiche.getNicheNo());
            String content = StrUtil.format(NicheDingTalkEnum.NICHE_TECHNICAL_SUPPORT_APPLY.getMessage(),
                    StringUtils.isNotBlank(diMarketingNiche.getCompanyName()) ? diMarketingNiche.getNicheNo() + "，客户名称：" + diMarketingNiche.getCompanyName() : diMarketingNiche.getNicheNo(),
                    SecurityUtils.getLoginUser().getSysUser().getNickName());
            DiMessageList diMessageList = new DiMessageList();
            diMessageList.setBusinessModule("商机-技术支持");
            diMessageList.setTitle(title);
            diMessageList.setSendingTime(new Date());
            diMessageList.setRemarks(diMessageList.getTitle());
            diMessageList.setContent(content);
            diMessageList.setSendingUser(sendUser);
            iDiMessageListService.insertDiMessageList(diMessageList);
        });
        saveAgencyTaskSendMessage(sendUsers, diMarketingNiche, 2);
    }

    /**
     * 修改商机
     *
     * @param diMarketingNiche 商机
     * @return 结果
     */
    @Override
    @Transactional
    public int updateDiMarketingNiche(DiMarketingNiche diMarketingNiche) {
        //获取商机信息
        DiMarketingNiche niche = diMarketingNicheMapper.selectDiMarketingNicheById(diMarketingNiche.getId());
        if (null == niche) {
            throw new ServiceException("商机不存在");
        }
        //获取老的销售
        String oldNicheOwner = null;
        if (StringUtils.isNotBlank(niche.getNicheOwner())) {
            oldNicheOwner = niche.getNicheOwner();
        }
        //获取老的共享销售
        String oldSharedBy = null;
        if (StringUtils.isNotBlank(niche.getSharedBy())) {
            oldSharedBy = niche.getSharedBy();
        }
        diMarketingNiche.setCreateBy(null);
        diMarketingNiche.setUpdateBy(SecurityUtils.getUsername());
        diMarketingNiche.setUpdateTime(DateUtils.getNowDate());

        List<DiMarketingContacts> contactsList = diMarketingNiche.getDiMarketingContactsList();
        //记录共享销售和主要销售(待办任务，勿删)
        List<String> byList = new ArrayList<>();
        byList.add(diMarketingNiche.getNicheOwner());
        String sharedBy = "";
        if (!CollectionUtils.isEmpty(contactsList)) {
            for (int i = 0; i < contactsList.size(); i++) {
                contactsList.get(i).setBelonging("商机");
                contactsList.get(i).setBusinessId(diMarketingNiche.getNicheNo());
                if (StringUtils.isNotBlank(contactsList.get(i).getContactsOwner())) {
                    sharedBy += contactsList.get(i).getContactsOwner() + ",";
                    byList.add(contactsList.get(i).getContactsOwner());
                }
            }
            diMarketingNiche.setSharedBy(sharedBy);
        }

        int result = diMarketingNicheMapper.updateDiMarketingNiche(diMarketingNiche);

        //判断商机状态，如果是丢单和放弃修改待办任务(待办任务，勿删)[成单放在订单那里处理]
        if ("4".equals(diMarketingNiche.getNicheStatus()) || "5".equals(diMarketingNiche.getNicheStatus())) {
            updateAgencyTaskSendMessage(niche.getNicheNo(), 1);
            updateAgencyTaskSendMessage(niche.getNicheNo(), 2);
            //销售报价单、产品方案、合同状态修改
            iDiContractService.discardProcess(niche.getNicheNo());
            //去除卡片视角
            diProcessProjectService.updateCardFlagProcessProject(niche.getNicheNo());
            //判断是否开启可行性验证审批，如果开启，则将审批流取消并删除待办审批
            if (NicheApprovalConstants.STR_SIX.equals(diMarketingNiche.getNicheStatus()) && NicheApprovalConstants.STR_TWO.equals(diMarketingNiche.getFeasibilityVerifyState()) && StringUtils.isNotBlank(diMarketingNiche.getFeasibilityVerifyProcessId())) {
                closeFeasibilityVerify(diMarketingNiche.getId(), diMarketingNiche.getNicheNo(), diMarketingNiche.getFeasibilityVerifyProcessId());
            } else {
                log.info("DiMarketingNicheServiceImpl---updateDiMarketingNiche()---商机不满足关闭可行性验证流程条件，商机号：{}", diMarketingNiche.getNicheNo());
            }
        } else {
            //普通修改，增加待办任务
            saveAgencyTaskSendMessage(byList, diMarketingNiche, 1);
        }

        //编辑商机发送钉钉消息
        updateSendDingTalkMessage(diMarketingNiche, niche);
        if (CollectionUtils.isEmpty(diMarketingNiche.getDiMarketingContactsList())) {
            return result;
        }
        //todo 删除商机需求再新增
        if (null != diMarketingNiche.getDiMarketingNicheDemand()) {
            editNicheDemand(diMarketingNiche);
        }
        diMarketingContactsService.editDiMarketingContacts(diMarketingNiche.getDiMarketingContactsList());
        //销售发生变更同步项目参与人
        synchroSale(diMarketingNiche, oldNicheOwner, oldSharedBy);
        //
        syncProjectCustomerNo(diMarketingNiche, niche);
        return result;
    }

    private void editNicheDemand(DiMarketingNiche diMarketingNiche) {
        /*DiMarketingNicheDemand diMarketingNicheDemand = diMarketingNicheDemandService.getOne(Wrappers.<DiMarketingNicheDemand>lambdaQuery().eq(DiMarketingNicheDemand::getNicheNo, diMarketingNiche.getNicheNo()));
        diMarketingNiche.getDiMarketingNicheDemand().setId(diMarketingNicheDemand.getId());
        diMarketingNiche.getDiMarketingNicheDemand().setNicheNo(diMarketingNicheDemand.getNicheNo());
        diMarketingNiche.getDiMarketingNicheDemand().setCreateBy(diMarketingNicheDemand.getCreateBy());
        diMarketingNiche.getDiMarketingNicheDemand().setCreateTime(diMarketingNicheDemand.getCreateTime());
        diMarketingNiche.getDiMarketingNicheDemand().setUpdateBy(SecurityUtils.getUsername());
        diMarketingNiche.getDiMarketingNicheDemand().setUpdateTime(DateUtils.getNowDate());
        diMarketingNicheDemandService.updateById(diMarketingNiche.getDiMarketingNicheDemand());*/
        diMarketingNicheDemandService.remove(Wrappers.<DiMarketingNicheDemand>lambdaQuery().eq(DiMarketingNicheDemand::getNicheNo, diMarketingNiche.getNicheNo()));
        diMarketingNicheDemandFileService.remove(Wrappers.<DiMarketingNicheDemandFile>lambdaQuery().eq(DiMarketingNicheDemandFile::getNicheNo, diMarketingNiche.getNicheNo()));
        diMarketingNicheDemandIntentionService.remove(Wrappers.<DiMarketingNicheDemandIntention>lambdaQuery().eq(DiMarketingNicheDemandIntention::getNicheNo, diMarketingNiche.getNicheNo()));
        diMarketingNicheRequirementAccessoryService.remove(Wrappers.<DiMarketingNicheRequirementAccessory>lambdaQuery().eq(DiMarketingNicheRequirementAccessory::getNicheNo, diMarketingNiche.getNicheNo()));
        diMarketingNicheRequirementCombustorService.remove(Wrappers.<DiMarketingNicheRequirementCombustor>lambdaQuery().eq(DiMarketingNicheRequirementCombustor::getNicheNo, diMarketingNiche.getNicheNo()));
        diMarketingNicheRequirementElectrollService.remove(Wrappers.<DiMarketingNicheRequirementElectroll>lambdaQuery().eq(DiMarketingNicheRequirementElectroll::getNicheNo, diMarketingNiche.getNicheNo()));
        diMarketingNicheRequirementHotstoveService.remove(Wrappers.<DiMarketingNicheRequirementHotstove>lambdaQuery().eq(DiMarketingNicheRequirementHotstove::getNicheNo, diMarketingNiche.getNicheNo()));
        diMarketingNicheRequirementValveService.remove(Wrappers.<DiMarketingNicheRequirementValve>lambdaQuery().eq(DiMarketingNicheRequirementValve::getNicheNo, diMarketingNiche.getNicheNo()));
        addNicheDemand(diMarketingNiche);
    }

    private void addNicheDemand(DiMarketingNiche diMarketingNiche) {
        String nicheNo = diMarketingNiche.getNicheNo();
        diMarketingNiche.getDiMarketingNicheDemand().setNicheNo(nicheNo);
        diMarketingNicheDemandService.save(diMarketingNiche.getDiMarketingNicheDemand());
        Long demandId = diMarketingNiche.getDiMarketingNicheDemand().getId();

        List<DiMarketingNicheDemandFile> diMarketingNicheDemandFileList = diMarketingNiche.getDiMarketingNicheDemandFileList();
        if (!CollectionUtils.isEmpty(diMarketingNicheDemandFileList)) {
            diMarketingNicheDemandFileList = diMarketingNicheDemandFileList.stream().map((item) -> {
                item.setDemandId(demandId);
                item.setNicheNo(nicheNo);
                item.setCreateBy(SecurityUtils.getUsername());
                item.setCreateTime(DateUtils.getNowDate());
                return item;
            }).collect(Collectors.toList());
            diMarketingNicheDemandFileService.saveBatch(diMarketingNicheDemandFileList);
        }

        List<DiMarketingNicheDemandIntention> diMarketingNicheDemandIntentionList = diMarketingNiche.getDiMarketingNicheDemandIntentionList();
        if (!CollectionUtils.isEmpty(diMarketingNicheDemandIntentionList)) {
            diMarketingNicheDemandIntentionList = diMarketingNicheDemandIntentionList.stream().map((item) -> {
                item.setDemandId(demandId);
                item.setNicheNo(nicheNo);
                item.setCreateBy(SecurityUtils.getUsername());
                item.setCreateTime(DateUtils.getNowDate());
                return item;
            }).collect(Collectors.toList());
            diMarketingNicheDemandIntentionService.saveBatch(diMarketingNicheDemandIntentionList);
        }

        List<DiMarketingNicheRequirementAccessory> diMarketingNicheRequirementAccessoryList = diMarketingNiche.getDiMarketingNicheRequirementAccessoryList();
        if (!CollectionUtils.isEmpty(diMarketingNicheRequirementAccessoryList)) {
            diMarketingNicheRequirementAccessoryList = diMarketingNicheRequirementAccessoryList.stream().map((item) -> {
                item.setDemandId(demandId);
                item.setNicheNo(nicheNo);
                item.setCreateBy(SecurityUtils.getUsername());
                item.setCreateTime(DateUtils.getNowDate());
                return item;
            }).collect(Collectors.toList());
            diMarketingNicheRequirementAccessoryService.saveBatch(diMarketingNicheRequirementAccessoryList);
        }

        List<DiMarketingNicheRequirementCombustor> diMarketingNicheRequirementCombustorList = diMarketingNiche.getDiMarketingNicheRequirementCombustorList();
        if (!CollectionUtils.isEmpty(diMarketingNicheRequirementCombustorList)) {
            diMarketingNicheRequirementCombustorList = diMarketingNicheRequirementCombustorList.stream().map((item) -> {
                item.setDemandId(demandId);
                item.setNicheNo(nicheNo);
                item.setCreateBy(SecurityUtils.getUsername());
                item.setCreateTime(DateUtils.getNowDate());
                return item;
            }).collect(Collectors.toList());
            diMarketingNicheRequirementCombustorService.saveBatch(diMarketingNicheRequirementCombustorList);
        }

        List<DiMarketingNicheRequirementElectroll> diMarketingNicheRequirementElectrollList = diMarketingNiche.getDiMarketingNicheRequirementElectrollList();
        if (!CollectionUtils.isEmpty(diMarketingNicheRequirementElectrollList)) {
            diMarketingNicheRequirementElectrollList = diMarketingNicheRequirementElectrollList.stream().map((item) -> {
                item.setDemandId(demandId);
                item.setNicheNo(nicheNo);
                item.setCreateBy(SecurityUtils.getUsername());
                item.setCreateTime(DateUtils.getNowDate());
                return item;
            }).collect(Collectors.toList());
            diMarketingNicheRequirementElectrollService.saveBatch(diMarketingNicheRequirementElectrollList);
        }

        List<DiMarketingNicheRequirementHotstove> diMarketingNicheRequirementHotstoveList = diMarketingNiche.getDiMarketingNicheRequirementHotstoveList();
        if (!CollectionUtils.isEmpty(diMarketingNicheRequirementHotstoveList)) {
            diMarketingNicheRequirementHotstoveList = diMarketingNicheRequirementHotstoveList.stream().map((item) -> {
                item.setDemandId(demandId);
                item.setNicheNo(nicheNo);
                item.setCreateBy(SecurityUtils.getUsername());
                item.setCreateTime(DateUtils.getNowDate());
                return item;
            }).collect(Collectors.toList());
            diMarketingNicheRequirementHotstoveService.saveBatch(diMarketingNicheRequirementHotstoveList);
        }

        List<DiMarketingNicheRequirementValve> diMarketingNicheRequirementValveList = diMarketingNiche.getDiMarketingNicheRequirementValveList();
        if (!CollectionUtils.isEmpty(diMarketingNicheRequirementValveList)) {
            diMarketingNicheRequirementValveList = diMarketingNicheRequirementValveList.stream().map((item) -> {
                item.setDemandId(demandId);
                item.setNicheNo(nicheNo);
                item.setCreateBy(SecurityUtils.getUsername());
                item.setCreateTime(DateUtils.getNowDate());
                return item;
            }).collect(Collectors.toList());
            diMarketingNicheRequirementValveService.saveBatch(diMarketingNicheRequirementValveList);
        }
    }

    private void syncProjectCustomerNo(DiMarketingNiche diMarketingNiche, DiMarketingNiche niche) {
        if (null == diMarketingNiche || StringUtils.isBlank(diMarketingNiche.getNicheStatus())) {
            return;
        }
        //修改状态不处理
        if (!diMarketingNiche.getNicheStatus().equals(niche.getNicheStatus())) {
            return;
        }
        DiProjectRelation relation = diProjectRelationService.getOne(new LambdaQueryWrapper<DiProjectRelation>().eq(DiProjectRelation::getRelationType, RelationTypeEnum.BUSINESS).eq(DiProjectRelation::getRelationNo, diMarketingNiche.getNicheNo()));
        //无归属项目
        if (Objects.isNull(relation)) {
            return;
        }
        diProcessProjectService.updateProjectCustomerNo(relation.getProjectNo(), diMarketingNiche.getCustomerNo());
    }

    private void synchroSale(DiMarketingNiche diMarketingNiche, String oldNicheOwner, String oldSharedBy) {
        if (StringUtils.isBlank(diMarketingNiche.getNicheNo())) {
            return;
        }
        //根据商机号获取项目编号
        DiProjectRelation relation = diProjectRelationService.getOne(new LambdaQueryWrapper<DiProjectRelation>().eq(DiProjectRelation::getRelationType, RelationTypeEnum.BUSINESS).eq(DiProjectRelation::getRelationNo, diMarketingNiche.getNicheNo()));
        if (null == relation) {
            log.info("DiMarketingNicheServiceImpl---synchroSale()---没有获取到商机关联的项目数据,商机号：{}", diMarketingNiche.getNicheNo());
            return;
        }
        //需要删除的销售
        List<String> oldPersonnelList = new ArrayList<>();
        //需要新增的销售
        List<String> newPersonnelList = new ArrayList<>();
        //判断原本是否有销售
        if (StringUtils.isNotBlank(oldNicheOwner)) {
            //将原本的销售删除
            oldPersonnelList.add(oldNicheOwner);
        }
        if (StringUtils.isNotBlank(oldSharedBy)) {
            String[] oldSharedByArr = oldSharedBy.split(",");
            oldPersonnelList.addAll(Arrays.asList(oldSharedByArr));
        }
        if (StringUtils.isNotBlank(diMarketingNiche.getNicheOwner())) {
            //将本次修改的销售维护到项目参与人中
            newPersonnelList.add(diMarketingNiche.getNicheOwner());
        }
        if (!CollectionUtils.isEmpty(diMarketingNiche.getDiMarketingContactsList())) {
            List<String> contactsOwnerList = diMarketingNiche.getDiMarketingContactsList().stream().map(DiMarketingContacts::getContactsOwner).filter(StringUtils::isNotBlank).toList();
            newPersonnelList.addAll(contactsOwnerList);
        }
        if (!CollectionUtils.isEmpty(oldPersonnelList)) {
            commonService.personnelSaveProject(relation.getProjectNo(), oldPersonnelList, "2");
        }
        if (!CollectionUtils.isEmpty(newPersonnelList)) {
            commonService.personnelSaveProject(relation.getProjectNo(), newPersonnelList, "0");
        }
    }

    /**
     * 批量删除商机
     *
     * @param ids 需要删除的商机主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteDiMarketingNicheByIds(String[] ids) {
        for (String id : ids) {
            DiMarketingNiche diMarketingNiche = diMarketingNicheMapper.selectDiMarketingNicheById(id);
            diMarketingContactsService.deleteDiMarketingContacts(diMarketingNiche.getNicheNo());
        }
        int result = diMarketingNicheMapper.deleteDiMarketingNicheByIds(ids);
        return result;
    }

    @Override
    public PageWrapper<List<DiMarketingNicheListResponse>> getNichePage(Integer pageNum, Integer pageSize) {
        Page page = PageHelper.startPage(pageNum, pageSize);
        DiMarketingNiche diMarketingNicheT = new DiMarketingNiche();
        diMarketingNicheT.setDelFlag("0");
        List<DiMarketingNiche> diMarketingNiches = diMarketingNicheMapper.selectDiMarketingNicheListNoAuth(diMarketingNicheT);
        R<List<SysDictData>> nicheStatusR = remoteDictDataService.dictTypeNew("niche_status");
        Map<String, List<SysDictData>> dictMap = new HashMap<>();
        if (nicheStatusR.isSuccess() && CollectionUtil.isNotEmpty(nicheStatusR.getData())) {
            dictMap = nicheStatusR.getData().stream().collect(Collectors.groupingBy(SysDictData::getDictValue));
        }
        if (!CollectionUtils.isEmpty(diMarketingNiches)) {
            Map<String, List<SysDictData>> finalDictMap = dictMap;
            List<DiMarketingNicheListResponse> diMarketingNicheListResponses = diMarketingNiches.stream().map(diMarketingNiche -> {
                DiMarketingNicheListResponse diMarketingNicheListResponse = cusConverUtil.converToNicheListResponse(diMarketingNiche);
                if (CollectionUtil.isNotEmpty(finalDictMap.get(diMarketingNiche.getNicheStatus()))) {
                    diMarketingNicheListResponse.setNicheStatus(finalDictMap.get(diMarketingNiche.getNicheStatus()).get(0).getDictLabel());
                }
                return diMarketingNicheListResponse;
            }).collect(Collectors.toList());
            return PageHelp.render(page, diMarketingNicheListResponses);
        }
        return PageHelp.render(page, null);
    }

    /**
     * 获取字典值的负责人
     *
     * @param dictPersonCharge
     * @return
     */
    @Override
    public DictPersonChargeVo getDictPersonChargeName(DictPersonCharge dictPersonCharge) {
        DictPersonChargeVo vo = new DictPersonChargeVo();
        String userNo = diMarketingNicheMapper.getDictPersonChargeName(dictPersonCharge);
        vo.setDictPersonChargeNo(userNo);
        vo.setDictPersonChargeName(getUserName(userNo));
        return vo;
    }

    public String getUserName(String userNo) {
        Map<String, String> userNameMap = Maps.newHashMap();
        List<String> list = new ArrayList<>();
        list.add(Optional.ofNullable(userNo).orElse(""));

        List<String> checkList = ListUtils.emptyIfNull(list).stream().filter(StringUtils::isNoneEmpty).toList();
        R<List<DdUserDTO>> userListResult = remoteDbcService.queryDdUserList(QueryDdUserDTO.builder().jobNumberList(checkList).build());
        if (userListResult.isSuccess()) {
            userNameMap = userListResult.getData().stream().filter(item -> Objects.nonNull(item.getJobNumber()))
                    .collect(Collectors.toMap(DdUserDTO::getJobNumber, DdUserDTO::getName));
            return userNameMap.get(userNo);
        }
        return null;
    }

    /**
     * 删除商机信息
     *
     * @param id 商机主键
     * @return 结果
     */
    @Override
    public int deleteDiMarketingNicheById(String id) {
        return diMarketingNicheMapper.deleteDiMarketingNicheById(id);
    }


    @Override
    public DiMarketingNiche selectDiMarketingNicheByNo(String nicheNo) {
        DiMarketingNiche diMarketingNiche = diMarketingNicheMapper.selectDiMarketingNicheById(nicheNo);
        if (null != diMarketingNiche) {
            DiMarketingContacts diMarketingContacts = new DiMarketingContacts();
            diMarketingContacts.setBusinessId(diMarketingNiche.getNicheNo());
            diMarketingNiche.setDiMarketingContactsList(diMarketingContactsService.selectDiMarketingContactsList(diMarketingContacts));

            //客户
            DiMarketingCustomer diMarketingCustomer = diMarketingCustomerMapper.selectDiMarketingCustomerById(diMarketingNiche.getCustomerNo());
            if (Objects.nonNull(diMarketingCustomer)) {
                diMarketingNiche.setCompanyName(diMarketingCustomer.getCompanyName());
            }
        }
        return diMarketingNiche;
    }

    @Override
    public List<DiMarketingNiche> selectDiMarketingNicheByNoCustomerNo(String customerNo) {
        return diMarketingNicheMapper.selectDiMarketingNicheByNoCustomerNo(customerNo);
    }

    /**
     * 修改商机状态
     *
     * @param nicheNo 商机号
     * @param status  状态
     */
    @Override
    public void updateNicheStatus(String nicheNo, String status) {
        DiMarketingNiche niche = diMarketingNicheMapper.selectDiMarketingNicheById(nicheNo);
        if (null == niche) {
            log.error("DiMarketingNicheServiceImpl---updateNicheStatus()---商机不存在，商机号：{}", nicheNo);
            return;
        }
        if (niche.getNicheStatus().equals(status)) {
            log.error("DiMarketingNicheServiceImpl---updateNicheStatus()---商机状态未改变，商机号：{}", nicheNo);
            return;
        }
        DiMarketingNiche diMarketingNiche = new DiMarketingNiche();
        if (status.equals("8")) {
            diMarketingNiche.setSubmitProposalDate(DateUtils.getNowDate());
        }
        diMarketingNiche.setId(niche.getId());
        diMarketingNiche.setNicheStatus(status);
        niche.setNicheStatus(status);
        diMarketingNiche.setUpdateBy(SecurityUtils.getUsername());
        diMarketingNiche.setUpdateTime(DateUtils.getNowDate());
        diMarketingNicheMapper.updateDiMarketingNiche(diMarketingNiche);
        if (NicheStatusEnum.SOLUTION_STAGE.getCode().equals(status)) {
            DiMarketingNicheDemand nicheDemand = diMarketingNicheDemandService.queryByNicheNo(nicheNo);
            if (Objects.nonNull(nicheDemand)) {
                String user;
                if (StringUtils.isNotBlank(nicheDemand.getDictPersonChargeNo())) {
                    user = nicheDemand.getDictPersonChargeNo();
                } else {
                    List<String> codeList = new ArrayList<>();
                    structureCode(diMarketingNiche, codeList);
                    if (CollectionUtil.isEmpty(codeList)) {
                        log.info("DiMarketingNicheServiceImpl---updateNicheStatus---商机_销售需求明细未填写行业/应用/场景或品类");
                        return;
                    }
                    Map<String, String> dictMap = getOwnerInfo(diMarketingNiche);
                    if (CollectionUtil.isEmpty(dictMap)) {
                        log.info("DiMarketingNicheServiceImpl---updateNicheStatus---未获取到对应负责人");
                        return;
                    }
                    List<String> sendUserList = new ArrayList<>();
                    codeList.forEach(code -> {
                        if (StringUtils.isNotBlank(dictMap.get(code))) {
                            sendUserList.addAll(Arrays.stream(dictMap.get(code).split(",")).toList());
                        }
                    });
                    if (CollectionUtil.isEmpty(sendUserList)) {
                        log.info("DiMarketingNicheServiceImpl---updateNicheStatus---发送人为空");
                        return;
                    }
                    user = sendUserList.stream().distinct().toList().get(0);
                }
                this.saveAgencyTaskSendMessage(Collections.singletonList(user), niche, 2);
                diAgencyTaskService.clearTask(niche.getNicheNo(), AgencyTaskTypeEnum.NICHE_MANAGE_QUOTE);
                DiMessageList diMessageList = new DiMessageList();
                diMessageList.setBusinessModule("商机-技术支持");
                String content = StrUtil.format(NicheDingTalkEnum.NICHE_TECHNICAL_SUPPORT_APPLY.getMessage(),
                        StringUtils.isNotBlank(niche.getCompanyName()) ? niche.getNicheNo() + "，客户名称：" + niche.getCompanyName() : niche.getNicheNo(),
                        SecurityUtils.getLoginUser().getSysUser().getNickName());
                String title = StrUtil.format(NicheDingTalkEnum.NICHE_TECHNICAL_SUPPORT_APPLY.getTitle(), niche.getNicheNo());
                diMessageList.setTitle(title);
                diMessageList.setSendingTime(new Date());
                diMessageList.setRemarks(diMessageList.getTitle());
                diMessageList.setContent(content);
                diMessageList.setSendingUser(user);
                iDiMessageListService.insertDiMessageList(diMessageList);
            }
        }
        if (NicheStatusEnum.QUOTE_STAGE.getCode().equals(status)) {
            niche.setNicheStatus("8");
            this.saveAgencyTaskSendMessage(Collections.singletonList(niche.getNicheOwner()), niche, 3);
            DiMessageList diMessageList = new DiMessageList();
            diMessageList.setBusinessModule("商机");
            String title = StrUtil.format(NicheDingTalkEnum.NICHE_UPDATE_QUOTE_STATE.getTitle(), niche.getNicheNo(), niche.getProjectName());
            String message = StrUtil.format(NicheDingTalkEnum.NICHE_UPDATE_QUOTE_STATE.getMessage(), niche.getNicheNo(), niche.getProjectName());
            diMessageList.setTitle(title);
            diMessageList.setSendingTime(new Date());
            diMessageList.setRemarks(diMessageList.getTitle());
            diMessageList.setContent(message);
            diMessageList.setSendingUser(niche.getNicheOwner());
            iDiMessageListService.insertDiMessageList(diMessageList);
        }
    }

    private void structureCode(DiMarketingNiche diMarketingNiche, List<String> codeList) {
        if (diMarketingNiche.getDiMarketingNicheDemand() == null) {
            return;
        }
        if (1 == diMarketingNiche.getDiMarketingNicheDemand().getDemandType()) {
            if (StringUtils.isNotBlank(diMarketingNiche.getDiMarketingNicheDemand().getScene())) {
                codeList.add(diMarketingNiche.getDiMarketingNicheDemand().getScene());
            } else if (StringUtils.isNotBlank(diMarketingNiche.getDiMarketingNicheDemand().getApplication())) {
                codeList.add(diMarketingNiche.getDiMarketingNicheDemand().getApplication());
            } else if (StringUtils.isNotBlank(diMarketingNiche.getDiMarketingNicheDemand().getIndustry())) {
                codeList.add(diMarketingNiche.getDiMarketingNicheDemand().getIndustry());
            }
        }
        if (2 == diMarketingNiche.getDiMarketingNicheDemand().getDemandType()) {
            if (StringUtils.isNotBlank(diMarketingNiche.getDiMarketingNicheDemand().getFuelAccessoryCategoryName())) {
                codeList.add(diMarketingNiche.getDiMarketingNicheDemand().getFuelAccessoryCategoryName());
            }
        }
        if (3 == diMarketingNiche.getDiMarketingNicheDemand().getDemandType()) {
            if (StringUtils.isNotBlank(diMarketingNiche.getDiMarketingNicheDemand().getFlameCategoryName())) {
                codeList.add(diMarketingNiche.getDiMarketingNicheDemand().getFlameCategoryName());
            }
        }
    }

    private Map<String, String> getOwnerInfo(DiMarketingNiche diMarketingNiche) {
        if (1 == diMarketingNiche.getDiMarketingNicheDemand().getDemandType()) {
            return remoteDictDataService.queryAsMap("industry_owner");
        }
        if (2 == diMarketingNiche.getDiMarketingNicheDemand().getDemandType()) {
            return remoteDictDataService.queryAsMap("fuel_accessory_owner");
        }
        if (3 == diMarketingNiche.getDiMarketingNicheDemand().getDemandType()) {
            return remoteDictDataService.queryAsMap("flame_owner");
        }
        return null;
    }

    /**
     * 根据id获取编号获取商机
     *
     * @param noOrId 商机ID或编号
     * @return 商机信息
     */
    @Override
    public DiMarketingNiche getMarketingNicheByNoRoId(String noOrId) {
        return diMarketingNicheMapper.selectDiMarketingNicheById(noOrId);
    }

    /**
     * 修改状态发送钉钉消息
     *
     * @param diMarketingNiche 商机信息
     * @param content          发送内容
     */
    @Override
    public void updateNicheStatusSendDingTalk(DiMarketingNiche diMarketingNiche, String content) {
        DiMessageList message = formMessageInfo(diMarketingNiche.getNicheNo(), content);
        //构建发送人信息
        List<String> sendByList = new ArrayList<>();
        //商机创建人默认发送
        sendByList.add(diMarketingNiche.getCreateBy());
        if (StringUtils.isNotBlank(diMarketingNiche.getNicheOwner())) {
            sendByList.add(diMarketingNiche.getNicheOwner());
        }
        //判断是否有共享销售
        if (StringUtils.isNotBlank(diMarketingNiche.getSharedBy())) {
            String[] sharedBys = diMarketingNiche.getSharedBy().split(",");
            List<String> sharedByList = Arrays.stream(sharedBys).distinct().toList();
            sendByList.addAll(sharedByList);
        }
        //发送钉钉
        List<String> sendBys = sendByList.stream().distinct().toList();
        for (String sendBy : sendBys) {
            message.setSendingUser(sendBy);
            iDiMessageListService.insertDiMessageList(message);
        }
    }

    /**
     * code转id
     *
     * @param code
     * @return
     */
    @Override
    public String convertCode2Id(String code) {
        DiMarketingNiche niche = this.selectDiMarketingNicheByNo(code);
        if (niche == null) {
            return null;
        }
        return niche.getId();
    }


    @Override
    public List<? extends Object> getStockUpList(String dataId) {

        /*if (StringUtils.isNotEmpty(dataId)){
            condList.add(Cond.builder().field("data_id").value(dataId).method(JdyConstants.JDY_EQ).build());
        }*/

        StockUpDataV5Request stockUpDataV5Request = new StockUpDataV5Request();
        StockUpDataV5OneRequest stockUpDataV5OneRequest = new StockUpDataV5OneRequest();
        stockUpDataV5Request.setApp_id("604877a3d4f7d50007d855fd");
        stockUpDataV5Request.setEntry_id("60658ac37989250007617d24");
        stockUpDataV5Request.setLimit(1000);
        stockUpDataV5OneRequest.setApp_id("604877a3d4f7d50007d855fd");
        stockUpDataV5OneRequest.setEntry_id("60658ac37989250007617d24");
        stockUpDataV5OneRequest.setLimit(1000);

        JdyCommonDto.Field field = new JdyCommonDto.Field();
        field.setField("_widget_1631947922603");
        field.setMethod("not_empty");
        JdyCommonDto.Filter filter = new JdyCommonDto.Filter();
        filter.setCond(Arrays.asList(field));
        filter.setRel(JdyConstants.AND);
        stockUpDataV5Request.setFilter(filter);
        stockUpDataV5OneRequest.setFilter(filter);

        if (StringUtils.isNotEmpty(dataId)) {
            stockUpDataV5OneRequest.setUrlName(JdyConstants.ONE_QUERY_URL);
            stockUpDataV5OneRequest.setData_id(dataId);
            log.info("请求参数{}", JSON.toJSONString(stockUpDataV5Request));
            StockUpDataV5OneResponse stockUpDataResponse = jdyClient.jdyCallV5(stockUpDataV5OneRequest);
            log.info("返回参数{}", JSON.toJSONString(stockUpDataResponse));

            if (Objects.nonNull(stockUpDataResponse.getData()) && CollectionUtil.isNotEmpty(stockUpDataResponse.getData().get_widget_1617672986486())) {
                for (StockUpDataV5OneResponse._widget_1617672986486 widget_1617672986486 : stockUpDataResponse.getData().get_widget_1617672986486()) {
                    if (StringUtils.isNotEmpty(widget_1617672986486.get_widget_1617676047085())) {
                        widget_1617672986486.set_widget_1617676047085(DateUtils.utcToLocalDate(widget_1617672986486.get_widget_1617676047085()).toString());
                    }

                }
            }

            return Arrays.asList(stockUpDataResponse.getData());
        } else {
            stockUpDataV5Request.setUrlName(JdyConstants.MORE_QUERY_URL);
            log.info("请求参数{}", JSON.toJSONString(stockUpDataV5Request));
            StockUpDataResponse stockUpDataResponse = jdyClient.jdyCallV5(stockUpDataV5Request);
            log.info("返回参数{}", JSON.toJSONString(stockUpDataResponse));

            if (CollectionUtil.isNotEmpty(stockUpDataResponse.getData())) {
                for (StockUpDataResponse.FromData fromData : stockUpDataResponse.getData()) {
                    if (CollectionUtil.isNotEmpty(fromData.get_widget_1617672986486())) {
                        for (StockUpDataResponse._widget_1617672986486 widget_1617672986486 : fromData.get_widget_1617672986486()) {
                            if (StringUtils.isNotEmpty(widget_1617672986486.get_widget_1617676047085())) {
                                widget_1617672986486.set_widget_1617676047085(DateUtils.utcToLocalDate(widget_1617672986486.get_widget_1617676047085()).toString());
                            }
                        }
                    }

                }
            }
            return stockUpDataResponse.getData();
        }

    }

    /**
     * 根据商机编号获取商机需求
     *
     * @param nicheCode
     * @return
     */
    @Override
    public DiMarketingNicheDemand selectNicheDemandByNo(String nicheCode) {
        String id = this.convertCode2Id(nicheCode);
        return this.diMarketingNicheDemandService.selectByNicheId(Long.parseLong(id));
    }

    /**
     * 发送新增待办任务消息
     *
     * @param byList           销售人员
     * @param diMarketingNiche 商机信息
     */
    @Override
    public void saveAgencyTaskSendMessage(List<String> byList, DiMarketingNiche diMarketingNiche, Integer type) {
        if (CollectionUtils.isEmpty(byList)) {
            return;
        }
        AgencyTaskInfoDTO agencyTaskInfoDto = new AgencyTaskInfoDTO();
        agencyTaskInfoDto.setBusinessKey(diMarketingNiche.getNicheNo());
        if (1 == type) {
            agencyTaskInfoDto.setTaskTypeEnum(AgencyTaskTypeEnum.NICHE_MANAGE);
            if (null != diMarketingNiche.getDiMarketingCustomer() && StringUtils.isNotBlank(diMarketingNiche.getDiMarketingCustomer().getCompanyName())) {
                agencyTaskInfoDto.setTaskName(diMarketingNiche.getDiMarketingCustomer().getCompanyName());
            }
        }
        if (2 == type) {
            agencyTaskInfoDto.setTaskTypeEnum(AgencyTaskTypeEnum.NICHE_TECHNICAL_SUPPORT);
            agencyTaskInfoDto.setTaskName(diMarketingNiche.getProjectName());
        }
        if (3 == type) {
            agencyTaskInfoDto.setTaskTypeEnum(AgencyTaskTypeEnum.NICHE_MANAGE_QUOTE);
            agencyTaskInfoDto.setTaskName(diMarketingNiche.getProjectName());
        }
        //获取状态中文描述
        SysDictData dictData = new SysDictData();
        dictData.setDictType("niche_status");
        dictData.setDictValue(diMarketingNiche.getNicheStatus());
        String nicheStatusDesc = remoteDictDataService.selectDictLabel(dictData);
        agencyTaskInfoDto.setTaskStateDesc(nicheStatusDesc);
        agencyTaskInfoDto.setLiabilityByList(byList);
        agencyTaskInfoDto.setType("1");
        String topic = AgencyConstants.AGENCY_TASK_TOPIC + AgencyConstants.DELIMITER_COLON + AgencyConstants.AGENCY_TASK_TAG;
        rocketMQTemplate.syncSend(topic, agencyTaskInfoDto);
    }

    /**
     * 发送修改待办任务消息
     *
     * @param nicheNo 商机编号
     */
    private void updateAgencyTaskSendMessage(String nicheNo, Integer type) {
        AgencyTaskInfoDTO agencyTaskInfoDto = new AgencyTaskInfoDTO();
        agencyTaskInfoDto.setBusinessKey(nicheNo);
        if (1 == type) {
            agencyTaskInfoDto.setTaskTypeEnum(AgencyTaskTypeEnum.NICHE_MANAGE);
        }
        if (2 == type) {
            agencyTaskInfoDto.setTaskTypeEnum(AgencyTaskTypeEnum.NICHE_TECHNICAL_SUPPORT);
        }
        agencyTaskInfoDto.setType("2");
        String topic = AgencyConstants.AGENCY_TASK_TOPIC + AgencyConstants.DELIMITER_COLON + AgencyConstants.AGENCY_TASK_TAG;
        rocketMQTemplate.syncSend(topic, agencyTaskInfoDto);
    }

    /**
     * 新增商机发送钉钉消息
     *
     * @param diMarketingNiche 商机实体
     */
    private void saveSendDingTalkMessage(DiMarketingNiche diMarketingNiche) {
        List<String> sendByList = new ArrayList<>();
        //商机创建人默认发送
        sendByList.add(diMarketingNiche.getCreateBy());
        List<DiMessageList> messageList = new ArrayList<>();
        //判断是否设置主要销售
        if (StringUtils.isNotBlank(diMarketingNiche.getNicheOwner())) {
            //获取主要销售中文名
            R<List<DdUserDTO>> userListResult = remoteDbcService.queryDdUserList(QueryDdUserDTO.builder().jobNumberList(Collections.singletonList(diMarketingNiche.getNicheOwner())).build());
            if (userListResult.isSuccess()) {
                // 设置主要销售发送钉钉消息
                String content = StrUtil.format(NicheDingTalkEnum.SET_MAIN_SALE.getMessage(), userListResult.getData().get(0).getName(), SecurityUtils.getLoginUser().getSysUser().getNickName());
                DiMessageList message = formMessageInfo(diMarketingNiche.getNicheNo(), content);
//                DiMessageList message = formMessageInfo(diMarketingNiche.getNicheNo(), "Test Content");
                messageList.add(message);
            } else {
                log.error("DiMarketingNicheServiceImpl---saveSendDingTalkMessage()---获取主要销售名称失败，主要销售工号：{}，返回：{}", diMarketingNiche.getNicheOwner(), JSONUtil.toJsonStr(userListResult));
            }
        }
        //判断是否关联了公司信息
        if (StringUtils.isNotBlank(diMarketingNiche.getCustomerNo())) {
            if (StringUtils.isNotBlank(diMarketingNiche.getDiMarketingCustomer().getCompanyName())) {
                String content = StrUtil.format(NicheDingTalkEnum.SAVE_RELATION_COMPANY.getMessage(), diMarketingNiche.getDiMarketingCustomer().getCompanyName(), SecurityUtils.getLoginUser().getSysUser().getNickName());
                DiMessageList message = formMessageInfo(diMarketingNiche.getNicheNo(), content);
//                DiMessageList message = formMessageInfo(diMarketingNiche.getNicheNo(), "Test content");
                messageList.add(message);
            } else {
                log.error("DiMarketingNicheServiceImpl---saveSendDingTalkMessage()---公司名称为空，公司信息：{}", JSONUtil.toJsonStr(diMarketingNiche.getDiMarketingCustomer()));
            }
        }
        //判断是否设置了共享销售
        if (StringUtils.isNotBlank(diMarketingNiche.getSharedBy())) {
            String[] sharedBys = diMarketingNiche.getSharedBy().split(",");
            List<String> sharedByList = Arrays.stream(sharedBys).distinct().toList();
            //获取共享销售中文名
            R<List<DdUserDTO>> userListResult = remoteDbcService.queryDdUserList(QueryDdUserDTO.builder().jobNumberList(sharedByList).build());
            if (userListResult.isSuccess()) {
                //将共享销售中文名转为字符串
                String sharedByName = userListResult.getData().stream().map(DdUserDTO::getName).collect(Collectors.joining(","));
                String content = StrUtil.format(NicheDingTalkEnum.SAVE_SHARE_SALE.getMessage(), sharedByName, SecurityUtils.getLoginUser().getSysUser().getNickName());
                DiMessageList message = formMessageInfo(diMarketingNiche.getNicheNo(), content);
//                DiMessageList message = formMessageInfo(diMarketingNiche.getNicheNo(), "Test content");
                messageList.add(message);
            } else {
                log.error("DiMarketingNicheServiceImpl---saveSendDingTalkMessage()---获取共享销售名称失败，共享销售工号：{}，返回：{}", JSONUtil.toJsonStr(sharedByList), JSONUtil.toJsonStr(userListResult));
            }
            sendByList.addAll(sharedByList);
        }
        //判断是否有可以发送的消息
        if (CollectionUtils.isEmpty(messageList)) {
            return;
        }
        //发送钉钉消息
        List<String> sendBys = sendByList.stream().distinct().toList();
        for (String sendBy : sendBys) {
            for (DiMessageList message : messageList) {
                message.setSendingUser(sendBy);
                iDiMessageListService.insertDiMessageList(message);
            }
        }
    }

    /**
     * 修改线索发送钉钉消息
     *
     * @param diMarketingNiche 入参商机
     * @param oldNiche         数据库中商机
     */
    private void updateSendDingTalkMessage(DiMarketingNiche diMarketingNiche, DiMarketingNiche oldNiche) {
        if (StringUtils.isBlank(diMarketingNiche.getNicheStatus())) {
            return;
        }
        //发送人
        List<String> sendByList = new ArrayList<>();
        //商机创建人默认发送
        sendByList.add(oldNiche.getCreateBy());
        List<DiMessageList> messageList = new ArrayList<>();
        //由于修改数据和修改状态是一个接口，修改状态时只会传状态数据，这里先判断是不是修改状态
        if (!diMarketingNiche.getNicheStatus().equals(oldNiche.getNicheStatus())) {
            //判断是否有共享销售
            if (StringUtils.isNotBlank(oldNiche.getSharedBy())) {
                String[] sharedBys = oldNiche.getSharedBy().split(",");
                List<String> sharedByList = Arrays.stream(sharedBys).distinct().toList();
            }
            //丢单发送钉钉消息
            if ("4".equals(diMarketingNiche.getNicheStatus())) {
                String content = StrUtil.format(NicheDingTalkEnum.NICHE_STATE_DISCARD.getMessage(), SecurityUtils.getLoginUser().getSysUser().getNickName());
                DiMessageList message = formMessageInfo(diMarketingNiche.getNicheNo(), content);
                messageList.add(message);
            }
            //放弃发送钉钉消息
            if ("5".equals(diMarketingNiche.getNicheStatus())) {
                String content = StrUtil.format(NicheDingTalkEnum.NICHE_STATE_ABANDON.getMessage(), SecurityUtils.getLoginUser().getSysUser().getNickName());
                DiMessageList message = formMessageInfo(diMarketingNiche.getNicheNo(), content);
                messageList.add(message);
            }
            if (StringUtils.isNotBlank(oldNiche.getNicheOwner())) {
                sendByList.add(oldNiche.getNicheOwner());
            }
            //判断是否有共享销售
            if (StringUtils.isNotBlank(oldNiche.getSharedBy())) {
                String[] sharedBys = oldNiche.getSharedBy().split(",");
                List<String> sharedByList = Arrays.stream(sharedBys).distinct().toList();
                sendByList.addAll(sharedByList);
            }
        } else {
            //判断主要销售
            if (!diMarketingNiche.getNicheOwner().equals(oldNiche.getNicheOwner())) {
                List<String> nicheOwnerList = new ArrayList<>();
                nicheOwnerList.add(diMarketingNiche.getNicheOwner());
                nicheOwnerList.add(oldNiche.getNicheOwner());
                //获取主要销售中文名
                R<List<DdUserDTO>> userListResult = remoteDbcService.queryDdUserList(QueryDdUserDTO.builder().jobNumberList(nicheOwnerList).build());
                if (userListResult.isSuccess()) {
                    Map<String, String> userNameMap = userListResult.getData().stream().collect(Collectors.toMap(DdUserDTO::getJobNumber, DdUserDTO::getName));
                    //设置主要销售发送钉钉消息
                    String content = StrUtil.format(NicheDingTalkEnum.UPDATE_MAIN_SALE.getMessage(), userNameMap.get(oldNiche.getNicheOwner()), userNameMap.get(diMarketingNiche.getNicheOwner()), SecurityUtils.getLoginUser().getSysUser().getNickName());
                    DiMessageList message = formMessageInfo(diMarketingNiche.getNicheNo(), content);
                    messageList.add(message);
                } else {
                    log.error("DiMarketingNicheServiceImpl---updateSendDingTalkMessage()---变更---获取主要销售名称失败，主要销售工号：{}，返回：{}", JSONUtil.toJsonStr(nicheOwnerList), JSONUtil.toJsonStr(userListResult));
                }
            }
            //判断关联客户
            if (StringUtils.isBlank(diMarketingNiche.getCustomerNo())) {
                //判断原来是否绑定客户
                if (StringUtils.isNotBlank(oldNiche.getCustomerNo())) {
                    //获取客户信息
                    DiMarketingCustomer diMarketingCustomer = diMarketingCustomerService.getOne(new LambdaQueryWrapper<DiMarketingCustomer>().eq(DiMarketingCustomer::getCustomerNo, oldNiche.getCustomerNo()));
                    if (null != diMarketingCustomer) {
                        //将关联客户删除
                        String content = StrUtil.format(NicheDingTalkEnum.DELETE_RELATION_COMPANY.getMessage(), diMarketingCustomer.getCompanyName(), SecurityUtils.getLoginUser().getSysUser().getNickName());
                        DiMessageList message = formMessageInfo(oldNiche.getNicheNo(), content);
                        messageList.add(message);
                    } else {
                        log.error("DiMarketingNicheServiceImpl---updateSendDingTalkMessage()---删除---根据客户号未获取到客户信息，客户号：{}", oldNiche.getNicheNo());
                    }
                }
            } else {
                //判断原来是否绑定客户
                if (StringUtils.isBlank(oldNiche.getCustomerNo())) {
                    //新增关联客户
                    String content = StrUtil.format(NicheDingTalkEnum.SAVE_RELATION_COMPANY.getMessage(), diMarketingNiche.getDiMarketingCustomer().getCompanyName(), SecurityUtils.getLoginUser().getSysUser().getNickName());
                    DiMessageList message = formMessageInfo(diMarketingNiche.getNicheNo(), content);
                    messageList.add(message);
                } else {
                    if (!oldNiche.getCustomerNo().equals(diMarketingNiche.getDiMarketingCustomer().getCustomerNo())) {
                        //获取客户信息
                        DiMarketingCustomer diMarketingCustomer = diMarketingCustomerService.getOne(new LambdaQueryWrapper<DiMarketingCustomer>().eq(DiMarketingCustomer::getCustomerNo, oldNiche.getCustomerNo()));
                        if (null != diMarketingCustomer) {
                            //修改关联客户
                            String content = StrUtil.format(NicheDingTalkEnum.UPDATE_RELATION_COMPANY.getMessage(), diMarketingCustomer.getCompanyName(), diMarketingNiche.getDiMarketingCustomer().getCompanyName(), SecurityUtils.getLoginUser().getSysUser().getNickName());
                            DiMessageList message = formMessageInfo(diMarketingNiche.getNicheNo(), content);
                            messageList.add(message);
                        } else {
                            log.error("DiMarketingNicheServiceImpl---updateSendDingTalkMessage()---修改---根据客户号未获取到客户信息，客户号：{}", oldNiche.getCustomerNo());
                        }
                    }
                }
            }
            //判断共享销售
            if (StringUtils.isBlank(diMarketingNiche.getSharedBy())) {
                //判断原本是否绑定共享销售
                if (StringUtils.isNotBlank(oldNiche.getSharedBy())) {
                    //共享销售被删除
                    String[] sharedBys = oldNiche.getSharedBy().split(",");
                    List<String> sharedByList = Arrays.stream(sharedBys).distinct().toList();
                    //获取共享销售中文名
                    R<List<DdUserDTO>> userListResult = remoteDbcService.queryDdUserList(QueryDdUserDTO.builder().jobNumberList(sharedByList).build());
                    if (userListResult.isSuccess()) {
                        //将共享销售中文名转为字符串
                        String sharedByName = userListResult.getData().stream().map(DdUserDTO::getName).collect(Collectors.joining(","));
                        String content = StrUtil.format(NicheDingTalkEnum.DELETE_SHARE_SALE.getMessage(), sharedByName, SecurityUtils.getLoginUser().getSysUser().getNickName());
                        DiMessageList message = formMessageInfo(diMarketingNiche.getNicheNo(), content);
                        messageList.add(message);
                    } else {
                        log.error("DiMarketingNicheServiceImpl---updateSendDingTalkMessage()---删除---获取共享销售名称失败，共享销售工号：{}，返回：{}", JSONUtil.toJsonStr(sharedByList), JSONUtil.toJsonStr(userListResult));
                    }
                }
            } else {
                if (StringUtils.isBlank(oldNiche.getSharedBy())) {
                    //共享销售新增
                    String[] sharedBys = diMarketingNiche.getSharedBy().split(",");
                    List<String> sharedByList = Arrays.stream(sharedBys).distinct().toList();
                    //获取共享销售中文名
                    R<List<DdUserDTO>> userListResult = remoteDbcService.queryDdUserList(QueryDdUserDTO.builder().jobNumberList(sharedByList).build());
                    if (userListResult.isSuccess()) {
                        //将共享销售中文名转为字符串
                        String content = StrUtil.format(NicheDingTalkEnum.SAVE_SHARE_SALE.getMessage(), userListResult.getData().stream().map(DdUserDTO::getName).collect(Collectors.joining(",")), SecurityUtils.getLoginUser().getSysUser().getNickName());
                        DiMessageList message = formMessageInfo(diMarketingNiche.getNicheNo(), content);
                        messageList.add(message);
                    } else {
                        log.error("DiMarketingNicheServiceImpl---updateSendDingTalkMessage()---新增---获取共享销售名称失败，共享销售工号：{}，返回：{}", JSONUtil.toJsonStr(sharedByList), JSONUtil.toJsonStr(userListResult));
                    }
                } else {
                    //都不等于空，判断是否修改
                    if (!oldNiche.getSharedBy().equals(diMarketingNiche.getSharedBy())) {
                        //转为集合
                        List<String> oldSharedBys = Arrays.stream(oldNiche.getSharedBy().split(",")).distinct().toList();
                        List<String> newSharedBys = Arrays.stream(diMarketingNiche.getSharedBy().split(",")).distinct().toList();
                        //获取新增和删除的共享销售
                        List<String> deleteSharedBys = oldSharedBys.stream().filter(sharedBy -> !newSharedBys.contains(sharedBy)).toList();
                        List<String> saveSharedBys = newSharedBys.stream().filter(sharedBy -> !oldSharedBys.contains(sharedBy)).toList();
                        //获取共享销售中文名
                        List<String> sharedByList = new ArrayList<>();
                        sharedByList.addAll(saveSharedBys);
                        sharedByList.addAll(deleteSharedBys);
                        R<List<DdUserDTO>> userListResult = remoteDbcService.queryDdUserList(QueryDdUserDTO.builder().jobNumberList(sharedByList).build());
                        if (userListResult.isSuccess()) {
                            //获取新增共享销售中文名
                            if (!CollectionUtils.isEmpty(saveSharedBys)) {
                                String saveSharedByName = userListResult.getData().stream().filter(ddUserDTO -> saveSharedBys.contains(ddUserDTO.getJobNumber())).map(DdUserDTO::getName).collect(Collectors.joining(","));
                                String content = StrUtil.format(NicheDingTalkEnum.SAVE_SHARE_SALE.getMessage(), saveSharedByName, SecurityUtils.getLoginUser().getSysUser().getNickName());
                                DiMessageList message = formMessageInfo(diMarketingNiche.getNicheNo(), content);
                                messageList.add(message);
                            }
                            //获取删除共享销售中文名
                            if (!CollectionUtils.isEmpty(deleteSharedBys)) {
                                String deleteSharedByName = userListResult.getData().stream().filter(ddUserDTO -> deleteSharedBys.contains(ddUserDTO.getJobNumber())).map(DdUserDTO::getName).collect(Collectors.joining(","));
                                String content = StrUtil.format(NicheDingTalkEnum.DELETE_SHARE_SALE.getMessage(), deleteSharedByName, SecurityUtils.getLoginUser().getSysUser().getNickName());
                                DiMessageList message = formMessageInfo(diMarketingNiche.getNicheNo(), content);
                                messageList.add(message);
                            }
                        } else {
                            log.error("DiMarketingNicheServiceImpl---updateSendDingTalkMessage()---修改---获取共享销售名称失败，共享销售工号：{}，返回：{}", JSONUtil.toJsonStr(sharedByList), JSONUtil.toJsonStr(userListResult));
                        }
                    }
                }
            }
            if (StringUtils.isNotBlank(diMarketingNiche.getNicheOwner())) {
                sendByList.add(diMarketingNiche.getNicheOwner());
            }
            //判断是否有共享销售
            if (StringUtils.isNotBlank(diMarketingNiche.getSharedBy())) {
                String[] sharedBys = diMarketingNiche.getSharedBy().split(",");
                List<String> sharedByList = Arrays.stream(sharedBys).distinct().toList();
                sendByList.addAll(sharedByList);
            }
        }
        //判断是否有可以发送的消息
        if (CollectionUtils.isEmpty(messageList)) {
            return;
        }
        //发送钉钉消息
        List<String> sendBys = sendByList.stream().distinct().toList();
        for (String sendBy : sendBys) {
            for (DiMessageList message : messageList) {
                message.setSendingUser(sendBy);
                iDiMessageListService.insertDiMessageList(message);
            }
        }
    }

    /**
     * 构建钉钉消息体
     *
     * @param nicheNo 商机编号
     * @param content 消息内容
     * @return 消息体
     */
    private DiMessageList formMessageInfo(String nicheNo, String content) {
        DiMessageList diMessageList = new DiMessageList();
        diMessageList.setBusinessModule("商机");
        diMessageList.setTitle("商机ID：" + nicheNo);
        diMessageList.setSendingTime(new Date());
        diMessageList.setRemarks(diMessageList.getTitle());
        diMessageList.setContent(content);
        return diMessageList;
    }

    /**
     * 关闭可行性验证审批
     *
     * @param nicheId                    商机ID
     * @param nicheNo                    商机号
     * @param feasibilityVerifyProcessId 可行性验证记录ID
     */
    private void closeFeasibilityVerify(String nicheId, String nicheNo, String feasibilityVerifyProcessId) {
        log.info("DiMarketingNicheServiceImpl---closeFeasibilityVerify()---丢单弃单_商机可行性验证取消流程开始---商机号：{}", nicheNo);
        //获取可行性验证流程记录
        DiFeasibilityVerifyProcess feasibilityVerifyProcess = diFeasibilityVerifyProcessService.getById(feasibilityVerifyProcessId);
        if (null == feasibilityVerifyProcess) {
            log.info("DiMarketingNicheServiceImpl---closeFeasibilityVerify()---可行性验证审批流程不存在，商机号：{}", nicheNo);
            return;
        }
        if (StringUtils.isBlank(feasibilityVerifyProcess.getBusinessApprovalProcessId()) || StringUtils.isBlank(feasibilityVerifyProcess.getTechniqueApprovalProcessId())) {
            log.info("DiMarketingNicheServiceImpl---closeFeasibilityVerify()---商机可行性验证信息不正确，商机号：{}", nicheNo);
            return;
        }
        //判断状态
        if (NicheApprovalConstants.STR_ZERO.equals(feasibilityVerifyProcess.getBusinessApprovalState())) {
            //关闭可行性验证-商务流程并删除待办审批
            diFeasibilityVerifyProcessService.closeProcessAndDelAgency(feasibilityVerifyProcess.getBusinessApprovalProcessId(), NicheApprovalConstants.BUSINESS_APPROVAL_ERROR_DISCARD_CANCEL, NicheApprovalConstants.FEASIBILITY_BUSINESS_APPROVAL_PROCESS, feasibilityVerifyProcess.getId());
        }
        if (NicheApprovalConstants.STR_ZERO.equals(feasibilityVerifyProcess.getTechniqueApprovalState())) {
            //关闭可行性验证-技术流程并删除待办审批
            diFeasibilityVerifyProcessService.closeProcessAndDelAgency(feasibilityVerifyProcess.getTechniqueApprovalProcessId(), NicheApprovalConstants.TECHNICAL_APPROVAL_ERROR_DISCARD_CANCEL, NicheApprovalConstants.FEASIBILITY_TECHNIQUE_APPROVAL_PROCESS, feasibilityVerifyProcess.getId());
        }
        //将商机可行性验证状态改为已取消
        LambdaUpdateChainWrapper<DiMarketingNiche> nicheUpdateWrapper = new LambdaUpdateChainWrapper<>(diMarketingNicheMapper);
        nicheUpdateWrapper.eq(DiMarketingNiche::getId, nicheId);
        nicheUpdateWrapper.set(DiMarketingNiche::getFeasibilityVerifyState, NicheApprovalConstants.STR_FIVE);
        nicheUpdateWrapper.update();
        log.info("DiMarketingNicheServiceImpl---closeFeasibilityVerify()---丢单弃单_商机可行性验证取消流程结束---商机号：{}", nicheNo);
    }


    /**
     * 回滚到方案阶段
     *
     * @param nicheId
     */
    @Override
    public void rollback2solutionStage(Long nicheId) {
        DiMarketingNiche niche = diMarketingNicheService.getById(nicheId);
        updateNicheStatus(niche.getNicheNo(), NicheStatusEnum.SOLUTION_STAGE.getCode());
        NicheRollback2SolutionStageEvent event = new NicheRollback2SolutionStageEvent();
        event.setNicheCode(niche.getNicheNo());
        eventPublisher.publishEvent(event);
    }

    @Override
    @Transactional
    public void rollbackRequirementStage(Long nicheId) {
        DiMarketingNiche diMarketingNiche = diMarketingNicheService.getById(nicheId);
        diMarketingNiche.setApplyForTechnicalSupport("0");
        diMarketingNiche.setNicheStatus("0");
        diMarketingNicheService.updateById(diMarketingNiche);
        diMarketingNiche.setNicheStage("0");
        updateNicheStatus(diMarketingNiche.getNicheNo(), NicheStatusEnum.REQUIREMENT_STAGE.getCode());
        //客户信息
        diMarketingNiche.setDiMarketingCustomer(diMarketingCustomerMapper.selectDiMarketingCustomerById(diMarketingNiche.getCustomerNo()));
        //退出技术支持商机代办
        //将方案阶段代办删除
        AgencyTaskInfoDTO agencyTaskInfoDto = new AgencyTaskInfoDTO();
        agencyTaskInfoDto.setBusinessKey(diMarketingNiche.getNicheNo());
        agencyTaskInfoDto.setTaskTypeEnum(AgencyTaskTypeEnum.NICHE_TECHNICAL_SUPPORT);
        agencyTaskInfoDto.setType("2");
        String topic = AgencyConstants.AGENCY_TASK_TOPIC + AgencyConstants.DELIMITER_COLON + AgencyConstants.AGENCY_TASK_TAG;
        rocketMQTemplate.syncSend(topic, agencyTaskInfoDto);
        //创建销售代办
        try {
            //增加待办任务(待办任务，勿删)
            List<String> byList = new ArrayList<>();
            byList.add(diMarketingNiche.getNicheOwner());
            DiMarketingContacts diMarketingContacts = new DiMarketingContacts();
            diMarketingContacts.setBusinessId(diMarketingNiche.getNicheNo());
            List<DiMarketingContacts> contactsList = diMarketingContactsService.selectDiMarketingContactsList(diMarketingContacts);
            String sharedBy = "";
            for (int i = 0; i < contactsList.size(); i++) {
                contactsList.get(i).setBelonging("商机");
                contactsList.get(i).setBusinessId(diMarketingNiche.getNicheNo());
                if (StringUtils.isNotBlank(contactsList.get(i).getContactsOwner())) {
                    sharedBy += contactsList.get(i).getContactsOwner() + ",";
                    byList.add(contactsList.get(i).getContactsOwner());
                }
            }
            //新增代办
            saveAgencyTaskSendMessage(byList, diMarketingNiche, 1);
            //发送钉钉消息
            diMarketingNiche.setSharedBy(sharedBy);
            saveSendDingTalkMessage(diMarketingNiche);
        } catch (Exception e1) {
            log.error("add niche saveAgencyTaskSendMessage error", e1);
        }
    }

    @Override
    public void dataMaintenance() {
        //获取所有商机
        List<DiMarketingNiche> nicheList = diMarketingNicheService.list(new LambdaQueryWrapper<DiMarketingNiche>()
                .eq(DiMarketingNiche::getDelFlag, "0"));
        if (CollectionUtil.isEmpty(nicheList)) {
            log.info("DiMarketingNicheServiceImpl---dataMaintenance()---商机为空");
            return;
        }
        //获取所有的商机需求
        List<DiMarketingNicheDemand> nicheDemandList = diMarketingNicheDemandService.list(new LambdaQueryWrapper<DiMarketingNicheDemand>()
                .eq(DiMarketingNicheDemand::getDelFlag, "0"));
        if (CollectionUtil.isEmpty(nicheDemandList)) {
            log.info("DiMarketingNicheServiceImpl---dataMaintenance()---商机销售需求为空");
            return;
        }
        //获取所有商机需求意向数据
        List<DiMarketingNicheDemandIntention> nicheDemandIntentionList = diMarketingNicheDemandIntentionService.list(new LambdaQueryWrapper<DiMarketingNicheDemandIntention>()
                .eq(DiMarketingNicheDemandIntention::getDelFlag, "0"));
        if (CollectionUtil.isEmpty(nicheDemandIntentionList)) {
            log.info("DiMarketingNicheServiceImpl---dataMaintenance()---商机销售需求意向为空");
            return;
        }
        Map<String, List<DiMarketingNicheDemand>> nicheDemandMap = nicheDemandList.stream().collect(Collectors.groupingBy(DiMarketingNicheDemand::getNicheNo));
        Map<Long, List<DiMarketingNicheDemandIntention>> nicheDemandIntentionMap = nicheDemandIntentionList.stream().collect(Collectors.groupingBy(DiMarketingNicheDemandIntention::getDemandId));
        nicheList.forEach(niche -> {
            if (CollectionUtil.isNotEmpty(nicheDemandMap.get(niche.getNicheNo()))
                    && CollectionUtil.isNotEmpty(nicheDemandIntentionMap.get(nicheDemandMap.get(niche.getNicheNo()).get(0).getId()))) {
                DiMarketingNicheDemand nicheDemand = nicheDemandMap.get(niche.getNicheNo()).get(0);
                DiMarketingNicheDemandIntention nicheDemandIntention = nicheDemandIntentionMap.get(nicheDemand.getId()).get(0);
                diMarketingNicheDemandService.updateNicheDemand(nicheDemand.getId(), nicheDemandIntention);
            }
        });
    }

    /**
     * 更新商机询价定性 和 定性原因
     * @param request
     */
    @Override
    public void updateNicheImportance(UpdateNicheImportanceRequest request) {

        DiMarketingNiche diMarketingNiche = diMarketingNicheMapper.selectById(request.getId());
        if(Objects.isNull(diMarketingNiche)){
            throw new RuntimeException("无效id");
        }

        if(Arrays.asList(3,4,5,7).contains(diMarketingNiche.getNicheStatus())){
            throw new RuntimeException("商机非终态，不能编辑");
        }

        UpdateWrapper<DiMarketingNiche> updateWrapper = new UpdateWrapper<>();
        updateWrapper.set("importance", request.getImportance().toString())
                .set("determine_cause",request.getDetermineCause())
                .eq("id", request.getId().toString());

        diMarketingNicheMapper.update(updateWrapper);
    }

}
