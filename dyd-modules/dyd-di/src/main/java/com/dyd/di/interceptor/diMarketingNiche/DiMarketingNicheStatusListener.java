package com.dyd.di.interceptor.diMarketingNiche;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dyd.common.core.utils.StringUtils;
import com.dyd.di.comment.enums.CommentStageEnum;
import com.dyd.di.interceptor.entity.DiProjectStageChange;
import com.dyd.di.interceptor.mapper.DiProjectStageChangeMapper;
import com.dyd.di.marketing.domain.DiMarketingNiche;
import com.dyd.di.marketing.mapper.DiMarketingNicheMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;

@Component
public class DiMarketingNicheStatusListener {
    @Autowired
    private DiMarketingNicheMapper diMarketingNicheMapper;
    @Autowired
    private DiProjectStageChangeMapper projectStageChangeMapper;

    @Async
    @EventListener
    public void handleDataChange(DiMarketingNicheStatusEvent event) {
        handleDataChange0(event);
    }

    public void handleDataChange0(DiMarketingNicheStatusEvent event) {
        DiMarketingNiche diMarketingNiche = null;
        if (StringUtils.hasText(event.getNicheNo())) {
            diMarketingNiche =
                    diMarketingNicheMapper.selectOne(Wrappers.<DiMarketingNiche>lambdaQuery().eq(DiMarketingNiche::getNicheNo, event.getNicheNo()));
        }
        if (StringUtils.hasText(event.getId())) {
            diMarketingNiche =
                    diMarketingNicheMapper.selectOne(Wrappers.<DiMarketingNiche>lambdaQuery().eq(DiMarketingNiche::getId, event.getId()));
        }
        if (diMarketingNiche != null) {
            DiProjectStageChange diProjectStageChange = projectStageChangeMapper.selectOne(
                    Wrappers.<DiProjectStageChange>lambdaQuery()
                            .eq(DiProjectStageChange::getRefNo, diMarketingNiche.getNicheNo())
                            .orderByDesc(DiProjectStageChange::getCreateTime)
                            .last("limit 1"));
            String currStage = Objects.nonNull(diProjectStageChange) ? diProjectStageChange.getStage() : null;
            String nicheStatus = diMarketingNiche.getNicheStatus();
            if (Objects.equals(nicheStatus, "2") && !Objects.equals(currStage, CommentStageEnum.contract.getCode())) {
                projectStageChangeMapper.insert(DiProjectStageChange.builder()
                                .stage(CommentStageEnum.contract.getCode())
                                .refNo(diMarketingNiche.getNicheNo())
                                .createTime(new Date()).build());
            } else if (Objects.equals(nicheStatus, "8") && !Objects.equals(currStage, CommentStageEnum.quote.getCode())) {
                projectStageChangeMapper.insert(DiProjectStageChange.builder()
                        .stage(CommentStageEnum.quote.getCode())
                        .refNo(diMarketingNiche.getNicheNo())
                        .createTime(new Date()).build());
            } else if (Objects.equals(nicheStatus, "1") && !Objects.equals(currStage, CommentStageEnum.plan.getCode())) {
                projectStageChangeMapper.insert(DiProjectStageChange.builder()
                        .stage(CommentStageEnum.plan.getCode())
                        .refNo(diMarketingNiche.getNicheNo())
                        .createTime(new Date()).build());
            } else if (Objects.equals(nicheStatus, "0") && !Objects.equals(currStage, CommentStageEnum.niche.getCode())) {
                projectStageChangeMapper.insert(DiProjectStageChange.builder()
                        .stage(CommentStageEnum.niche.getCode())
                        .refNo(diMarketingNiche.getNicheNo())
                        .createTime(new Date()).build());
            }
        }
    }
}
