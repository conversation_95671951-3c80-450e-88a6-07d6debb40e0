package com.dyd.di.pre.entity;

import java.io.Serializable;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

/**
 * 销售报价单
 *
 * @TableName di_pre_sale_quote
 */
@Data
public class DiPreSaleQuote implements Serializable {

    /**
     * id 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 报价单编码
     */
    private String preSaleQuoteCode;
    /**
     * 商机编码
     */
    private String nicheCode;

    /**
     * 理论成本合计
     */
    private BigDecimal costFeeTotal;

    /**
     * 销售报价合计
     */
    private BigDecimal saleQuoteTotal;

    /**
     * 毛利额合计
     */
    private BigDecimal grossProfitTotal;

    /**
     * 整单毛利率
     */
    private BigDecimal grossMarginTotal;

    /**
     * 客户名称
     */
    private String companyName;

    /**
     * 联系人姓名
     */
    private String contactsName;

    /**
     * 联系方式
     */
    private String contactsPhone;


    /**
     * 报价状态 0:未报价 1：已报价 2：已放弃
     */
    private Integer quoteStatus;

    /**
     * 审批状态 0:待审批 1:审批中 2:审批通过 3：审批驳回 4：已放弃
     */
    private Integer approvalStatus;

    /**
     * 成本审批流程实例ID
     */
    private String costApprovalProcessId;

    /**
     * 审批流中任务ID
     */
    private String camundaTaskId;

    /**
     * 销售服务报价合计
     */
    private BigDecimal saleServiceQuoteTotal;

    /**
     * 销售服务周期报价成本合计
     */
    private BigDecimal saleServiceCycleTotal;

    /**
     * 订单期望交付日期
     */
    private LocalDate expecteDate;

    /**
     * 是否提前开票
     */
    private Integer preInvoice;


    /**
     * 创建人
     */
    private String createBy;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新人
     */
    private String updateBy;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 国家
     */
    private String countryName;

    /**
     * 国家编码
     */
    private String countryCode;

    /**
     * 省
     */
    private String provinceName;

    /**
     * 市
     */
    private String cityName;

    /**
     * 区
     */
    private String areaName;

    /**
     * 省编码
     */
    private String provinceCode;

    /**
     * 市编码
     */
    private String cityCode;

    /**
     * 区编码
     */
    private String areaCode;

    /**
     * 统一收货地址
     */
    private String commonDeliveryAddress;

    /**
     * 商务费用
     */
    private BigDecimal businessCost;

    /**
     * 核算交期（周），获取待报价方案列表中，单套测算交期（天）最大值。 以该值/7，并向上取整。如 7.1周 算8周。
     */
    private BigDecimal calculateDeliveryWeek;


    /**
     * 复核后交期（周）
     */
    private BigDecimal finallyDeliveryWeek;

    /**
     * 是否发起过来交期复核 ，0未发生过
     * 1  复核后交期 用 直接复核后交期
     * 2  复核后 周期 取 核算交期，即被驳回后的初始值
     */
    private Integer deliveryReviewFlag;

    /**
     * 是否提前执行：1.否，2.是
     */
    private Integer isAdvanceExecution;

    /**
     * 期望交期（周）
     */
    private Integer expectationDeliveryWeek;

    /**
     * 是否生成合同：1.未生成，2.已生成
     */
    private Integer isGenerateContract;

    /**
     * 低利润原因大类
     */
    private String reasonCategories;

    /**
     * 详细情况说明
     */
    private String situationDescription;
}
