package com.dyd.di.comment.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;


@Getter
@AllArgsConstructor
public enum CommentStageEnum {

    clue("clue", "线索"),
    niche("niche", "商机"),
    plan("plan", "方案"),
    quote("quote", "报价"),
    contract("contract", "合同"),
    order("order", "订单"),
    design("design", "设计"),
    purchase("purchase", "采购"),
    production("production", "生产"),
    quality_inspection("quality_inspection", "质检"),
    to_be_shipped("to_be_shipped", "待出货"),
    completed("completed", "已完成")
    ;

    private final String code;

    private final String name;

}
