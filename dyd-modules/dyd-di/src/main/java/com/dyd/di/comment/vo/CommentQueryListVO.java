package com.dyd.di.comment.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CommentQueryListVO {

    /**
     * 需要筛选的项目列表 key为项目号 value为项目名
     */
    private List<Map<String, String>> projectList;

    /**
     * 需要筛选的商机号
     */
    private List<String> nicheNoList;

    /**
     * 需要筛选的线索号
     */
    private List<String> clueNoList;

    /**
     * 需要筛选的客户列表 key为客户号 value为客户名
     */
    private List<Map<String, String>> companyList;

}
