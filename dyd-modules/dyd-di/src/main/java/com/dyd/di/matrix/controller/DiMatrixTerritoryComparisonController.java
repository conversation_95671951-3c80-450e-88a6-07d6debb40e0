package com.dyd.di.matrix.controller;

import com.dyd.common.core.domain.R;
import com.dyd.di.matrix.convert.MatrixQualificationConvert;
import com.dyd.di.matrix.dto.*;
import com.dyd.di.matrix.entity.DiMatrixTerritoryQualification;
import com.dyd.di.matrix.service.IDiMatrixTerritoryComparisonGroupService;
import com.dyd.di.matrix.service.IDiMatrixTerritoryQualificationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 业务版图对比分组 前端控制器
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
@RestController
@RequestMapping("/matrix/diMatrixTerritoryComparison")
public class DiMatrixTerritoryComparisonController {


    @Autowired
    private IDiMatrixTerritoryComparisonGroupService groupService;

    /*
     * 添加对比
     */
    @PostMapping("/saveComparison")
    public R saveComparison(@RequestBody SaveComparisonRequest req) {
        groupService.saveComparison(req.getTerritoryId(), req.getGroupList());
        return R.ok();
    }

    /*
     * 查询对比
     */
    @PostMapping("/queryComparison")
    public R<QueryComparisonResponse> queryComparison(@RequestBody QueryComparisonRequest req) {
        List<ComparisonGroupDTO> groups = groupService.queryComparisonByTerritoryId(req.getTerritoryId());
        QueryComparisonResponse response = new QueryComparisonResponse();
        response.setGroupList(groups);
        response.setTerritoryId(req.getTerritoryId());
        return R.ok(response);
    }

}
