<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dyd.di.pre.mapper.DiPreSaleQuoteDetailMapper">

    <select id="findQuoteNoByCondition" resultType="com.dyd.di.pre.domain.response.OrderListDataResponse">
        select dpsq.pre_sale_quote_code,
               GROUP_CONCAT(dps.industry_categories SEPARATOR ',')      as industryCategoriesList,
               GROUP_CONCAT(dpsc.tech_support_owner_code SEPARATOR ',') as techSupportOwnerCodeList
        from di_pre_sale_quote_detail dpsqd
            left join di_pre_sale_quote dpsq on dpsq.id = dpsqd.pre_sale_quote_id
            left join di_order do on do.pre_sale_quote_no = dpsq.pre_sale_quote_code
            left join di_pre_sale dps on dps.id = dpsqd.pre_sale_id
            left join di_pre_sale_customized dpsc on dpsc.pre_sale_id = dpsqd.pre_sale_id
        where do.order_no is not null
            and do.order_no != ''
        <if test="industryCategoriesList != null and industryCategoriesList.size() > 0">
            and dps.industry_categories in
            <foreach collection="industryCategoriesList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="techSupportOwnerCodeList != null and techSupportOwnerCodeList.size() > 0">
            and dpsc.tech_support_owner_code in
            <foreach collection="techSupportOwnerCodeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="preSaleQuoteCodeList != null and preSaleQuoteCodeList.size() > 0">
            and dpsq.pre_sale_quote_code in
            <foreach collection="preSaleQuoteCodeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        group by dpsq.pre_sale_quote_code
    </select>

    <select id="getQuoteDetailByCustomerNo" resultType="java.math.BigDecimal">
        select * from (
          SELECT
              ROUND(d.sale_quote / s.num, 2) AS result
          FROM
              di_pre_sale_quote_detail d
                  LEFT JOIN di_pre_sale_quote q on q.id = d.pre_sale_quote_id
                  LEFT JOIN di_order o on o.pre_sale_quote_no = q.pre_sale_quote_code
                  LEFT JOIN di_pre_sale s ON d.pre_sale_code = s.pre_sale_code
          where
              o.order_status != '9' and
              s.niche_id in(
                  select id from di_marketing_niche where customer_no = #{customerNo}
              )
      ) a where 1=1 ORDER BY a.result
    </select>

</mapper>
