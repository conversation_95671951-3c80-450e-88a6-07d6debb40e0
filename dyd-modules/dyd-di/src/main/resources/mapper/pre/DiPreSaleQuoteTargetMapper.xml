<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dyd.di.pre.mapper.DiPreSaleQuoteTargetMapper">

    <resultMap id="BaseResultMap" type="com.dyd.di.pre.domain.DiPreSaleQuoteTarget">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="saleBu" column="sale_bu" jdbcType="VARCHAR"/>
            <result property="saleNo" column="sale_no" jdbcType="VARCHAR"/>
            <result property="saleName" column="sale_name" jdbcType="VARCHAR"/>
            <result property="yearTarget" column="year_target" jdbcType="DECIMAL"/>
            <result property="yearGp1" column="year_gp1" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,sale_bu,sale_no,
        sale_name,year_target,year_gp1
    </sql>
</mapper>
