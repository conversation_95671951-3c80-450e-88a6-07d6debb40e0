package com.dyd.di.service.impl;

import com.alibaba.fastjson.JSON;
import com.dyd.common.core.utils.PageWrapper;
import com.dyd.di.api.model.DiMarketingCustomerListResponse;
import com.dyd.di.api.model.DiOrderListRequest;
import com.dyd.di.api.model.PreSaleAppearanceResponse;
import com.dyd.di.api.model.ProjectProcessSynJdyResponse;
import com.dyd.di.grid.dto.request.QueryGridDataRequest;
import com.dyd.di.grid.query.QueryGridDataExecutor;
import com.dyd.di.grid.service.IDiGridTableDictService;
import com.dyd.di.marketing.domain.dto.UpdateNicheImportanceRequest;
import com.dyd.di.marketing.service.IDiMarketingClueService;
import com.dyd.di.marketing.service.IDiMarketingCustomerService;
import com.dyd.di.marketing.service.IDiMarketingNicheService;
import com.dyd.di.material.service.DiMaterialInquiryFormService;
import com.dyd.di.materiel.pojo.dto.MaterielFeeDTO;
import com.dyd.di.materiel.pojo.dto.MaterielFeeQueryDTO;
import com.dyd.di.materiel.pojo.dto.MaterielVersionBomDTO;
import com.dyd.di.materiel.service.IDiMaterielBomService;
import com.dyd.di.materiel.service.IDiMaterielService;
import com.dyd.di.matrix.domain.MatrixTerritoryMetaSaveRequest;
import com.dyd.di.matrix.pojo.request.QueryProductByTerritoryRequest;
import com.dyd.di.matrix.query.QueryProductByTerritoryExecutor;
import com.dyd.di.matrix.service.DiMatrixProductService;
import com.dyd.di.matrix.service.DiMatrixTerritoryMetaService;
import com.dyd.di.matrix.vo.OperateMatrixProductVO;
import com.dyd.di.order.pojo.DiOrderUpdateRequest;
import com.dyd.di.order.service.IDiOrderService;
import com.dyd.di.oss.OssService;
import com.dyd.di.pre.domain.request.*;
import com.dyd.di.pre.domain.response.*;
import com.dyd.di.pre.entity.DiPreSaleHistory;
import com.dyd.di.pre.service.DiPreSaleHistoryService;
import com.dyd.di.pre.service.IDiPreSaleService;
import com.dyd.di.pre.service.IDiPreSaleSupportService;
import com.dyd.di.pre.service.impl.PreSaleQuoteService;
import com.dyd.di.process.service.IDiProcessProjectService;
import com.dyd.di.project.service.IDiProjectService;
import com.dyd.di.sequence.SequenceService;
import com.dyd.di.u9.service.U9Service;
import com.dydtec.base.oss.api.dto.response.OssPreviewDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;


@Slf4j
@SpringBootTest
@RunWith(SpringRunner.class)
public class DiPreSaleTest {

    @Autowired
    private IDiPreSaleService iDiPreSaleService;

    @Autowired
    private IDiPreSaleSupportService iDiPreSaleSupportService;

    @Autowired
    private PreSaleQuoteService preSaleQuoteService;

    @Autowired
    private U9Service u9Service;

    @Autowired
    private IDiMaterielService iDiMaterielService;

    @Autowired
    private IDiProjectService iDiProjectService;

    @Autowired
    private SequenceService sequenceService;

    @Autowired
    private IDiMarketingCustomerService diMarketingCustomerService;

    @Autowired
    private DiPreSaleHistoryService preSaleHistoryService;

    @Autowired
    private IDiGridTableDictService iDiGridTableDictService;

    @Autowired
    private IDiOrderService diOrderService;

    @Autowired
    private DiMatrixTerritoryMetaService diMatrixTerritoryMetaService;

    @Autowired
    private QueryProductByTerritoryExecutor queryProductByTerritoryExecutor;

    @Autowired
    private IDiMarketingNicheService diMarketingNicheService;

    @Autowired
    private IDiMarketingClueService diMarketingClueService;

    @Autowired
    private OssService ossService;

    @Autowired
    private IDiProcessProjectService diProcessProjectService;

    @Autowired
    private QueryGridDataExecutor queryGridDataExecutor;

    @Autowired
    private DiMaterialInquiryFormService diMaterialInquiryFormService;

    @Test
    public void updateNicheImportance(){
        UpdateNicheImportanceRequest updateNicheImportanceRequest = new UpdateNicheImportanceRequest();
        updateNicheImportanceRequest.setId(258L);
        updateNicheImportanceRequest.setImportance(1);
        updateNicheImportanceRequest.setDetermineCause("tt");
        diMarketingNicheService.updateNicheImportance(updateNicheImportanceRequest);
    }

    @Test
    public void preSaleMaterielReLoad(){
        diMaterialInquiryFormService.preSaleMaterielReLoad(192L);
    }

    @Test
    public void queryData(){
        QueryGridDataRequest req  = new QueryGridDataRequest();
        QueryGridDataRequest.Option option = new QueryGridDataRequest.Option();
        option.setFieldCode("territoryId");
        option.setArgs(Arrays.asList("554"));
        option.setType("simple");
        option.setConcat("and");
        req.setOptionList(Arrays.asList(option));


        queryGridDataExecutor.queryData(req,"territory_project",null);
    }

    @Test
    public void projectProcessSynJdy(){
        PageWrapper<List<ProjectProcessSynJdyResponse>> listPageWrapper = diProcessProjectService.projectProcessSynJdy(1, 200);

    }

    @Test
    public void getOssFileByList() throws IOException, InterruptedException {
        //List<OssPreviewDTO> appearance = ossService.getOssFileByList("pre_sale_scene", Arrays.asList("产品方案/72397ebc41eb459b8c69024312c47eacrbA4v"));

        List<PreSaleAppearanceResponse> preSaleAppearanceResponses = iDiProjectService.queryProjectPreSale();

    }

    @Test
    public void editOrderAdvanceExecution(){
        DiOrderUpdateRequest diOrderUpdateRequest = new DiOrderUpdateRequest();
        diOrderUpdateRequest.setId(1813473280328835475L);
        diOrderUpdateRequest.setIsAdvanceExecution("2");
        diOrderService.updateOrderAdvanceExecution(diOrderUpdateRequest);
    }

    @Test
    public void synClueToJdyT(){
        diMarketingClueService.synClueToJdy();
    }

    @Test
    public void getStockUpList(){
        List<?> stockUpList = diMarketingNicheService.getStockUpList("");
        log.info("stockUpList{}",JSON.toJSONString(stockUpList));
    }

    @Resource
    private DiMatrixProductService diMatrixProductService;

    @Autowired
    private IDiMaterielBomService diMaterielBomService;

    @Test
    public void bomByVersionU9(){
        MaterielVersionBomDTO materielVersionBomDTO = diMaterielBomService.bomByVersionU9("1050110500-021");
        System.out.println(JSON.toJSONString(materielVersionBomDTO));
    }

    @Test
    public void getDetail(){
        OperateMatrixProductVO operateMatrixProductVO = new OperateMatrixProductVO();
        operateMatrixProductVO.setId(502L);
        diMatrixProductService.getMatrixProductDetail(operateMatrixProductVO);
    }

    @Test
    public void exce(){
        QueryProductByTerritoryRequest queryProductByTerritoryRequest = new QueryProductByTerritoryRequest();
        queryProductByTerritoryRequest.setPageNum(1);
        queryProductByTerritoryRequest.setPageSize(100);
        queryProductByTerritoryRequest.setTerritoryId(569L);
        /*Map<String ,String> map = new HashMap<>();
        map.put("number_label_937","");
        //map.put("number_label_1715","≥-300cm");
        queryProductByTerritoryRequest.setRequestMap(map);*/
        queryProductByTerritoryExecutor.executor(queryProductByTerritoryRequest);
    }

    @Test
    public void checkMetaConflict(){
        MatrixTerritoryMetaSaveRequest matrixTerritoryMetaSaveRequest = new MatrixTerritoryMetaSaveRequest();
        MatrixTerritoryMetaSaveRequest.Meta meta = new MatrixTerritoryMetaSaveRequest.Meta();
        meta.setId(561L);
        meta.setMetaType(4);
        meta.setTerritoryName("77");

        MatrixTerritoryMetaSaveRequest.Meta metaT = new MatrixTerritoryMetaSaveRequest.Meta();
        metaT.setId(566L);
        metaT.setMetaType(6);
        metaT.setTerritoryName("AAA建材");

        MatrixTerritoryMetaSaveRequest.Meta metaTT = new MatrixTerritoryMetaSaveRequest.Meta();
        metaTT.setId(565L);
        metaTT.setMetaType(6);
        metaTT.setTerritoryName("AAA香菜");
        MatrixTerritoryMetaSaveRequest.Meta metaTTT = new MatrixTerritoryMetaSaveRequest.Meta();
        metaTTT.setMetaType(7);
        metaTTT.setTerritoryName("PP");
        metaTTT.setPartsParentId(566L);
        metaTTT.setPartsParentName("AAA建材");
        matrixTerritoryMetaSaveRequest.setMatrixMetas(Arrays.asList(meta,metaT,metaTT,metaTTT));
        diMatrixTerritoryMetaService.checkMetaConflict(matrixTerritoryMetaSaveRequest);
    }

    @Test
    public void selectDiPreSaleListForRemote(){
        PreSaleListRequest preSaleListRequest = new PreSaleListRequest();
        preSaleListRequest.setPageNum(1);
        preSaleListRequest.setPageSize(500);
        iDiPreSaleService.selectDiPreSaleListForRemote(preSaleListRequest);
    }

    @Test
    public void  getGridDictList(){
        iDiGridTableDictService.getGridDictList("chengshi");
    }

    @Test
    public void saveHistoryOfPreSale(){
        PreSaleDetailResponse historyInfo = iDiPreSaleService.selectDiPreSaleById(954L, null, null,true);
        DiPreSaleHistory diPreSaleHistory = new DiPreSaleHistory();
        diPreSaleHistory.setPreSaleId(954L);
        diPreSaleHistory.setPreSaleHistoryContent(JSON.toJSONString(historyInfo));
        preSaleHistoryService.saveDiPreSaleHistory(diPreSaleHistory);
    }


    @Test
    public void getProjectNo(){

        List<String> objects = Lists.newArrayList();
        sequenceService.getProjectNo("YSY",objects );
    }


    @Test
    public void test(){
        MaterielFeeQueryDTO materielFeeQueryDTO = new MaterielFeeQueryDTO();
        //materielFeeQueryDTO.setMaterielNo("821.000.02");
        materielFeeQueryDTO.setProvince("320000");
        materielFeeQueryDTO.setCity("320100");
        materielFeeQueryDTO.setArea("320102");
        MaterielFeeDTO materielFeeDTO = iDiMaterielService.queryMaterielFee(materielFeeQueryDTO);

    }

    @Test
    public void createSoU9(){
        u9Service.createSoU9(510L);
    }


    @Test
    public void insertDiPreSale(){
        PreSaleAddRequest preSaleAddRequest = new PreSaleAddRequest();
       // preSaleAddRequest.setSjId(1);
        preSaleAddRequest.setOrderType(5);
        preSaleAddRequest.setPreSaleName("产品方案V2");
        /*preSaleAddRequest.setPlanDealDate(LocalDate.now());
        preSaleAddRequest.setPlanDealAmount(new BigDecimal("1000.55"));*/
        preSaleAddRequest.setDeliveryAddressType(1);
        preSaleAddRequest.setCommonDeliveryAddress("川沙哈哈哈哈哈哈");
        preSaleAddRequest.setCountry("中国");
        preSaleAddRequest.setProvince("上海");
        preSaleAddRequest.setCity("上海");
        preSaleAddRequest.setArea("浦东");
        preSaleAddRequest.setExpecteDate(LocalDate.of(2024,6,29));
        preSaleAddRequest.setSceneUrls(Arrays.asList("ss.jpg","dd.jpg"));
        preSaleAddRequest.setNicheCode("DYD-SJ240713-0002");
        preSaleAddRequest.setNum(1);
        preSaleAddRequest.setPackageType("100");
        preSaleAddRequest.setIsInstallation(1);
        preSaleAddRequest.setInstallationFee(new BigDecimal(100));
        preSaleAddRequest.setIsConstructionQualification(1);
        preSaleAddRequest.setConstructionQualificationContext("自制内容");
        //preSaleAddRequest.setPreSaleUrls(Arrays.asList("ff.jpg","gg.jpg"));


        PreSaleAddRequest.PreSaleManifest preSaleManifest = new PreSaleAddRequest.PreSaleManifest();
        /*preSaleManifest.setSellCategory("系统");
        preSaleManifest.setProductType("系统1");
        preSaleManifest.setBrand("岱鼎");*/
        //preSaleManifest.setMaterialCode("2024.07.15.00125");
        preSaleManifest.setProductName("岱鼎一号");
        /*preSaleManifest.setExpirationDateEnd(LocalDate.of(2024,6,30));
        preSaleManifest.setFeeTotal(new BigDecimal("1000"));
        preSaleManifest.setDeviseFee(new BigDecimal("800"));
        preSaleManifest.setMaterialFee(new BigDecimal("100"));
        preSaleManifest.setProduceFee(new BigDecimal("50"));
        preSaleManifest.setImplementFee(new BigDecimal("10"));
        preSaleManifest.setPackageFee(new BigDecimal("5"));
        preSaleManifest.setOtherFee(new BigDecimal("10"));
        preSaleManifest.setQuantity(1);
        preSaleManifest.setRdDay(1);
        preSaleManifest.setSupplyDay(1);
        preSaleManifest.setProduceDay(2);
        preSaleManifest.setImplementDay(3);
        preSaleManifest.setDeliveryAddress("22333rrrff");*/
        preSaleAddRequest.setPreSaleManifests(Arrays.asList(preSaleManifest));
        iDiPreSaleService.insertDiPreSale(preSaleAddRequest);
    }


    @Test
    public void preSaleList(){
        PreSaleListRequest preSaleListRequest = new PreSaleListRequest();
        preSaleListRequest.setPageNum(1);
        preSaleListRequest.setPageSize(10);
        preSaleListRequest.setPreCode("");
        PageWrapper<List<PreSaleListResponse>> listPageWrapper = iDiPreSaleService.selectDiPreSaleList(preSaleListRequest);
        log.info("列表{}", JSON.toJSONString(listPageWrapper));
    }

    @Test
    public void getInfo(){
        PreSaleDetailResponse preSaleDetailResponse = iDiPreSaleService.selectDiPreSaleById(null,"DYD-SQ240814-0012",2,false);
        log.info("售前详情{}",JSON.toJSONString(preSaleDetailResponse));
    }

    @Test
    public void edit(){
        PreSaleUpdateRequest preSaleUpdateRequest = new PreSaleUpdateRequest();
        preSaleUpdateRequest.setId(151);
        preSaleUpdateRequest.setPreSaleName("产品方案V22");
        iDiPreSaleService.updateDiPreSale(preSaleUpdateRequest);
    }



    @Test
    public void insertDiPreSaleSupport(){
        PreSaleSupportAddRequest preSaleSupportAddRequest = new PreSaleSupportAddRequest();
        //preSaleSupportAddRequest.setDiPreSaleId(2);
        preSaleSupportAddRequest.setTelSupportHeader("王小二");
        preSaleSupportAddRequest.setApportionDate(LocalDate.now());
        preSaleSupportAddRequest.setSponsor("王工");
        preSaleSupportAddRequest.setBuName("燃烧");
        preSaleSupportAddRequest.setNicheCode("SJ001");
        preSaleSupportAddRequest.setNicheId(1);
        preSaleSupportAddRequest.setExpecteFinishDate(LocalDate.of(2024,7,30));

        PreSaleSupportAddRequest.PreSaleSupportManifest preSaleManifest = new PreSaleSupportAddRequest.PreSaleSupportManifest();
        preSaleManifest.setSellCategory("系统");
        preSaleManifest.setProductType("系统1");
        preSaleManifest.setBrand("岱鼎");
        preSaleManifest.setMaterialCode("WL99900029939");
        preSaleManifest.setProductName("岱鼎一号");
        preSaleManifest.setExpirationDateEnd(LocalDate.of(2024,6,30));
        preSaleManifest.setFeeTotal(new BigDecimal("1000"));
        preSaleManifest.setDeviseFee(new BigDecimal("800"));
        preSaleManifest.setMaterialFee(new BigDecimal("100"));
        preSaleManifest.setProduceFee(new BigDecimal("50"));
        preSaleManifest.setImplementFee(new BigDecimal("10"));
        preSaleManifest.setPackageFee(new BigDecimal("5"));
        preSaleManifest.setOtherFee(new BigDecimal("10"));
        preSaleManifest.setQuantity(1);
        preSaleManifest.setRdDay(1);
        preSaleManifest.setSupplyDay(1);
        preSaleManifest.setProduceDay(2);
        preSaleManifest.setImplementDay(3);
        preSaleManifest.setDeliveryAddress("22333rrrff");

        //模块
        PreSaleSupportAddRequest.PreSaleSupportDisposition preSaleSupportDisposition = new PreSaleSupportAddRequest.PreSaleSupportDisposition();
        preSaleSupportDisposition.setMaterialCode("WL-090987655");
        preSaleSupportDisposition.setMaterialName("物料名222");
        preSaleSupportDisposition.setLength("10cm");
        preSaleSupportDisposition.setWidth("20cm");
        preSaleSupportDisposition.setHeight("30cm");
        preSaleSupportDisposition.setWeight("10kg");
        preSaleSupportDisposition.setPackageType(1);
        preSaleSupportDisposition.setSupportDay(30);
        preSaleSupportDisposition.setRdDesignDay(31);
        preSaleSupportDisposition.setPidUrls(Arrays.asList("sd"));
        preSaleSupportDisposition.setTelUrls(Arrays.asList("ff"));

        PreSaleSupportAddRequest.PreSaleSupportTree preSaleSupportTree = new PreSaleSupportAddRequest.PreSaleSupportTree();
        preSaleSupportTree.setMaterialCode("WL-09098765688");
        preSaleSupportTree.setMaterialName("物料名333888");
        PreSaleSupportAddRequest.PreSaleSupportTree preSaleSupportTree1 = new PreSaleSupportAddRequest.PreSaleSupportTree();
        preSaleSupportTree1.setMaterialCode("WL-09098765788");
        preSaleSupportTree1.setMaterialName("物料名444888");
        preSaleSupportTree.setChildList(Arrays.asList(preSaleSupportTree1));

        PreSaleSupportAddRequest.PreSaleSupportModule preSaleSupportModule = new PreSaleSupportAddRequest.PreSaleSupportModule();
        preSaleSupportModule.setModuleType(1);
        preSaleSupportModule.setMaterialName("物料名55588");
        preSaleSupportModule.setMaterialCode("WL-09098765888");
        preSaleSupportModule.setSerialNumber(1);
        preSaleSupportModule.setMaterialType(1);
        PreSaleSupportAddRequest.PreSaleSupportModule preSaleSupportModuleT = new PreSaleSupportAddRequest.PreSaleSupportModule();
        preSaleSupportModuleT.setModuleType(1);
        preSaleSupportModuleT.setMaterialName("物料名666");
        preSaleSupportModuleT.setMaterialCode("WL-090987659");
        preSaleSupportModuleT.setSerialNumber(1);
        preSaleSupportModuleT.setMaterialType(1);
        PreSaleSupportAddRequest.PreSaleSupportModule preSaleSupportModuleTT = new PreSaleSupportAddRequest.PreSaleSupportModule();
        preSaleSupportModuleTT.setModuleType(1);
        preSaleSupportModuleTT.setMaterialName("物料名666");
        preSaleSupportModuleTT.setMaterialCode("WL-090987659");
        preSaleSupportModuleTT.setSerialNumber(1);
        preSaleSupportModuleTT.setMaterialType(2);
        preSaleSupportModuleT.setPreSaleSupportModule(Arrays.asList(preSaleSupportModuleTT));
        preSaleSupportTree.setPreSaleSupportModules(Arrays.asList(preSaleSupportModule,preSaleSupportModuleT));
        preSaleSupportDisposition.setPreSaleSupportTree(preSaleSupportTree);

        preSaleManifest.setPreSaleSupportDisposition(preSaleSupportDisposition);

        preSaleSupportAddRequest.setPreSaleSupportManifests(Arrays.asList(preSaleManifest));

        iDiPreSaleSupportService.insertDiPreSaleSupport(preSaleSupportAddRequest);
    }


    @Test
    public void selectDiPreSaleSupportList(){
        PreSaleSupportListRequest preSaleSupportListRequest = new PreSaleSupportListRequest();
        preSaleSupportListRequest.setPageNum(1);
        preSaleSupportListRequest.setPageSize(10);
        PageWrapper<List<PreSaleSupportListResponse>> listPageWrapper = iDiPreSaleSupportService.selectDiPreSaleSupportList(preSaleSupportListRequest);
        log.info("查询方案支持{}",JSON.toJSONString(listPageWrapper));
    }


    @Test
    public void getSupportInfo(){
        PreSaleSupportDetailResponse preSaleSupportDetailResponse = iDiPreSaleSupportService.selectDiPreSaleSupportById(null,"DYD-ZC240714-0007");
        log.info("获取售前方案支持详细信息{}",JSON.toJSONString(preSaleSupportDetailResponse));
    }

    @Test
    public void getSupportDisposition(){
        PreSaleSupportDetailResponse supportDisposition = iDiPreSaleSupportService.getSupportDisposition(4);
        log.info("方案支持配置{}",JSON.toJSONString(supportDisposition));
    }

    @Test
    public void getModuleDetailList(){
        List<PreSaleDetailModuleResponse> moduleDetailList = iDiPreSaleSupportService.getModuleDetailList(9);
        log.info("获取模块配置集合{}",JSON.toJSONString(moduleDetailList));
    }

    @Test
    public void updateDiPreSaleSupport(){
        PreSaleSupportUpdateRequest preSaleSupportUpdateRequest = new PreSaleSupportUpdateRequest();
        preSaleSupportUpdateRequest.setTelSupportHeader("王老五");
        preSaleSupportUpdateRequest.setId(4);
        preSaleSupportUpdateRequest.setExpecteFinishDate(LocalDate.of(2024,7,30));

       /* PreSaleSupportUpdateRequest.PreSaleSupportManifest preSaleManifest = new PreSaleSupportUpdateRequest.PreSaleSupportManifest();
        preSaleManifest.setSellCategory("系统1");
        preSaleManifest.setProductType("系统1");
        preSaleManifest.setBrand("岱鼎1");
        preSaleManifest.setMaterialCode("WL99900029939");
        preSaleManifest.setProductName("岱鼎一号1");
        preSaleManifest.setExpirationDateEnd(LocalDate.of(2024,6,30));
        preSaleManifest.setFeeTotal(new BigDecimal("1000"));
        preSaleManifest.setDeviseFee(new BigDecimal("800"));
        preSaleManifest.setMaterialFee(new BigDecimal("100"));
        preSaleManifest.setProduceFee(new BigDecimal("50"));
        preSaleManifest.setImplementFee(new BigDecimal("10"));
        preSaleManifest.setPackageFee(new BigDecimal("5"));
        preSaleManifest.setOtherFee(new BigDecimal("10"));
        preSaleManifest.setQuantity(1);
        preSaleManifest.setRdDay(1);
        preSaleManifest.setSupplyDay(1);
        preSaleManifest.setProduceDay(2);
        preSaleManifest.setImplementDay(3);
        preSaleManifest.setDeliveryAddress("22333rrrff111");
        preSaleManifest.setId(4);
        //模块
        PreSaleSupportUpdateRequest.PreSaleSupportDisposition preSaleSupportDisposition = new PreSaleSupportUpdateRequest.PreSaleSupportDisposition();
        preSaleSupportDisposition.setMaterialCode("WL-090987655");
        preSaleSupportDisposition.setMaterialName("物料名2221");
        preSaleSupportDisposition.setLength("10cm");
        preSaleSupportDisposition.setWidth("20cm");
        preSaleSupportDisposition.setHeight("30cm");
        preSaleSupportDisposition.setWeight("10kg");
        preSaleSupportDisposition.setPackageType(1);
        preSaleSupportDisposition.setSupportDay(32);
        preSaleSupportDisposition.setRdDesignDay(33);
        preSaleSupportDisposition.setId(4);


        PreSaleSupportUpdateRequest.PreSaleSupportTree preSaleSupportTree = new PreSaleSupportUpdateRequest.PreSaleSupportTree();
        preSaleSupportTree.setMaterialCode("WL-090987656144555000");
        preSaleSupportTree.setMaterialName("物料名3338888000");
        preSaleSupportTree.setId(9);
        PreSaleSupportUpdateRequest.PreSaleSupportTree preSaleSupportTree1 = new PreSaleSupportUpdateRequest.PreSaleSupportTree();
        preSaleSupportTree1.setMaterialCode("WL-090987657156666000");
        preSaleSupportTree1.setMaterialName("物料名4443333340000");
        preSaleSupportTree1.setId(10);
        PreSaleSupportUpdateRequest.PreSaleSupportTree preSaleSupportTree2 = new PreSaleSupportUpdateRequest.PreSaleSupportTree();
        preSaleSupportTree2.setMaterialCode("WL-090987657156666888000");
        preSaleSupportTree2.setMaterialName("物料名444333334888000");

        PreSaleSupportUpdateRequest.PreSaleSupportTree preSaleSupportTree3 = new PreSaleSupportUpdateRequest.PreSaleSupportTree();
        preSaleSupportTree3.setMaterialCode("WL-09098765715666688800033");
        preSaleSupportTree3.setMaterialName("物料名4443333348880033033");

        preSaleSupportTree.setSubTree(Arrays.asList(preSaleSupportTree1,preSaleSupportTree2,preSaleSupportTree3));

        PreSaleSupportUpdateRequest.PreSaleSupportModule preSaleSupportModule = new PreSaleSupportUpdateRequest.PreSaleSupportModule();
        preSaleSupportModule.setModuleType(1);
        preSaleSupportModule.setMaterialName("物料名555115677700800");
        preSaleSupportModule.setMaterialCode("WL-0909876581100");
        preSaleSupportModule.setSerialNumber(1);
        preSaleSupportModule.setMaterialType(1);
        preSaleSupportModule.setId(8);

        PreSaleSupportUpdateRequest.PreSaleSupportModule preSaleSupportModuleT = new PreSaleSupportUpdateRequest.PreSaleSupportModule();
        preSaleSupportModuleT.setModuleType(1);
        preSaleSupportModuleT.setMaterialName("物料名666115566622223300");
        preSaleSupportModuleT.setMaterialCode("WL-090987659112200");
        preSaleSupportModuleT.setSerialNumber(1);
        preSaleSupportModuleT.setMaterialType(2);
        preSaleSupportModuleT.setId(9);
        PreSaleSupportUpdateRequest.PreSaleSupportModule preSaleSupportModuleTT = new PreSaleSupportUpdateRequest.PreSaleSupportModule();
        preSaleSupportModuleTT.setModuleType(1);
        preSaleSupportModuleTT.setMaterialName("物料名6661155666222233");
        preSaleSupportModuleTT.setMaterialCode("WL-0909876591122");
        preSaleSupportModuleTT.setSerialNumber(1);
        preSaleSupportModuleTT.setMaterialType(2);
        preSaleSupportModuleTT.setId(10);
        PreSaleSupportUpdateRequest.PreSaleSupportModule preSaleSupportModuleTTT = new PreSaleSupportUpdateRequest.PreSaleSupportModule();
        preSaleSupportModuleTTT.setModuleType(1);
        preSaleSupportModuleTTT.setMaterialName("物料名6661155666222233tttt");
        preSaleSupportModuleTTT.setMaterialCode("WL-0909876591122ttt");
        preSaleSupportModuleTTT.setSerialNumber(1);
        preSaleSupportModuleTTT.setMaterialType(2);
        preSaleSupportModuleTTT.setId(null);
        preSaleSupportModuleT.setSubModule(Arrays.asList(preSaleSupportModuleTT,preSaleSupportModuleTTT));
        preSaleSupportTree.setPreSaleSupportModules(Arrays.asList(preSaleSupportModule,preSaleSupportModuleT,preSaleSupportModuleTT));
        preSaleSupportDisposition.setPreSaleSupportTree(preSaleSupportTree);

        preSaleManifest.setPreSaleSupportDisposition(preSaleSupportDisposition);

        preSaleSupportUpdateRequest.setPreSaleSupportManifests(Arrays.asList(preSaleManifest));
*/
        log.info("参数{}",JSON.toJSONString(preSaleSupportUpdateRequest));
        iDiPreSaleSupportService.updateDiPreSaleSupport(preSaleSupportUpdateRequest);
    }


    @Test
    public void lockPreSaleSupport(){
        iDiPreSaleSupportService.lockPreSaleSupport(20);
    }


    @Test
    public void addQuote(){
        PreSaleQuoteAddRequest preSaleQuoteAddRequest = new PreSaleQuoteAddRequest();
        preSaleQuoteAddRequest.setNicheCode("DYD-SJ240731-0019");

        PreSaleQuoteAddRequest.PreSaleQuoteAddDetail preSaleQuoteAddDetail = new PreSaleQuoteAddRequest.PreSaleQuoteAddDetail();
        preSaleQuoteAddDetail.setPreSaleCode("DYD-SQ240807-0002");
        preSaleQuoteAddDetail.setSaleQuote(new BigDecimal("100"));

        PreSaleQuoteAddRequest.PreSaleQuoteAddDetail preSaleQuoteAddDetailT = new PreSaleQuoteAddRequest.PreSaleQuoteAddDetail();
        preSaleQuoteAddDetailT.setPreSaleCode("DYD-SQ240807-0003");
        preSaleQuoteAddDetailT.setDeliveryQuote(new BigDecimal("200"));

        preSaleQuoteAddRequest.setPreSaleQuoteDetailList(Arrays.asList(preSaleQuoteAddDetail,preSaleQuoteAddDetailT));
        preSaleQuoteService.add(preSaleQuoteAddRequest);
    }


    @Test
    public void getInfoTemp(){
        PreSaleQuoteInfoTempRequest preSaleQuoteInfoTempRequest = new PreSaleQuoteInfoTempRequest();
        preSaleQuoteInfoTempRequest.setNicheCode("DYD-SJ240913-0002");
        preSaleQuoteInfoTempRequest.setPreSaleCodes(Arrays.asList("DYD-SQ240913-0004"));
        PreSaleQuoteInfoTempResponse preSaleQuoteInfoTempResponse = preSaleQuoteService.infoTemp(preSaleQuoteInfoTempRequest);
        log.info("临时详情{}",JSON.toJSONString(preSaleQuoteInfoTempResponse));
    }

    @Test
    public void getQuoteListPage(){
        PreSaleQuoteListRequest preSaleQuoteListRequest = new PreSaleQuoteListRequest();
        preSaleQuoteListRequest.setPageNum(1);
        preSaleQuoteListRequest.setPageSize(10);
        PageWrapper<List<PreSaleQuoteListResponse>> quoteListPage = preSaleQuoteService.getQuoteListPage(preSaleQuoteListRequest);
        log.info("报价单分页列表{}",JSON.toJSONString(quoteListPage));
    }

    @Test
    public void editQuote(){
        PreSaleQuoteEditRequest preSaleQuoteEditRequest = new PreSaleQuoteEditRequest();
        preSaleQuoteEditRequest.setId(80);

        PreSaleQuoteEditRequest.PreSaleQuoteEditDetail preSaleQuoteEditDetail = new PreSaleQuoteEditRequest.PreSaleQuoteEditDetail();
        preSaleQuoteEditDetail.setSaleQuote(new BigDecimal("0"));
        preSaleQuoteEditDetail.setDeliveryQuote(new BigDecimal(0));
        preSaleQuoteEditDetail.setId(98);

        PreSaleQuoteEditRequest.PreSaleQuoteEditDetail preSaleQuoteEditDetailT = new PreSaleQuoteEditRequest.PreSaleQuoteEditDetail();
        preSaleQuoteEditDetailT.setSaleQuote(new BigDecimal("0"));
        preSaleQuoteEditDetailT.setDeliveryQuote(new BigDecimal(0));
        preSaleQuoteEditDetailT.setId(99);
        preSaleQuoteEditRequest.setPreSaleQuoteDetailList(Arrays.asList(preSaleQuoteEditDetail,preSaleQuoteEditDetailT));
        preSaleQuoteService.edit(preSaleQuoteEditRequest);
    }

    @Test
    public void infoQuote(){
        PreSaleQuoteInfoResponse info = preSaleQuoteService.info(3,null);
        log.info("报价单详情{}",JSON.toJSONString(info));
    }

    @Test
    public void lockQuote(){

        PreSaleQuoteLockRequest preSaleQuoteLockRequest = new PreSaleQuoteLockRequest();
        preSaleQuoteLockRequest.setId(3);

        PreSaleQuoteLockRequest.PreSaleQuoteAddDetail preSaleQuoteAddDetail = new PreSaleQuoteLockRequest.PreSaleQuoteAddDetail();
        preSaleQuoteAddDetail.setSaleQuote(new BigDecimal("11"));
        preSaleQuoteAddDetail.setDeliveryQuote(new BigDecimal("21"));
        preSaleQuoteAddDetail.setId(1);
        preSaleQuoteLockRequest.setPreSaleQuoteDetailList(Arrays.asList(preSaleQuoteAddDetail));
        preSaleQuoteService.lockQuote(preSaleQuoteLockRequest);
    }


    @Test
    public void updateProjectNo(){
        iDiProjectService.updateProjectNo("DYD-XM240729-0001","DD");
    }

    @Test
    public void getCusListPage(){


        PageWrapper<List<DiMarketingCustomerListResponse>> cusListPage = diMarketingCustomerService.getCusListPage(1, 100, null);
    }

    @Test
    public void selectOrderListByTime(){
        DiOrderListRequest request = new DiOrderListRequest();
        request.setStartTime("2024-12-31 11:00:00");
        request.setEndTime("2024-12-31 12:00:00");
        diOrderService.selectOrderListByTime(request);
    }

}
