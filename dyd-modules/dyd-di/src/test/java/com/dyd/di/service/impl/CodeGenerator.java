package com.dyd.di.service.impl;

import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.OutputFile;
import com.baomidou.mybatisplus.generator.config.rules.DbColumnType;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;
import com.dyd.common.security.entity.BaseEntity;

import java.nio.file.Paths;
import java.security.CodeSource;
import java.sql.Types;
import java.util.Collections;
import java.util.Date;

public class CodeGenerator {
    public static void main(String[] args) {

//        CodeSource codeSource = CodeGenerator.class.getProtectionDomain().getCodeSource();
//        System.out.println(codeSource.getLocation().getPath());
//
//        System.out.println(Paths.get(System.getProperty("user.dir")));
//        if (new Date().getYear() > 0) {
//            return;
//        }

        String modulePath = "\\dyd-modules\\dyd-di";
        FastAutoGenerator.create("**************************************************************************", "liuwujing", "tyt3UWHyBQvrlv")

                .globalConfig(builder -> {
                    builder.author("baomidou") // 设置作者
                            .enableSwagger() // 开启 swagger 模式
                            .disableOpenDir()
                            .outputDir(Paths.get(System.getProperty("user.dir")) + modulePath + "\\src\\main\\java"); // 指定输出目录
                })
                .dataSourceConfig(builder ->
                        builder.typeConvertHandler((globalConfig, typeRegistry, metaInfo) -> {
                            int typeCode = metaInfo.getJdbcType().TYPE_CODE;
                            if (typeCode == Types.SMALLINT) {
                                // 自定义类型转换
                                return DbColumnType.INTEGER;
                            }
                            if (typeCode == Types.TIMESTAMP) {
                                return DbColumnType.DATE;
                            }
                            return typeRegistry.getColumnType(metaInfo);
                        })
                )
                .packageConfig(builder ->
                        builder.parent("com.dyd.di") // 设置父包名
                                .moduleName("matrix") // 设置父包模块名
                                .pathInfo(Collections.singletonMap(OutputFile.xml, Paths.get(System.getProperty("user.dir")) + modulePath + "\\src\\main\\resources\\mapper\\matrix")) // 设置mapperXml生成路径
                )
                .strategyConfig(builder ->
                        builder.addInclude("di_matrix_territory_comparison_group,di_matrix_territory_comparison_product,di_matrix_territory_comparison_detail,di_matrix_territory_comparison_dimension") // 设置需要生成的表名
                                .addTablePrefix("t_", "c_")
                                .entityBuilder().addSuperEntityColumns("create_by", "create_time", "update_by", "update_time")
                                .enableFileOverride()
                                .logicDeleteColumnName("del_flag")

                                .addIgnoreColumns("trace_id")
                                .superClass(BaseEntity.class)// 设置过滤表前缀
                                .enableLombok() // 启用 Lombok
                                .enableTableFieldAnnotation() // 启用字段注解

                                .controllerBuilder()
                                .enableRestStyle()
                                .disable()


                )
                .templateEngine(new FreemarkerTemplateEngine()) // 使用Freemarker引擎模板，默认的是Velocity引擎模板
                .execute();
    }
}
