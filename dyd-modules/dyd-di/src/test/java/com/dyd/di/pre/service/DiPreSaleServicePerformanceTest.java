package com.dyd.di.pre.service;

import com.dyd.di.pre.entity.DiPreSale;
import com.dyd.di.pre.domain.response.PreSaleDetailResponse;
import com.dyd.di.pre.service.impl.DiPreSaleServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.util.StopWatch;

/**
 * 售前方案服务性能测试
 * 用于验证type=4和type=5的性能优化效果
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class DiPreSaleServicePerformanceTest {

    @Autowired
    private IDiPreSaleService diPreSaleService;

    /**
     * 测试type=4的性能优化效果
     */
    @Test
    public void testType4Performance() {
        // 准备测试数据
        Long testPreSaleId = 1L; // 替换为实际的测试数据ID
        
        StopWatch stopWatch = new StopWatch("Type4 Performance Test");
        
        // 测试优化前的性能（如果需要对比）
        stopWatch.start("Original Method");
        try {
            PreSaleDetailResponse response1 = diPreSaleService.selectDiPreSaleById(testPreSaleId, null, 4, false);
            log.info("原始方法查询结果: {}", response1 != null ? "成功" : "失败");
        } catch (Exception e) {
            log.error("原始方法执行异常", e);
        }
        stopWatch.stop();
        
        // 测试优化后的性能
        stopWatch.start("Optimized Method");
        try {
            PreSaleDetailResponse response2 = diPreSaleService.selectDiPreSaleById(testPreSaleId, null, 4, false);
            log.info("优化方法查询结果: {}", response2 != null ? "成功" : "失败");
        } catch (Exception e) {
            log.error("优化方法执行异常", e);
        }
        stopWatch.stop();
        
        log.info("Type=4 性能测试结果:\n{}", stopWatch.prettyPrint());
    }

    /**
     * 测试type=5的性能优化效果
     */
    @Test
    public void testType5Performance() {
        // 准备测试数据
        Long testPreSaleId = 1L; // 替换为实际的测试数据ID
        
        StopWatch stopWatch = new StopWatch("Type5 Performance Test");
        
        // 测试优化前的性能（如果需要对比）
        stopWatch.start("Original Method");
        try {
            PreSaleDetailResponse response1 = diPreSaleService.selectDiPreSaleById(testPreSaleId, null, 5, false);
            log.info("原始方法查询结果: {}", response1 != null ? "成功" : "失败");
        } catch (Exception e) {
            log.error("原始方法执行异常", e);
        }
        stopWatch.stop();
        
        // 测试优化后的性能
        stopWatch.start("Optimized Method");
        try {
            PreSaleDetailResponse response2 = diPreSaleService.selectDiPreSaleById(testPreSaleId, null, 5, false);
            log.info("优化方法查询结果: {}", response2 != null ? "成功" : "失败");
        } catch (Exception e) {
            log.error("优化方法执行异常", e);
        }
        stopWatch.stop();
        
        log.info("Type=5 性能测试结果:\n{}", stopWatch.prettyPrint());
    }

    /**
     * 批量性能测试
     */
    @Test
    public void testBatchPerformance() {
        Long testPreSaleId = 1L; // 替换为实际的测试数据ID
        int testCount = 10; // 测试次数
        
        log.info("开始批量性能测试，测试次数: {}", testCount);
        
        // 测试type=4
        StopWatch type4Watch = new StopWatch("Type4 Batch Test");
        type4Watch.start("Type4 Batch");
        for (int i = 0; i < testCount; i++) {
            try {
                diPreSaleService.selectDiPreSaleById(testPreSaleId, null, 4, false);
            } catch (Exception e) {
                log.error("Type4 第{}次测试异常", i + 1, e);
            }
        }
        type4Watch.stop();
        
        // 测试type=5
        StopWatch type5Watch = new StopWatch("Type5 Batch Test");
        type5Watch.start("Type5 Batch");
        for (int i = 0; i < testCount; i++) {
            try {
                diPreSaleService.selectDiPreSaleById(testPreSaleId, null, 5, false);
            } catch (Exception e) {
                log.error("Type5 第{}次测试异常", i + 1, e);
            }
        }
        type5Watch.stop();
        
        log.info("Type=4 批量测试结果 ({}次):\n{}", testCount, type4Watch.prettyPrint());
        log.info("Type=5 批量测试结果 ({}次):\n{}", testCount, type5Watch.prettyPrint());
        
        // 计算平均时间
        double type4AvgTime = type4Watch.getTotalTimeMillis() / (double) testCount;
        double type5AvgTime = type5Watch.getTotalTimeMillis() / (double) testCount;
        
        log.info("Type=4 平均响应时间: {:.2f}ms", type4AvgTime);
        log.info("Type=5 平均响应时间: {:.2f}ms", type5AvgTime);
    }

    /**
     * 缓存效果测试
     */
    @Test
    public void testCacheEffect() {
        Long testPreSaleId = 1L; // 替换为实际的测试数据ID
        
        log.info("开始缓存效果测试");
        
        StopWatch stopWatch = new StopWatch("Cache Effect Test");
        
        // 第一次查询（冷启动）
        stopWatch.start("First Query (Cold)");
        try {
            PreSaleDetailResponse response1 = diPreSaleService.selectDiPreSaleById(testPreSaleId, null, 4, false);
            log.info("第一次查询结果: {}", response1 != null ? "成功" : "失败");
        } catch (Exception e) {
            log.error("第一次查询异常", e);
        }
        stopWatch.stop();
        
        // 第二次查询（应该使用缓存）
        stopWatch.start("Second Query (Cached)");
        try {
            PreSaleDetailResponse response2 = diPreSaleService.selectDiPreSaleById(testPreSaleId, null, 4, false);
            log.info("第二次查询结果: {}", response2 != null ? "成功" : "失败");
        } catch (Exception e) {
            log.error("第二次查询异常", e);
        }
        stopWatch.stop();
        
        // 第三次查询（继续使用缓存）
        stopWatch.start("Third Query (Cached)");
        try {
            PreSaleDetailResponse response3 = diPreSaleService.selectDiPreSaleById(testPreSaleId, null, 4, false);
            log.info("第三次查询结果: {}", response3 != null ? "成功" : "失败");
        } catch (Exception e) {
            log.error("第三次查询异常", e);
        }
        stopWatch.stop();
        
        log.info("缓存效果测试结果:\n{}", stopWatch.prettyPrint());
        
        // 分析缓存效果
        long firstQueryTime = stopWatch.getTaskInfo()[0].getTimeMillis();
        long secondQueryTime = stopWatch.getTaskInfo()[1].getTimeMillis();
        long thirdQueryTime = stopWatch.getTaskInfo()[2].getTimeMillis();
        
        double cacheSpeedup = (double) firstQueryTime / secondQueryTime;
        log.info("缓存加速比: {:.2f}x", cacheSpeedup);
        log.info("第一次查询时间: {}ms", firstQueryTime);
        log.info("第二次查询时间: {}ms", secondQueryTime);
        log.info("第三次查询时间: {}ms", thirdQueryTime);
    }
}
