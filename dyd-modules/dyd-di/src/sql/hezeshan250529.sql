
ALTER TABLE di_pre_sale_quote
    ADD reason_categories varchar(20) NULL COMMENT '低利润原因大类';


ALTER TABLE di_pre_sale_quote
    ADD situation_description varchar(2000) NULL COMMENT '详细情况说明';


INSERT INTO sys_dict_type (dict_name, dict_type, status, create_by)
VALUES ('低利润原因大类', 'reason_categories', '0', 'admin');

INSERT INTO sys_dict_data (dict_label, dict_value, dict_type)
VALUES ('订单有行业战略意义', '1', 'reason_categories'),
       ('后续有大量潜在订单', '2', 'reason_categories'),
       ('有竞争对手压价', '3', 'reason_categories');


ALTER TABLE di_pre_sale_quote_detail
    ADD single_sale_quote DECIMAL(18, 6) NULL COMMENT '单套报价';

ALTER TABLE di_pre_sale_manifest
    ADD single_sale_quote DECIMAL(18, 6) NULL COMMENT '单套报价';

ALTER TABLE di_pre_sale_quote_detail
    ADD recommended_quotation DECIMAL(18, 6) NULL COMMENT '单套基准推荐报价';

ALTER TABLE di_pre_sale_quote_detail
    ADD remaining_recommended_quotation DECIMAL(18, 6) NULL COMMENT '单套剩余推荐报价';

ALTER TABLE di_pre_sale_quote_detail
    ADD minimum_quotation_amount DECIMAL(18, 6) NULL COMMENT '单方案报价额最小值';

ALTER TABLE di_pre_sale_quote_detail
    ADD maximum_quotation_amount DECIMAL(18, 6) NULL COMMENT '单方案报价额最大值';




CREATE TABLE `di_pre_sale_quote_target` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `sale_bu` varchar(255) DEFAULT NULL COMMENT 'bu',
    `sale_no` varchar(255) DEFAULT NULL COMMENT '销售工号',
    `sale_name` varchar(255) DEFAULT NULL COMMENT '销售',
    `year_target` decimal(18,2) DEFAULT NULL COMMENT '年销售目标',
    `year_gp1` decimal(18,2) DEFAULT NULL COMMENT '年GP1(年度利润目标）',
    PRIMARY KEY (`id`)
) COMMENT='销售目标';

INSERT INTO `di_pre_sale_quote_target` (`id`, `sale_bu`, `sale_no`, `sale_name`, `year_target`, `year_gp1`) VALUES (1, '出口组', 'dyd529', '巴天洵', 3000000.00, 1620000.00);
INSERT INTO `di_pre_sale_quote_target` (`id`, `sale_bu`, `sale_no`, `sale_name`, `year_target`, `year_gp1`) VALUES (2, '出口组', 'dyd556', '费霆', 1000000.00, 540000.00);
INSERT INTO `di_pre_sale_quote_target` (`id`, `sale_bu`, `sale_no`, `sale_name`, `year_target`, `year_gp1`) VALUES (3, '出口组', 'dyd557', '康硕夫', 1000000.00, 540000.00);
INSERT INTO `di_pre_sale_quote_target` (`id`, `sale_bu`, `sale_no`, `sale_name`, `year_target`, `year_gp1`) VALUES (4, '固废组', 'dyd222', '侯成森', 9000000.00, 4000000.00);
INSERT INTO `di_pre_sale_quote_target` (`id`, `sale_bu`, `sale_no`, `sale_name`, `year_target`, `year_gp1`) VALUES (5, '固废组', 'dyd558', '缠振平', 3000000.00, 800000.00);
INSERT INTO `di_pre_sale_quote_target` (`id`, `sale_bu`, `sale_no`, `sale_name`, `year_target`, `year_gp1`) VALUES (6, '干燥组', 'dyd570', '丁林', 3000000.00, 1482000.00);
INSERT INTO `di_pre_sale_quote_target` (`id`, `sale_bu`, `sale_no`, `sale_name`, `year_target`, `year_gp1`) VALUES (7, '干燥组', 'dyd029', '崔亮亮', 10500000.00, 5187000.00);
INSERT INTO `di_pre_sale_quote_target` (`id`, `sale_bu`, `sale_no`, `sale_name`, `year_target`, `year_gp1`) VALUES (8, '干燥组', 'dyd006', '梅广斌', 10500000.00, 5187000.00);
INSERT INTO `di_pre_sale_quote_target` (`id`, `sale_bu`, `sale_no`, `sale_name`, `year_target`, `year_gp1`) VALUES (9, '废气组', 'dyd459', '张翼飞', 5500000.00, 1925000.00);
INSERT INTO `di_pre_sale_quote_target` (`id`, `sale_bu`, `sale_no`, `sale_name`, `year_target`, `year_gp1`) VALUES (10, '废气组', 'dyd383', '朱加辉', 5000000.00, 1662500.00);
INSERT INTO `di_pre_sale_quote_target` (`id`, `sale_bu`, `sale_no`, `sale_name`, `year_target`, `year_gp1`) VALUES (11, '废气组', 'dyd427', '欧才鑫', 6000000.00, 2622000.00);
INSERT INTO `di_pre_sale_quote_target` (`id`, `sale_bu`, `sale_no`, `sale_name`, `year_target`, `year_gp1`) VALUES (12, '废气组', 'dyd406', '安虹拓', 3500000.00, 790500.00);
INSERT INTO `di_pre_sale_quote_target` (`id`, `sale_bu`, `sale_no`, `sale_name`, `year_target`, `year_gp1`) VALUES (13, '涂装组', 'dyd064', '张利国', 8000000.00, 3940000.00);
INSERT INTO `di_pre_sale_quote_target` (`id`, `sale_bu`, `sale_no`, `sale_name`, `year_target`, `year_gp1`) VALUES (14, '涂装组', 'dyd004', '张满意', 4500000.00, 2160000.00);
INSERT INTO `di_pre_sale_quote_target` (`id`, `sale_bu`, `sale_no`, `sale_name`, `year_target`, `year_gp1`) VALUES (15, '涂装组', 'dyd192', '牛庆斌', 2000000.00, 600000.00);
INSERT INTO `di_pre_sale_quote_target` (`id`, `sale_bu`, `sale_no`, `sale_name`, `year_target`, `year_gp1`) VALUES (16, '涂装组', 'dyd012', '陈来', 5000000.00, 2500000.00);
INSERT INTO `di_pre_sale_quote_target` (`id`, `sale_bu`, `sale_no`, `sale_name`, `year_target`, `year_gp1`) VALUES (17, '涂装组', 'dyd018', '马振壮', 7000000.00, 2870000.00);
INSERT INTO `di_pre_sale_quote_target` (`id`, `sale_bu`, `sale_no`, `sale_name`, `year_target`, `year_gp1`) VALUES (18, '涂装组', 'dyd566', '陈佳友', 3000000.00, 1230000.00);
INSERT INTO `di_pre_sale_quote_target` (`id`, `sale_bu`, `sale_no`, `sale_name`, `year_target`, `year_gp1`) VALUES (19, '火焰组', 'dyd059', '刘德强', 36700000.00, 18150000.00);
INSERT INTO `di_pre_sale_quote_target` (`id`, `sale_bu`, `sale_no`, `sale_name`, `year_target`, `year_gp1`) VALUES (20, '火焰组', 'dyd016', '熊添强', 4300000.00, 1850000.00);
INSERT INTO `di_pre_sale_quote_target` (`id`, `sale_bu`, `sale_no`, `sale_name`, `year_target`, `year_gp1`) VALUES (21, '燃配组', 'dyd522', '付大川', 3000000.00, 1280000.00);
INSERT INTO `di_pre_sale_quote_target` (`id`, `sale_bu`, `sale_no`, `sale_name`, `year_target`, `year_gp1`) VALUES (22, '燃配组', 'dyd444', '张超', 5000000.00, 1950000.00);
INSERT INTO `di_pre_sale_quote_target` (`id`, `sale_bu`, `sale_no`, `sale_name`, `year_target`, `year_gp1`) VALUES (23, '燃配组', 'dyd101', '方加强', 7000000.00, 3000000.00);
INSERT INTO `di_pre_sale_quote_target` (`id`, `sale_bu`, `sale_no`, `sale_name`, `year_target`, `year_gp1`) VALUES (24, '燃配组', 'dyd167', '施柳彬', 5000000.00, 1950000.00);
INSERT INTO `di_pre_sale_quote_target` (`id`, `sale_bu`, `sale_no`, `sale_name`, `year_target`, `year_gp1`) VALUES (25, '燃配组', 'dyd011', '汪光成', 7000000.00, 2320000.00);
INSERT INTO `di_pre_sale_quote_target` (`id`, `sale_bu`, `sale_no`, `sale_name`, `year_target`, `year_gp1`) VALUES (26, '船舶组', 'dyd006', '梅广斌', 12000000.00, 5010000.00);
INSERT INTO `di_pre_sale_quote_target` (`id`, `sale_bu`, `sale_no`, `sale_name`, `year_target`, `year_gp1`) VALUES (27, '轻工组', 'dyd017', '刘庆云', 16000000.00, 8000000.00);
INSERT INTO `di_pre_sale_quote_target` (`id`, `sale_bu`, `sale_no`, `sale_name`, `year_target`, `year_gp1`) VALUES (28, '非标组', 'dyd423', '徐建中', 4000000.00, 1760000.00);
INSERT INTO `di_pre_sale_quote_target` (`id`, `sale_bu`, `sale_no`, `sale_name`, `year_target`, `year_gp1`) VALUES (29, '非标组', 'dyd436', '李文豪', 6000000.00, 2700000.00);
INSERT INTO `di_pre_sale_quote_target` (`id`, `sale_bu`, `sale_no`, `sale_name`, `year_target`, `year_gp1`) VALUES (30, '非标组', 'dyd006', '梅广斌', 5000000.00, 2537500.00);
