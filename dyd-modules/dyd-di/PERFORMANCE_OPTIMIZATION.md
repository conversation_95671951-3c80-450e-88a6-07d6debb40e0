# 售前方案查询性能优化文档

## 优化背景

`DiPreSaleServiceImpl.selectDiPreSaleById` 方法在 `type=4` 和 `type=5` 时执行特别慢，影响用户体验。

## 性能问题分析

### 原始问题
1. **getFeeList方法性能瓶颈**：
   - 查询所有相同preCode的方案版本
   - 对每个方案版本都调用getFee方法进行费用计算
   - 涉及大量数据库查询和复杂的费用计算逻辑

2. **queryPreSaleInquiry方法复杂查询**：
   - 多次数据库查询（物料询价、实施询价、生产询价等）
   - 复杂的数据处理和转换逻辑
   - 用户信息的远程调用

3. **缺乏缓存机制**：
   - 重复计算相同的费用信息
   - 没有利用已有的缓存机制

## 优化方案

### 1. 方法重构
- 创建了优化版本的方法：
  - `queryPreSaleBomOptimized` (type=4)
  - `queryPreSaleInquiryOptimized` (type=5)
  - `getFeeListOptimized`

### 2. 缓存优化
- **历史数据缓存**：优先使用历史记录中的费用数据
- **智能缓存判断**：根据方案状态决定是否使用缓存
- **缓存降级**：缓存失败时自动降级到实时计算

### 3. 并行处理优化
- **异步查询**：使用 `CompletableFuture` 并行处理多个查询
- **批量查询**：减少数据库交互次数
- **并行计算**：使用 `parallelStream` 并行处理费用计算

### 4. 数据库查询优化
- **批量查询**：一次性查询多个相关数据
- **减少N+1查询**：合并相关查询
- **索引优化建议**：针对频繁查询字段建议添加索引

### 5. 性能监控
- **PerformanceMonitor工具类**：实时监控方法执行时间
- **慢查询检测**：自动检测超过阈值的查询
- **性能统计**：提供详细的性能分析报告

## 优化效果

### 预期性能提升
- **type=4查询**：预计提升 50-70%
- **type=5查询**：预计提升 60-80%
- **缓存命中时**：提升 80-90%

### 监控指标
- 平均响应时间
- 缓存命中率
- 并发处理能力
- 数据库查询次数

## 使用方法

### 1. 正常使用
```java
// 原有调用方式不变
PreSaleDetailResponse response = diPreSaleService.selectDiPreSaleById(id, null, 4, false);
```

### 2. 性能监控
```java
// 查看性能统计
PerformanceMonitor.printStatistics();

// 获取平均执行时间
double avgTime = PerformanceMonitor.getAverageExecutionTime("methodName");
```

### 3. 测试验证
```java
// 运行性能测试
@Test
public void testPerformance() {
    // 参考 DiPreSaleServicePerformanceTest 类
}
```

## 配置说明

### 1. 缓存配置
- 缓存策略：基于方案状态和历史记录
- 缓存失效：方案状态变更时自动失效
- 降级策略：缓存失败时使用原有逻辑

### 2. 并发配置
- 线程池：使用系统默认的 ForkJoinPool
- 超时设置：异步任务默认超时时间
- 异常处理：异步任务异常时的降级策略

### 3. 监控配置
- 慢查询阈值：默认1000ms
- 统计周期：实时统计
- 日志级别：DEBUG级别记录详细信息

## 注意事项

### 1. 兼容性
- 保持原有API接口不变
- 向后兼容所有现有功能
- 异常情况下自动降级到原有逻辑

### 2. 数据一致性
- 缓存数据与实时数据的一致性检查
- 方案状态变更时的缓存更新
- 并发访问时的数据安全

### 3. 监控告警
- 慢查询自动告警
- 缓存命中率监控
- 异常情况记录和分析

## 部署建议

### 1. 灰度发布
- 先在测试环境验证
- 小流量灰度验证
- 逐步扩大到全量

### 2. 监控观察
- 部署后密切监控性能指标
- 观察缓存命中率
- 关注异常日志

### 3. 回滚准备
- 保留原有方法作为备份
- 准备快速回滚方案
- 监控关键业务指标

## 后续优化方向

### 1. 数据库优化
- 添加必要的索引
- 优化SQL查询语句
- 考虑读写分离

### 2. 缓存优化
- 引入Redis等外部缓存
- 实现分布式缓存
- 缓存预热机制

### 3. 架构优化
- 考虑微服务拆分
- 异步消息处理
- 事件驱动架构

## 联系方式

如有问题或建议，请联系开发团队。
