/*
 * This file was generated by the Gradle 'init' task.
 */

plugins {
    id 'org.springframework.boot'
    id 'jacoco' // Jacoco 插件，用于生成单元测试覆盖率报告
}

apply plugin: 'java' // 显式应用 Java 插件

jar {
    enabled = false
}

def appId = getAppId()
archivesBaseName = appId

private String getAppId() {
    def props = new Properties()
    file('src/main/resources/bootstrap.properties').withInputStream {
        props.load(it)
    }
    String app_id = props['app.id']
    println("read properties " + app_id)

    File file = new File(rootProject.getProjectDir().getAbsolutePath() + "/output/" + app_id)
    println("rootProject " + file.getAbsolutePath())
    project.layout.setBuildDirectory(file)
    return props['app.id']
}

// 👇 Test Task 配置：启用 JUnit Jupiter (5.x) 并开启日志输出
test {
    useJUnitPlatform()
    testLogging {
        events "PASSED", "FAILED", "SKIPPED"
    }
}

// 👇 Jacoco 插件配置
jacoco {
    toolVersion = "0.8.13" // 可选最新版本
}

jacocoTestReport {
    reports {
        xml.required = true  // XML 报告用于 SonarQube 等平台
        xml.destination = file("${project.buildDir}/target/site/jacoco/jacoco.xml")
        html.required = true // HTML 报告便于人工查看
        html.destination = file("${project.buildDir}/target/site/jacoco")
        csv.required = false
    }

    afterEvaluate {
        classDirectories.setFrom(
                files(classDirectories.files.collect {
                    fileTree(dir: it, exclude: [
                            // 可以排除不关心的类（如 model、utils）
                            // 示例："com/example/demo/utils/**",
                    ])
                })
        )
    }
}

// 👇 确保每次 test 执行后自动生成 jacoco 报告
test.finalizedBy jacocoTestReport

// 👇 依赖配置
dependencies {
    implementation project(':dyd-common-core')
    implementation project(':dyd-common-datasource')
    implementation project(':dyd-common-datascope')
    implementation project(':dyd-common-swagger')
    implementation project(':dyd-common-log')
    implementation project(':dyd-common-security')
    implementation project(':dyd-api-dbc')
    implementation project(':dyd-api-di')
    implementation project(':dyd-api-data-exchange')
    implementation project(':dyd-api-system')
    implementation project(':dyd-jiandaoyun')
    implementation project(':dyd-erp')
    implementation project(':dyd-gaode')
    implementation project(':dyd-email')
    implementation project(':dyd-dingtalk')
    implementation project(':dyd-common-es')

    implementation("org.springframework.cloud:spring-cloud-starter")
    implementation("org.springframework.cloud:spring-cloud-starter-openfeign")
    implementation("org.springframework.cloud:spring-cloud-starter-loadbalancer")

    implementation("org.springframework.boot:spring-boot-starter-web")
    implementation("org.springframework.boot:spring-boot-starter-actuator")
    implementation("org.springframework.boot:spring-boot-starter-validation")
    implementation("org.springframework.boot:spring-boot-starter-freemarker")

    implementation("com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-discovery")
    implementation("com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-config")
    implementation("com.alibaba.cloud:spring-cloud-starter-alibaba-sentinel")

    implementation("jakarta.activation:jakarta.activation-api:2.1.3")
    implementation('jakarta.servlet:jakarta.servlet-api:6.1.0')
    implementation("com.dydtec.base:oss-api:0.0.1.RELEASE")
    implementation("com.dydtec.infras:core-model:0.0.1.RELEASE")
    implementation("com.dydtec.infras:core-lib:0.0.1.RELEASE")
    implementation("org.springdoc:springdoc-openapi-ui:1.6.15")

    implementation("org.springframework.boot:spring-boot-starter-jdbc")

    implementation("org.commonmark:commonmark:0.22.0")

//    implementation("commons-lang:commons-lang:2.6")
//    implementation("com.google.guava:guava:33.2.1-jre")
    implementation("org.springframework.data:spring-data-elasticsearch:4.4.18")
    implementation("com.baomidou:mybatis-plus-boot-starter:3.5.7")
    implementation("com.github.yulichang:mybatis-plus-join-boot-starter:1.4.13")
    implementation("com.github.pagehelper:pagehelper:6.1.0")

    implementation("com.dydtec.base:camunda-api:0.0.18.RELEASE")
    implementation("org.xhtmlrenderer:flying-saucer-pdf:9.9.0")
    implementation 'org.apache.groovy:groovy-all:4.0.24'

    implementation libs.mysql.mysql.connector.java
    implementation libs.com.microsoft.sqlserver.sqljdbc4
    implementation libs.org.apache.httpcomponents.httpclient
    implementation libs.org.apache.httpcomponents.httpmime
    implementation libs.com.aliyun.dysmsapi20170525
    implementation libs.org.redisson.redisson.spring.boot.starter
    implementation libs.org.redisson.redisson.spring.data.v21
    implementation(libs.org.apache.cxf.cxf.rt.frontend.jaxws)
    implementation libs.org.apache.cxf.cxf.rt.transports.http.jetty
    implementation libs.com.itextpdf.itextpdf
    implementation libs.com.itextpdf.itext.asian
    implementation libs.com.alibaba.fastjson2.fastjson2
    implementation libs.org.mapstruct.mapstruct
    implementation libs.org.apache.rocketmq.rocketmq.client
    implementation libs.org.apache.rocketmq.rocketmq.spring.boot.starter

    implementation('xml-apis:xml-apis:1.4.01')
    implementation('xerces:xercesImpl:2.12.0')

    testImplementation("org.springframework.boot:spring-boot-starter-test") {
        exclude group: 'org.junit.vintage', module: 'junit-vintage-engine'
    }
    testImplementation libs.junit.junit
    testImplementation 'com.baomidou:mybatis-plus-generator:3.5.7'
}