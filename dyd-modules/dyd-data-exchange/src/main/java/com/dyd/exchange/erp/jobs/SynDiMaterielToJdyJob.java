package com.dyd.exchange.erp.jobs;

import com.dyd.exchange.erp.service.SynDiMaterielToJdyService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * di同步简道云料品档案阳阳
 */
@Component
@Slf4j
public class SynDiMaterielToJdyJob extends IJobHandler {

    @Autowired
    private SynDiMaterielToJdyService synDiMaterielToJdyService;

    @XxlJob("SynDiMaterielToJdyJob")
    @Override
    public void execute() throws Exception {
        String jobParam = XxlJobHelper.getJobParam();
        if(StringUtil.isNotEmpty(jobParam)){
            for(String materialNo:jobParam.split(",")){
                synDiMaterielToJdyService.synDiMaterielToJdy(1,300,materialNo);
            }
        }else{
            synDiMaterielToJdyService.synDiMaterielToJdy(1,300,null);
        }

    }
}
