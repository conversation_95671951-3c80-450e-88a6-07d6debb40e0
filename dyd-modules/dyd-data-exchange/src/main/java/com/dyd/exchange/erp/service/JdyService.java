package com.dyd.exchange.erp.service;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.dyd.common.core.domain.R;
import com.dyd.common.core.utils.DateUtils;
import com.dyd.common.core.utils.file.FileUtils;
import com.dyd.di.api.RemoteDiService;
import com.dyd.di.api.model.*;
import com.dyd.di.api.model.vo.DiMarketingContactsVo;
import com.dyd.di.api.model.vo.DiMarketingFollowVo;
import com.dyd.erp.erpEnum.RcvSrcDocTypeEnum;
import com.dyd.exchange.erp.domain.erp.FinanceSendItem;
import com.dyd.exchange.erp.domain.erp.FinanceStandardSalesU9Response;
import com.dyd.exchange.erp.domain.erp.SmSoResponse;
import com.dyd.exchange.erp.mapper.ErpU9Mapper;
import com.dyd.exchange.model.BaseDefineValueResponse;
import com.dyd.exchange.model.PurchaseResponse;
import com.dyd.exchange.util.HttpDownloadUtil;
import com.dyd.exchange.util.JdyFileUtils;
import com.dyd.jdy.JdyClient;
import com.dyd.jdy.bean.clue.ClueDataResponse;
import com.dyd.jdy.bean.common.*;
import com.dyd.jdy.bean.customer.JdyCustomerRecordResponse;
import com.dyd.jdy.bean.jdy.JdyCommonDto;
import com.dyd.jdy.bean.jdy.request.*;
import com.dyd.jdy.bean.jdy.response.*;
import com.dyd.jdy.bean.quotation.JdyRpQuotationDataRequest;
import com.dyd.jdy.bean.quotation.JdyRpQuotationDataResponse;
import com.dyd.jdy.bean.yinan.request.ExportQuotationRequest;
import com.dyd.jdy.bean.yinan.response.ExportQuotationResponse;
import com.dyd.jdy.constant.JdyConstants;
import com.dyd.jdy.service.JdyCommonService;
import com.dyd.system.api.RemoteDictDataService;
import com.dyd.system.api.RemoteSysService;
import com.dyd.system.api.RemoteUserService;
import com.dyd.system.api.domain.SysDeptResponse;
import com.dyd.system.api.domain.SysDictData;
import com.dyd.exchange.erp.conver.MaterielConver;
import com.dyd.exchange.erp.domain.jdy.request.CrmCustomerPushDataReq;
import com.dyd.system.api.domain.SysUser;
import com.dydtec.base.oss.api.dto.request.OssUploadFileReq;
import com.dydtec.base.oss.api.dto.response.OssUploadDTO;
import com.dydtec.base.oss.api.service.ResourceOssRemote;
import com.dydtec.base.oss.api.service.ResourceUploadService;
import com.dydtec.infras.core.base.bean.BaseResponse;
import lombok.extern.slf4j.Slf4j;
import net.bytebuddy.asm.Advice;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.WeekFields;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.dyd.jdy.constant.JdyConstants.JDY_EQ;


@Slf4j
@Service
public class JdyService {

    @Autowired
    private RemoteDiService remoteDiService;

    @Autowired
    private RemoteDictDataService remoteDictDataService;

    @Autowired
    private JdyCommonService jdyCommonService;

    @Autowired
    private MaterielConver materielConver;

    @Autowired
    private ResourceOssRemote resourceOssRemote;

    @Autowired
    private RemoteUserService remoteUserService;

    @Autowired
    private RemoteSysService remoteSysService;

    @Autowired
    private ErpU9Mapper erpU9Mapper;

    @Autowired
    private JdyClient jdyClient;



    /**
     * 同步订单数据
     */
    public void syncOrderBase(Long dataId){

        String now = "2024-02-01 00:00:00";

        String end = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        List<FinanceStandardSalesU9Response> financeStandardSalesU9s = erpU9Mapper.selectSmSOOrders(dataId,now,end,null);
        for(FinanceStandardSalesU9Response financeStandardSalesU9Response:financeStandardSalesU9s){
            //查询应收款
            List<JdyFinanceOrderCollectionPlanQueryResponse.PlanData> planDataList = new ArrayList<>();
            int limit = 20;
            JdyFinanceOrderCollectionPlanQueryRequest.Field fieldTTT = new JdyFinanceOrderCollectionPlanQueryRequest.Field();
            fieldTTT.setField("_widget_1617264711816");
            fieldTTT.setValue(Arrays.asList(financeStandardSalesU9Response.getOrderNo()));
            fieldTTT.setMethod(JDY_EQ);
            //查询应收数据
            jdyCommonService.queryJdyFinanceNew(planDataList,null,null,JdyConstants.AND,Arrays.asList(fieldTTT),limit);


            if(CollectionUtil.isNotEmpty(planDataList)) {

                JdyFinanceOrderCollectionPlanQueryResponse.PlanData planData = planDataList.get(0);
                //u9单号
                String orderNo = planData.get_widget_1617264711816();

                JdyOrderBaseUpdateRequest jdyOrderBaseUpdateRequest = new JdyOrderBaseUpdateRequest();

                JdyOrderBaseUpdateRequest.FinanceOrderCollectionPlanData financeOrderCollectionPlanData = new JdyOrderBaseUpdateRequest.FinanceOrderCollectionPlanData();
                financeOrderCollectionPlanData.set_widget_1620719335689(ValueString.builder().value(planData.get_widget_1617264711816()).build());
                log.info("订单号{}", orderNo);
                //查询u9标准销售
                SmSoResponse smSoResponse = erpU9Mapper.selectSmSO(orderNo);
                if (Objects.isNull(smSoResponse)) {
                    continue;
                }

                financeOrderCollectionPlanData.set_widget_1620719335638(ValueString.builder().value(smSoResponse.getSjCode()).build());
                financeOrderCollectionPlanData.set_widget_1620719335965(ValueString.builder().value(smSoResponse.getProjectCode()).build());
                financeOrderCollectionPlanData.set_widget_1630979828223(ValueString.builder().value(smSoResponse.getName()).build());


                CrmBusinessResponse crmBusinessResponse = null;
                if (StringUtils.isNotEmpty(smSoResponse.getSjCode())) {
                    CrmBusinessRequest crmBusinessRequest = new CrmBusinessRequest();
                    crmBusinessRequest.setApp_id("6166529614a97a0008504397");
                    crmBusinessRequest.setEntry_id("020100500000000000000001");
                    crmBusinessRequest.setUrlName(JdyConstants.MORE_QUERY_URL);
                    List<CrmBusinessRequest.Field> fieldList = new ArrayList<>();
                    CrmBusinessRequest.Field field = new CrmBusinessRequest.Field();

                    field.setField("_widget_0201005000001");
                    field.setValue(Arrays.asList(smSoResponse.getSjCode()));
                    field.setMethod("eq");
                    fieldList.add(field);
                    CrmBusinessRequest.Filter filter = new CrmBusinessRequest.Filter();
                    filter.setCond(fieldList);
                    filter.setRel("and");
                    crmBusinessRequest.setFilter(filter);
                    log.info("查询crm商机请求参数:[{}]", com.alibaba.fastjson2.JSON.toJSONString(crmBusinessRequest));
                    crmBusinessResponse = jdyClient.jdyCallV5(crmBusinessRequest);
                    log.info("查询crm商机返回参数:[{}]", com.alibaba.fastjson2.JSON.toJSONString(crmBusinessResponse));

                }
                if (Objects.nonNull(crmBusinessResponse) && CollectionUtil.isNotEmpty(crmBusinessResponse.getData())) {
                    CrmBusinessResponse.CrmBusinessData crmBusinessData = crmBusinessResponse.getData().get(0);
                    financeOrderCollectionPlanData.set_widget_1668585494361(ValueString.builder().value(crmBusinessData.get_widget_0201005000002()).build());
                    financeOrderCollectionPlanData.set_widget_1668585494362(ValueString.builder().value(crmBusinessData.get_widget_1648616102873()).build());

                    financeOrderCollectionPlanData.set_widget_1620719339466(ValueString.builder().value(crmBusinessData.get_widget_1706578047324()).build());
                    financeOrderCollectionPlanData.set_widget_1620719335656(ValueString.builder().value(crmBusinessData.get_widget_1647413184214()).build());
                    financeOrderCollectionPlanData.set_widget_1629342966409(ValueString.builder().value(crmBusinessData.get_widget_1643075620840()).build());
                    financeOrderCollectionPlanData.set_widget_1620719339679(ValueString.builder().value(crmBusinessData.get_widget_1647484540775()).build());
                }
                financeOrderCollectionPlanData.set_widget_1620719337744(ValueString.builder().value(planData.get_widget_1687951469805()).build());

                financeOrderCollectionPlanData.set_widget_1620722037392(ValueString.builder().value(planData.get_widget_1606360345609()).build());
                financeOrderCollectionPlanData.set_widget_1620719337868(ValueString.builder().value(planData.get_widget_1698287072628()).build());
                financeOrderCollectionPlanData.set_widget_1620886517117(ValueString.builder().value(smSoResponse.getCustomerCode()).build());
                financeOrderCollectionPlanData.set_widget_1620719337851(ValueString.builder().value(planData.get_widget_1606360345597()).build());
                financeOrderCollectionPlanData.set_widget_1620886517365(ValueString.builder().value(smSoResponse.getName()).build());

                List<BaseDefineValueResponse> baseDefineValueResponses = erpU9Mapper.selectBaseDefineValue(1001910150336302L, null, smSoResponse.getContractCode());
                if (CollectionUtil.isNotEmpty(baseDefineValueResponses)) {
                    baseDefineValueResponses = baseDefineValueResponses.stream().filter(baseDefineValueResponse -> StringUtils.isNotEmpty(baseDefineValueResponse.getName())).collect(Collectors.toList());
                    if (CollectionUtil.isNotEmpty(baseDefineValueResponses)) {
                        financeOrderCollectionPlanData.set_widget_1656897927232(ValueString.builder().value(baseDefineValueResponses.get(0).getName()).build());
                    }
                }
                BigDecimal contractAmount = planData.get_widget_1688950307210();
                financeOrderCollectionPlanData.set_widget_1620719337635(ValueString.builder().value(Objects.nonNull(contractAmount) ? String.valueOf(contractAmount) : "0").build());
                financeOrderCollectionPlanData.set_widget_1620719340995(ValueString.builder().value(planData.get_widget_1689040389520()).build());
                //
                BigDecimal hke = planDataList.stream().map(JdyFinanceOrderCollectionPlanQueryResponse.PlanData::get_widget_1682401018487).reduce(BigDecimal.ZERO, BigDecimal::add);
                financeOrderCollectionPlanData.set_widget_1620719338455(ValueString.builder().value(Objects.nonNull(hke) ? String.valueOf(hke) : "0").build());
                BigDecimal whke = planDataList.stream().map(JdyFinanceOrderCollectionPlanQueryResponse.PlanData::get_widget_1684822711735).reduce(BigDecimal.ZERO, BigDecimal::add);

                financeOrderCollectionPlanData.set_widget_1620719338601(ValueString.builder().value(Objects.nonNull(whke) ? String.valueOf(whke) : "0").build());
                if (Objects.nonNull(hke) && Objects.nonNull(contractAmount)) {

                    financeOrderCollectionPlanData.set_widget_1669628863333(ValueString.builder().value(String.valueOf(hke.divide(contractAmount, 4, RoundingMode.HALF_UP).setScale(4, RoundingMode.HALF_UP))).build());
                }
                financeOrderCollectionPlanData.set_widget_1620797961674(ValueString.builder().value(planData.get_widget_1689040389521()).build());


                Integer count = erpU9Mapper.selectSoLine(smSoResponse.getSoId());
                financeOrderCollectionPlanData.set_widget_1620888030944(ValueString.builder().value(String.valueOf(count)).build());
                List<FinanceSendItem> orderSendDate = new ArrayList<>();
                if(StringUtils.isNotEmpty(financeStandardSalesU9Response.getPCode())) {
                    orderSendDate = erpU9Mapper.getOrderSendDate(financeStandardSalesU9Response.getPCode(), null, null);
                }
                //u9订单号查
                List<FinanceSendItem> orderSendDateT = erpU9Mapper.getOrderSendDate(financeStandardSalesU9Response.getOrderNo(), null, null);
                if(CollectionUtil.isEmpty(orderSendDate)){
                    orderSendDate = new ArrayList<>();
                    orderSendDate.addAll(orderSendDateT);

                }else{
                    if(CollectionUtil.isNotEmpty(orderSendDateT)){
                        BigDecimal shipQty = orderSendDate.stream().filter(financeSendItem -> !"补货出货".equals(financeSendItem.getDocTypeName())).map(FinanceSendItem::getShipQty).reduce(BigDecimal.ZERO, BigDecimal::add);

                        BigDecimal shipQtyT = orderSendDateT.stream().filter(financeSendItem -> !"补货出货".equals(financeSendItem.getDocTypeName())).map(FinanceSendItem::getShipQty).reduce(BigDecimal.ZERO, BigDecimal::add);

                        if(shipQty.compareTo(shipQtyT) < 0){
                            orderSendDate = orderSendDateT;
                        }
                    }
                }

                if(CollectionUtil.isNotEmpty(orderSendDate)) {
                    BigDecimal shipQty = orderSendDate.stream().filter(financeSendItem -> !"补货出货".equals(financeSendItem.getDocTypeName())).map(FinanceSendItem::getShipQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                    financeOrderCollectionPlanData.set_widget_1620888066589(ValueString.builder().value(String.valueOf(shipQty)).build());

                    if(Objects.nonNull(shipQty) && Objects.nonNull(count) && count != 0){
                        //比例
                        BigDecimal bili = shipQty.divide(new BigDecimal(count), RoundingMode.HALF_UP).setScale(4, RoundingMode.HALF_UP);
                        financeOrderCollectionPlanData.set_widget_1620719338711(ValueString.builder().value(String.valueOf(bili)).build());
                    }
                }

                BigDecimal sceneInstallIncidentalFees = null;
                R<QuoteGpResponse> quoteGpResponseR = remoteDiService.selectGp(smSoResponse.getSjCode());
                if(quoteGpResponseR.isSuccess() && Objects.nonNull(quoteGpResponseR.getData()) && Objects.nonNull(quoteGpResponseR.getData().getSceneInstallIncidentalFees())){
                    sceneInstallIncidentalFees = quoteGpResponseR.getData().getSceneInstallIncidentalFees();
                }
                //类型
                String orderTypeName = smSoResponse.getName();
                if(orderTypeName.contains("项目")){
                    //项目报价
                    List<JdyProjectQuotationResponse.JdyProjectQuotationData> projectQuotationDataList = new ArrayList<>();
                    JdyProjectQuotationRequest.Field fieldTT = new JdyProjectQuotationRequest.Field();
                    fieldTT.setField("_widget_1608688709531");
                    fieldTT.setValue(Arrays.asList(smSoResponse.getSjCode()));
                    fieldTT.setMethod(JdyConstants.JDY_EQ);

                    JdyProjectQuotationRequest.Field fieldTTF = new JdyProjectQuotationRequest.Field();
                    fieldTTF.setField("_widget_1646294612201");
                    fieldTTF.setValue(Arrays.asList("正常"));
                    fieldTTF.setMethod(JdyConstants.JDY_EQ);
                    jdyCommonService.queryJdyProjectQuotation(projectQuotationDataList, null, null, JdyConstants.AND, Arrays.asList(fieldTT,fieldTTF), 10);

                    if (CollectionUtil.isNotEmpty(projectQuotationDataList)) {
                        financeOrderCollectionPlanData.set_widget_1620719336923(ValueString.builder().value(projectQuotationDataList.get(0).get_widget_1608692162428()).build());

                        financeOrderCollectionPlanData.set_widget_1620719336961(ValueString.builder().value(projectQuotationDataList.get(0).get_widget_1608692162428()).build());

                        financeOrderCollectionPlanData.set_widget_1620886516328(ValueString.builder().value(projectQuotationDataList.get(0).get_widget_1608692162428()).build());

                    }
                }else if(orderTypeName.contains("散件")){

                    JdyRpQuotationDataRequest jdyRpQuotationDataRequest = JdyRpQuotationDataRequest.builder()
                            .limit(1)
                            .filter(Filter.builder().rel("and")
                                    .cond(Collections.singletonList(Cond.builder()
                                            .field("_widget_1654159417601")
                                            .method("eq").value(smSoResponse.getSjCode()).build()))
                                    .build())
                            .build();
                    JdyRpQuotationDataResponse jdyRpQuotationDataResponse = jdyClient.jdyCall(jdyRpQuotationDataRequest);

                    if(Objects.nonNull(jdyRpQuotationDataResponse) && CollectionUtil.isNotEmpty(jdyRpQuotationDataResponse.getData())) {
                        //预算
                        financeOrderCollectionPlanData.set_widget_1620719336923(ValueString.builder().value(jdyRpQuotationDataResponse.getData().get(0).get_widget_1654477617455()).build());

                        //实际
                        financeOrderCollectionPlanData.set_widget_1620719336961(ValueString.builder().value(jdyRpQuotationDataResponse.getData().get(0).get_widget_1654477617455()).build());

                        //未领取
                        financeOrderCollectionPlanData.set_widget_1620886516328(ValueString.builder().value(jdyRpQuotationDataResponse.getData().get(0).get_widget_1654477617455()).build());
                    }
                }else if(orderTypeName.contains("出口")){
                    List<ExportQuotationResponse.ExportQuotationData> dataList = new ArrayList<>();
                    ExportQuotationRequest.Field field = new ExportQuotationRequest.Field();
                    field.setField("_widget_1639381050594");
                    field.setValue(Arrays.asList(smSoResponse.getSjCode()));
                    field.setMethod(JdyConstants.JDY_IN);
                    jdyCommonService.queryExportQuotation(dataList,null,null,JdyConstants.AND,Arrays.asList(field),20);
                    if(CollectionUtil.isNotEmpty(dataList)){
                        //预算
                        financeOrderCollectionPlanData.set_widget_1620719336923(ValueString.builder().value(dataList.get(0).get_widget_1639447153147()).build());

                        //实际
                        financeOrderCollectionPlanData.set_widget_1620719336961(ValueString.builder().value(dataList.get(0).get_widget_1639447153147()).build());

                        //未领取
                        financeOrderCollectionPlanData.set_widget_1620886516328(ValueString.builder().value(dataList.get(0).get_widget_1648686732266()).build());

                    }
                }else if(orderTypeName.contains("备件") || orderTypeName.contains("服务")){
                    List<JdyReplacementResponse.JdyReplacementData> dataList = new ArrayList<>();
                    JdyReplacementRequest.Field field = new JdyReplacementRequest.Field();
                    field.setField("_widget_1687154553434");
                    field.setValue(Arrays.asList(smSoResponse.getSjCode().split("-")[0]));
                    field.setMethod(JdyConstants.JDY_IN);
                    jdyCommonService.queryJdyReplacement(dataList,null,null,JdyConstants.AND,Arrays.asList(field),20);
                    if(CollectionUtil.isNotEmpty(dataList)){

                        //预算
                        financeOrderCollectionPlanData.set_widget_1620719336923(ValueString.builder().value(dataList.get(0).get_widget_1687325084790()).build());

                        //实际
                        financeOrderCollectionPlanData.set_widget_1620719336961(ValueString.builder().value(dataList.get(0).get_widget_1687325084790()).build());

                        //未领取
                        financeOrderCollectionPlanData.set_widget_1620886516328(ValueString.builder().value(dataList.get(0).get_widget_1687325084668()).build());

                    }
                }

                //简道云没有查到值，从di取
                if(Objects.isNull(financeOrderCollectionPlanData.get_widget_1620719336923()) && Objects.nonNull(sceneInstallIncidentalFees)){
                    financeOrderCollectionPlanData.set_widget_1620719336923(ValueString.builder().value(String.valueOf(sceneInstallIncidentalFees)).build());

                    financeOrderCollectionPlanData.set_widget_1620719336961(ValueString.builder().value(String.valueOf(sceneInstallIncidentalFees)).build());

                    financeOrderCollectionPlanData.set_widget_1620886516328(ValueString.builder().value(String.valueOf(sceneInstallIncidentalFees)).build());
                }

                jdyOrderBaseUpdateRequest.setData(financeOrderCollectionPlanData);

                //查询订单数据是否存在
                List<JdyOrderBaseResponse.OrderBase> orderBaseList = new ArrayList<>();
                JdyCommonDto.Field fieldT = new JdyCommonDto.Field();
                fieldT.setField("_widget_1620719335689");
                fieldT.setValue(Arrays.asList(orderNo));
                fieldT.setMethod(JDY_EQ);
                jdyCommonService.queryOrderBase(orderBaseList, null, null, JdyConstants.AND, Arrays.asList(fieldT), 10);

                jdyOrderBaseUpdateRequest.setApp_id("609a3e38bca1f5000759c679");
                jdyOrderBaseUpdateRequest.setEntry_id("609a3e7c1299a100076c5bb6");
                if (CollectionUtil.isEmpty(orderBaseList)) {

                    String userId = "";
                    financeOrderCollectionPlanData.set_widget_1728891058237(ValueString.builder().value(planData.get_widget_1685349050213()).build());
                    if (Objects.nonNull(planData.get_widget_1693639205815())) {
                        userId = planData.get_widget_1693639205815().getUsername();

                    } else if (StringUtils.isNotEmpty(planData.get_widget_1685349050213())) {
                        R<SysUser> sysUserR = remoteUserService.userInfo(planData.get_widget_1685349050213());
                        if (sysUserR.isSuccess() && Objects.nonNull(sysUserR.getData())) {
                            userId = sysUserR.getData().getDdUserId();
                        }
                    }
                    financeOrderCollectionPlanData.set_widget_1620719337801(ValueString.builder().value(userId).build());
                    financeOrderCollectionPlanData.set_widget_1620869048573(ValueList.builder().value(Arrays.asList(userId)).build());
                    jdyOrderBaseUpdateRequest.setData(financeOrderCollectionPlanData);
                    jdyOrderBaseUpdateRequest.setUrlName(JdyConstants.ONE_CREATE_URL);
                    jdyClient.jdyCallV5(jdyOrderBaseUpdateRequest);
                } else {

                    jdyOrderBaseUpdateRequest.setData_id(orderBaseList.get(0).get_id());
                    jdyOrderBaseUpdateRequest.setUrlName(JdyConstants.ONE_UPDATE_URL);

                    jdyOrderBaseUpdateRequest.setData(financeOrderCollectionPlanData);
                    jdyClient.jdyCallV5(jdyOrderBaseUpdateRequest);


                }
            }
        }

        /*if(CollectionUtil.isNotEmpty(financeStandardSalesU9s) && financeStandardSalesU9s.size() == 1000){
            FinanceStandardSalesU9Response financeStandardSalesU9Response = financeStandardSalesU9s.stream().sorted(Comparator.comparing(FinanceStandardSalesU9Response::getDataId).reversed()).collect(Collectors.toList()).get(0);
            syncOrderBase(financeStandardSalesU9Response.getDataId());
        }*/
    }

    /**
     * crm客户数据推送
     *
     * @param crmCustomerPushDataReq
     */
    public void pushCrmCustomerData(CrmCustomerPushDataReq.CrmCustomerPushData crmCustomerPushDataReq,Map<String, List<JdyCustomerRecordResponse.FromData>> fromDataMap) {

        //客户编号
        String accountNo = crmCustomerPushDataReq.getAccount_no();
        //客户地区
        String widget_1650934343642 = crmCustomerPushDataReq.get_widget_1650934343642();

        String widget_1647244406261 = crmCustomerPushDataReq.get_widget_1647244406261();

        //找不到则把主要销售塞进去
        R<SysUser> sysUserR = remoteUserService.userInfo(crmCustomerPushDataReq.getCreator().getName().split("-")[0]);

        /*DiMarketingCustomer diMarketingCustomer = new DiMarketingCustomer();
        diMarketingCustomer.setCustomerNo(accountNo);

        diMarketingCustomer.setCompanyName(crmCustomerPushDataReq.getAccount_name());

        R<List<SysDictData>> customerGrade = remoteDictDataService.dictTypeNew("customer_grade");
        Map<String, SysDictData> customerGradeMap = new HashMap<>();
        if( customerGrade.isSuccess() && Objects.nonNull(customerGrade.getData())){
            customerGradeMap = customerGrade.getData().stream().collect(Collectors.toMap(SysDictData::getDictLabel, Function.identity()));
        }
        if(StringUtils.isNotEmpty(crmCustomerPushDataReq.get_widget_1650934344382())){
            if(Objects.nonNull(customerGradeMap.get(crmCustomerPushDataReq.get_widget_1650934344382()))) {
                diMarketingCustomer.setCustomerGrade(customerGradeMap.get(crmCustomerPushDataReq.get_widget_1650934344382()).getDictValue());
            }
        }

        R<List<SysDictData>> customerCategory = remoteDictDataService.dictTypeNew("customer_category");
        Map<String, SysDictData> customerCategoryMap = new HashMap<>();
        if( customerCategory.isSuccess() && Objects.nonNull(customerCategory.getData())){
            customerCategoryMap = customerCategory.getData().stream().collect(Collectors.toMap(SysDictData::getDictLabel, Function.identity()));
        }
        if(StringUtils.isNotEmpty(crmCustomerPushDataReq.get_widget_1650934343932())){
            if(Objects.nonNull(customerCategoryMap.get(crmCustomerPushDataReq.get_widget_1650934343932()))) {
                diMarketingCustomer.setCustomerCategory(customerCategoryMap.get(crmCustomerPushDataReq.get_widget_1650934343932()).getDictValue());
            }
        }

        R<List<SysDictData>> customerValue = remoteDictDataService.dictTypeNew("customer_value");
        Map<String, SysDictData> customerValueMap = new HashMap<>();
        if( customerValue.isSuccess() && Objects.nonNull(customerValue.getData())){
            customerValueMap = customerValue.getData().stream().collect(Collectors.toMap(SysDictData::getDictLabel, Function.identity()));
        }
        if(StringUtils.isNotEmpty(crmCustomerPushDataReq.get_widget_1653545761159())){
            if(Objects.nonNull(customerValueMap.get(crmCustomerPushDataReq.get_widget_1653545761159()))) {
                diMarketingCustomer.setCustomerValue(customerValueMap.get(crmCustomerPushDataReq.get_widget_1653545761159()).getDictValue());
            }
        }

        diMarketingCustomer.setCompanyTaxNumber(crmCustomerPushDataReq.get_widget_1675047001717());

        //行业地位
        R<List<SysDictData>> customerNature = remoteDictDataService.dictTypeNew("customer_nature");
        Map<String, SysDictData> customerNatureMap = new HashMap<>();
        if( customerNature.isSuccess() && Objects.nonNull(customerNature.getData())){
            customerNatureMap = customerNature.getData().stream().collect(Collectors.toMap(SysDictData::getDictLabel, Function.identity()));
        }
        if(StringUtils.isNotEmpty(crmCustomerPushDataReq.get_widget_1630862543415())){
            if(Objects.nonNull(customerNatureMap.get(crmCustomerPushDataReq.get_widget_1630862543415()))) {
                diMarketingCustomer.setCustomerNature(customerNatureMap.get(crmCustomerPushDataReq.get_widget_1630862543415()).getDictValue());
            }
        }


        //查询客户来源
        R<List<SysDictData>> customerSourceResult = remoteDictDataService.dictTypeNew("customer_source");
        Map<String, SysDictData> customerSourceMap = new HashMap<>();
        if(customerSourceResult.isSuccess() && Objects.nonNull(customerSourceResult.getData())){
            List<SysDictData> data = customerSourceResult.getData();
            customerSourceMap = data.stream().collect(Collectors.toMap(SysDictData::getDictLabel, Function.identity()));
        }
        if(Objects.nonNull(customerSourceMap.get(crmCustomerPushDataReq.get_widget_1631071964761()))) {
            diMarketingCustomer.setCustomerSource(customerSourceMap.get(crmCustomerPushDataReq.get_widget_1631071964761()).getDictValue());
        }
        if(Objects.nonNull(crmCustomerPushDataReq.get_widget_1631071964919())){
            diMarketingCustomer.setProvince(crmCustomerPushDataReq.get_widget_1631071964919().getProvince());
            diMarketingCustomer.setCity(crmCustomerPushDataReq.get_widget_1631071964919().getCity());
            diMarketingCustomer.setArea(crmCustomerPushDataReq.get_widget_1631071964919().getDistrict());
            diMarketingCustomer.setDetailedAddress(crmCustomerPushDataReq.get_widget_1631071964919().getDetail());

        }


        R<List<SysDictData>> customerCountry = remoteDictDataService.dictTypeNew("customer_country");
        Map<String, SysDictData> customerCountryMap = new HashMap<>();
        if( customerGrade.isSuccess() && CollectionUtil.isNotEmpty(customerCountry.getData())){
            customerCountryMap = customerCountry.getData().stream().collect(Collectors.toMap(SysDictData::getDictLabel, Function.identity()));
        }

        if(StringUtils.isNotEmpty(widget_1647244406261)){
            if(Objects.nonNull(customerCountryMap.get(widget_1647244406261))){
                diMarketingCustomer.setCountry(customerCountryMap.get(widget_1647244406261).getDictValue());
            }else{
                List<SysDictData> sysDictDataList = customerCountry.getData().stream().filter(sysDictData -> sysDictData.getDictLabel().contains(widget_1647244406261)).collect(Collectors.toList());
                if(CollectionUtil.isNotEmpty(sysDictDataList)){
                    diMarketingCustomer.setCountry(sysDictDataList.get(0).getDictValue());
                }else {
                    diMarketingCustomer.setCountry("1");
                }
            }
        }else{
            if(StringUtils.isNotEmpty(widget_1650934343642) && "大陆".equals(widget_1650934343642)){
                diMarketingCustomer.setCountry("0");
            }else{
                diMarketingCustomer.setCountry("1");
            }

        }



        //行业分类
        R<List<SysDictData>> industryResult = remoteDictDataService.dictTypeNew("industry_classification");
        Map<String, SysDictData> industryMap = new HashMap<>();
        if(industryResult.isSuccess() && Objects.nonNull(industryResult.getData())){
            industryMap = industryResult.getData().stream().collect(Collectors.toMap(SysDictData::getDictLabel, Function.identity()));
        }
        if(Objects.nonNull(industryMap.get(crmCustomerPushDataReq.get_widget_1647240687730()))) {
            diMarketingCustomer.setIndustryClassification(industryMap.get(crmCustomerPushDataReq.get_widget_1647240687730()).getDictValue());
        }

        //客户有效性
        R<List<SysDictData>> customerResult = remoteDictDataService.dictTypeNew("customer_status");
        Map<String, SysDictData> customerMap = new HashMap<>();
        if( customerResult.isSuccess() && Objects.nonNull(customerResult.getData())){
            customerMap = customerResult.getData().stream().collect(Collectors.toMap(SysDictData::getDictLabel, Function.identity()));
        }
        if(Objects.nonNull(customerMap.get(crmCustomerPushDataReq.get_widget_1652950482516()))) {
            diMarketingCustomer.setCustomerStatus(customerMap.get(crmCustomerPushDataReq.get_widget_1652950482516()).getDictValue());
        }

        //行业地位
        R<List<SysDictData>> industryStatusResult = remoteDictDataService.dictTypeNew("industry_status");
        Map<String, SysDictData> industryStatusMap = new HashMap<>();
        if( industryStatusResult.isSuccess() && Objects.nonNull(industryStatusResult.getData())){
            industryStatusMap = industryStatusResult.getData().stream().collect(Collectors.toMap(SysDictData::getDictLabel, Function.identity()));
        }
        if(Objects.nonNull(industryStatusMap.get(crmCustomerPushDataReq.get_widget_1650934344651()))) {
            diMarketingCustomer.setIndustryStatus(industryStatusMap.get(crmCustomerPushDataReq.get_widget_1650934344651()).getDictValue());
        }

        if(StringUtils.isNotEmpty(crmCustomerPushDataReq.get_widget_1647240687749())){
            if("坏账".equals(crmCustomerPushDataReq.get_widget_1647240687749())){
                diMarketingCustomer.setBadDebts("0");
            }else{
                diMarketingCustomer.setBadDebts("1");
            }
        }

        if(Objects.nonNull(crmCustomerPushDataReq.getCharger())) {
            R<SysUser> sysUserR = remoteUserService.userInfo(crmCustomerPushDataReq.getCharger().getName().split("-")[0]);
            if(sysUserR.isSuccess() && Objects.nonNull(sysUserR.getData()) && !"1".equals(sysUserR.getData().getStatus()) && !"1".equals(sysUserR.getData().getDelFlag())){
                diMarketingCustomer.setCustomerOwner(sysUserR.getData().getUserName());
                diMarketingCustomer.setCustomerOwnerDept(String.valueOf(sysUserR.getData().getDeptId()));
            }

        }

        if(Objects.isNull(diMarketingCustomer.getCustomerOwner())){
            if(Objects.nonNull(crmCustomerPushDataReq.get_widget_1669881029150())){

                if(crmCustomerPushDataReq.get_widget_1669881029150().contains("轻工")){
                    diMarketingCustomer.setCustomerOwnerDept("556455453");
                    diMarketingCustomer.setCustomerOwner("dyd017");
                }else if(crmCustomerPushDataReq.get_widget_1669881029150().contains("火焰")){
                    diMarketingCustomer.setCustomerOwnerDept("919610890");
                    diMarketingCustomer.setCustomerOwner("dyd017");
                }else if(crmCustomerPushDataReq.get_widget_1669881029150().contains("燃配")){
                    diMarketingCustomer.setCustomerOwnerDept("*********");
                    diMarketingCustomer.setCustomerOwner("dyd011");
                }else if(crmCustomerPushDataReq.get_widget_1669881029150().contains("固废") || crmCustomerPushDataReq.get_widget_1669881029150().contains("环保")){
                    diMarketingCustomer.setCustomerOwnerDept("912164332");
                    diMarketingCustomer.setCustomerOwner("dyd222");
                }else if(crmCustomerPushDataReq.get_widget_1669881029150().contains("出口")){
                    diMarketingCustomer.setCustomerOwnerDept("*********");
                    diMarketingCustomer.setCustomerOwner("dyd529");
                }else if(crmCustomerPushDataReq.get_widget_1669881029150().contains("干燥")){
                    diMarketingCustomer.setCustomerOwnerDept("912228295");
                    diMarketingCustomer.setCustomerOwner("dyd006");
                }else if(crmCustomerPushDataReq.get_widget_1669881029150().contains("废气")){
                    diMarketingCustomer.setCustomerOwnerDept("912508204");
                    diMarketingCustomer.setCustomerOwner("dyd406");
                }else if(crmCustomerPushDataReq.get_widget_1669881029150().contains("船舶")){
                    diMarketingCustomer.setCustomerOwnerDept("912672168");
                    diMarketingCustomer.setCustomerOwner("dyd006");
                }else if(crmCustomerPushDataReq.get_widget_1669881029150().contains("涂装")){
                    diMarketingCustomer.setCustomerOwnerDept("919903080");
                    diMarketingCustomer.setCustomerOwner("dyd017");
                }
            }
        }


        //找不到则把主要销售塞进去
        R<SysUser> sysUserR = remoteUserService.userInfo(crmCustomerPushDataReq.getCreator().getName().split("-")[0]);
        if(sysUserR.isSuccess() && Objects.nonNull(sysUserR.getData())){
            diMarketingCustomer.setCreateBy(sysUserR.getData().getUserName().split("-")[0]);
        }else{
            diMarketingCustomer.setCreateBy(diMarketingCustomer.getCustomerOwner());
        }

        diMarketingCustomer.setCreateTime(Date.from(DateUtils.utcToLocalDateTime(crmCustomerPushDataReq.getCreateTime()).atZone( ZoneId.systemDefault()).toInstant()));
        diMarketingCustomer.setUpdateTime(Date.from(DateUtils.utcToLocalDateTime(crmCustomerPushDataReq.getUpdateTime()).atZone( ZoneId.systemDefault()).toInstant()));
        StringBuilder shareBy = new StringBuilder();
        if(StringUtils.isNotEmpty(widget_1647244406261)){
            if(widget_1647244406261.contains("美国") || widget_1647244406261.contains("台湾")){
                shareBy.append("dyd556,dyd529,");
            }
        }
        if(CollectionUtil.isNotEmpty(crmCustomerPushDataReq.getCollaborator())){
            for(JdyCommonDto.User user : crmCustomerPushDataReq.getCollaborator()){
                R<SysUser> sysUserRT = remoteUserService.userInfo(user.getUsername());
                if(sysUserR.isSuccess() && Objects.nonNull(sysUserRT.getData()) && !"1".equals(sysUserRT.getData().getStatus()) && !"1".equals(sysUserRT.getData().getDelFlag())){
                    shareBy.append(sysUserRT.getData().getUserName()).append(",");
                }else{


                        if(crmCustomerPushDataReq.get_widget_1669881029150().contains("轻工")){

                            shareBy.append("dyd017").append(",");
                        }else if(crmCustomerPushDataReq.get_widget_1669881029150().contains("火焰")){

                            shareBy.append("dyd017").append(",");
                        }else if(crmCustomerPushDataReq.get_widget_1669881029150().contains("燃配")){

                            shareBy.append("dyd011").append(",");
                        }else if(crmCustomerPushDataReq.get_widget_1669881029150().contains("固废") || crmCustomerPushDataReq.get_widget_1669881029150().contains("环保")){

                            shareBy.append("dyd222").append(",");
                        }else if(crmCustomerPushDataReq.get_widget_1669881029150().contains("出口")){

                            shareBy.append("dyd529").append(",");
                        }else if(crmCustomerPushDataReq.get_widget_1669881029150().contains("干燥")){

                            shareBy.append("dyd006").append(",");
                        }else if(crmCustomerPushDataReq.get_widget_1669881029150().contains("废气")){

                            shareBy.append("dyd406").append(",");
                        }else if(crmCustomerPushDataReq.get_widget_1669881029150().contains("船舶")){

                            shareBy.append("dyd006").append(",");
                        }else if(crmCustomerPushDataReq.get_widget_1669881029150().contains("涂装")){

                            shareBy.append("dyd017").append(",");
                        }
                }
            }
        }
        if(StringUtils.isNotEmpty(shareBy.toString())){
            diMarketingCustomer.setSharedBy(shareBy.toString().substring(0,shareBy.toString().length()-1));
        }
        //校验客户唯一
        R<MarketingCustomerResponse> infoByNo = remoteDiService.getInfoByNo(accountNo);
        if(infoByNo.isSuccess() && Objects.nonNull(infoByNo.getData())){
            diMarketingCustomer.setId(infoByNo.getData().getId());
            DiMarketingCustomer diMarketingCustomerUpdate = new DiMarketingCustomer();
            diMarketingCustomerUpdate.setCustomerOwnerDept(diMarketingCustomer.getCustomerOwnerDept());
            diMarketingCustomerUpdate.setId(infoByNo.getData().getId());
            diMarketingCustomerUpdate.setCustomerOwner(diMarketingCustomer.getCustomerOwner());
            diMarketingCustomerUpdate.setSharedBy(diMarketingCustomer.getSharedBy());
            diMarketingCustomerUpdate.setCountry(diMarketingCustomer.getCountry());
            //remoteDiService.updateCus(diMarketingCustomerUpdate);
            //更新客户联系人
        }else{
            //remoteDiService.add(diMarketingCustomer);
        }*/

        List<JdyCrmContactResponse.JdyCrmContactData> dataListContract = new ArrayList<>();

        //施柳彬的客户同步
        if(Objects.nonNull(crmCustomerPushDataReq.getCharger()) && crmCustomerPushDataReq.getCharger().getUsername().equals("01084852152425878614")) {
            JdyCrmContactRequest.Field field = new JdyCrmContactRequest.Field();
            field.setField("_widget_1638780478597");
            field.setValue(Arrays.asList(accountNo));
            field.setMethod(JDY_EQ);
            jdyCommonService.queryJdyCrmContact(dataListContract, null, null, JdyConstants.AND, Arrays.asList(field), 100);
        }

        if(CollectionUtil.isEmpty(dataListContract)){
            return;
        }


        if(CollectionUtil.isNotEmpty(crmCustomerPushDataReq.getCollaborator())){


            List<DiMarketingContactsVo> diMarketingContactsVos = new ArrayList<>();
            if(StringUtils.isNotEmpty(widget_1647244406261)){
                if(widget_1647244406261.contains("美国") || widget_1647244406261.contains("台湾")){
                    DiMarketingContactsVo diMarketingContactsVo = new DiMarketingContactsVo();
                    diMarketingContactsVo.setBelonging("客户");
                    diMarketingContactsVo.setContactsName("初始化");
                    diMarketingContactsVo.setIsUpdatePhone(false);
                    diMarketingContactsVo.setContactsPhone("<EMAIL>");
                    diMarketingContactsVo.setBusinessId(accountNo);
                    diMarketingContactsVo.setContactsOwner("dyd556");
                    diMarketingContactsVo.setContactsOwnerDept("*********");
                    diMarketingContactsVos.add(diMarketingContactsVo);

                    DiMarketingContactsVo diMarketingContactsVoT = new DiMarketingContactsVo();
                    diMarketingContactsVoT.setBelonging("客户");
                    diMarketingContactsVoT.setContactsName("初始化");
                    diMarketingContactsVo.setIsUpdatePhone(false);
                    diMarketingContactsVoT.setContactsPhone("<EMAIL>");
                    diMarketingContactsVoT.setBusinessId(accountNo);
                    diMarketingContactsVoT.setContactsOwner("dyd529");
                    diMarketingContactsVoT.setContactsOwnerDept("*********");
                    diMarketingContactsVos.add(diMarketingContactsVoT);
                }
            }



            int i = 0;
            for(JdyCommonDto.User user : crmCustomerPushDataReq.getCollaborator()){
                DiMarketingContactsVo diMarketingContactsVo = new DiMarketingContactsVo();
                diMarketingContactsVo.setBelonging("客户");
                diMarketingContactsVo.setContactsName("初始化");
                diMarketingContactsVo.setContactsPhone("<EMAIL>");
                diMarketingContactsVo.setIsUpdatePhone(false);
                if(CollectionUtil.isNotEmpty(dataListContract)){
                    if(dataListContract.size() > i) {

                        diMarketingContactsVo.setIsUpdatePhone(true);
                        diMarketingContactsVo.setContactsPhone(dataListContract.get(i).getPhone());
                        diMarketingContactsVo.setContactsName("初始化:"+dataListContract.get(i).getName());
                        if(StringUtils.isNotEmpty(dataListContract.get(i).get_widget_1734328832479())) {
                            diMarketingContactsVo.setContactsRole(dataListContract.get(i).get_widget_1734328832479());
                        }else{
                            diMarketingContactsVo.setContactsRole(dataListContract.get(i).get_widget_1631072817206());
                        }
                    }
                }
                diMarketingContactsVo.setBusinessId(accountNo);

                R<SysUser> sysUserRT = remoteUserService.userInfo(user.getUsername());
                log.info("请求参数{},返回{}",user.getUsername(),sysUserRT);
                if(sysUserR.isSuccess() && Objects.nonNull(sysUserRT.getData()) && !"1".equals(sysUserRT.getData().getStatus()) && !"1".equals(sysUserRT.getData().getDelFlag())){
                    diMarketingContactsVo.setContactsOwner(sysUserRT.getData().getUserName());
                    diMarketingContactsVo.setContactsOwnerDept(String.valueOf(sysUserRT.getData().getDeptId()));
                }else{

                    if(Objects.nonNull(crmCustomerPushDataReq.get_widget_1669881029150())){
                        R<SysDeptResponse> dept = remoteSysService.getDept(crmCustomerPushDataReq.get_widget_1669881029150());
                        if(dept.isSuccess() && Objects.nonNull(dept.getData())){
                            if(dept.getData().getDeptName().contains("轻工")){
                                diMarketingContactsVo.setContactsOwner("dyd017");
                                diMarketingContactsVo.setContactsOwnerDept("556455453");
                            }else if(dept.getData().getDeptName().contains("火焰")){

                                diMarketingContactsVo.setContactsOwner("dyd017");
                                diMarketingContactsVo.setContactsOwnerDept("919610890");
                            }else if(dept.getData().getDeptName().contains("燃配")){

                                diMarketingContactsVo.setContactsOwner("dyd011");
                                diMarketingContactsVo.setContactsOwnerDept("*********");
                            }else if(dept.getData().getDeptName().contains("固废") || dept.getData().getDeptName().contains("环保")){

                                diMarketingContactsVo.setContactsOwner("dyd222");
                                diMarketingContactsVo.setContactsOwnerDept("912164332");
                            }else if(dept.getData().getDeptName().contains("出口")){

                                diMarketingContactsVo.setContactsOwner("dyd529");
                                diMarketingContactsVo.setContactsOwnerDept("*********");
                            }else if(dept.getData().getDeptName().contains("干燥")){

                                diMarketingContactsVo.setContactsOwner("dyd006");
                                diMarketingContactsVo.setContactsOwnerDept("912228295");
                            }else if(dept.getData().getDeptName().contains("废气")){

                                diMarketingContactsVo.setContactsOwner("dyd406");
                                diMarketingContactsVo.setContactsOwnerDept("912508204");
                            }else if(dept.getData().getDeptName().contains("船舶")){

                                diMarketingContactsVo.setContactsOwner("dyd006");
                                diMarketingContactsVo.setContactsOwnerDept("912672168");
                            }else if(dept.getData().getDeptName().contains("涂装")){

                                diMarketingContactsVo.setContactsOwner("dyd017");
                                diMarketingContactsVo.setContactsOwnerDept("919903080");
                            }

                        }

                    }


                }

                diMarketingContactsVos.add(diMarketingContactsVo);

                i++;
            }


            if(dataListContract.size() > crmCustomerPushDataReq.getCollaborator().size()) {
                for (JdyCrmContactResponse.JdyCrmContactData jdyCrmContactData:dataListContract) {
                    if(dataListContract.size() > i){
                        DiMarketingContactsVo diMarketingContactsVo = new DiMarketingContactsVo();
                        diMarketingContactsVo.setBelonging("客户");
                        diMarketingContactsVo.setIsUpdatePhone(true);
                        diMarketingContactsVo.setContactsPhone(dataListContract.get(i).getPhone());
                        diMarketingContactsVo.setContactsName("初始化:"+dataListContract.get(i).getName());
                        if(StringUtils.isNotEmpty(dataListContract.get(i).get_widget_1734328832479())) {
                            diMarketingContactsVo.setContactsRole(dataListContract.get(i).get_widget_1734328832479());
                        }else{
                            diMarketingContactsVo.setContactsRole(dataListContract.get(i).get_widget_1631072817206());
                        }
                        diMarketingContactsVo.setBusinessId(accountNo);
                        diMarketingContactsVo.setIsUpdatePhone(true);
                        diMarketingContactsVo.setContactsOwner("dyd588");
                        diMarketingContactsVo.setContactsOwnerDept("*********");

                        diMarketingContactsVos.add(diMarketingContactsVo);
                    }

                    i++;
                }
            }

            if(CollectionUtil.isNotEmpty(diMarketingContactsVos)) {
                diMarketingContactsVos.stream().forEach(diMarketingContactsVo -> {
                    if(StringUtils.isEmpty(diMarketingContactsVo.getContactsOwner())){
                        diMarketingContactsVo.setContactsOwner("dyd588");
                        diMarketingContactsVo.setContactsOwnerDept("*********");
                    }
                });
                remoteDiService.synAddCusContact(diMarketingContactsVos);
            }
        }

        /*List<JdyCustomerRecordResponse.FromData> dataList = fromDataMap.get(accountNo);
        if(CollectionUtils.isNotEmpty(dataList)){

            for(JdyCustomerRecordResponse.FromData fromData:dataList){
                DiMarketingFollowVo diMarketingFollowVo = new DiMarketingFollowVo();
                diMarketingFollowVo.setBelonging("客户");
                diMarketingFollowVo.setBusinessId(diMarketingCustomer.getCustomerNo());
                diMarketingFollowVo.setFollowCode(fromData.get_widget_1652923775539());
                String ddName= "";
                if(Objects.nonNull(fromData.get_widget_0201004000006())) {
                    ddName = fromData.get_widget_0201004000006().getName();
                }else {
                    ddName = fromData.getCreator().getName();
                }
                R<SysUser> sysUserRT = remoteUserService.userInfo(ddName.split("-")[0]);
                if(sysUserRT.isSuccess() && Objects.nonNull(sysUserRT.getData())) {
                    diMarketingFollowVo.setFollowUp(sysUserRT.getData().getUserName());
                }
                if(StringUtils.isNotEmpty(fromData.get_widget_0201004000007())) {
                    diMarketingFollowVo.setFollowTime(DateUtils.utcToLocalDateTime(fromData.get_widget_0201004000007()));
                }
                if(StringUtils.isNotEmpty(fromData.get_widget_0201004000004())){
                    diMarketingFollowVo.setFollowTitle("跟进方式-"+fromData.get_widget_0201004000004());
                }
                diMarketingFollowVo.setFollowContent(fromData.get_widget_0201004000005());

                R<SysUser> sysUserRTT = remoteUserService.userInfo(fromData.getCreator().getName().split("-")[0]);
                if(sysUserRTT.isSuccess() && Objects.nonNull(sysUserRTT.getData())){
                    diMarketingFollowVo.setCreateBy(sysUserRTT.getData().getUserName());
                }

                diMarketingFollowVo.setCreateTime(DateUtils.utcToLocalDateTime(fromData.getCreateTime()));

                //跟进附件
                String followFile = "";
                //附件
                if(CollectionUtils.isNotEmpty(fromData.get_widget_1646199010581())){
                    for(JdyCommonDto.JdyUrl jdyUrl:fromData.get_widget_1646199010581()){
                        //BaseRequest<OssQueryPolicyReq> ossQueryPolicyReqBaseRequest = new BaseRequest<>();
                       *//* OssQueryPolicyReq ossQueryPolicyReq = new OssQueryPolicyReq();
                        ossQueryPolicyReq.setFileName(jdyUrl.getName());
                        ossQueryPolicyReq.setSysCode("marketing");*//*
                        *//*ossQueryPolicyReqBaseRequest.setModel(ossQueryPolicyReq);
                        log.info("获取上传的policy请求参数{}", JSON.toJSONString(ossQueryPolicyReqBaseRequest));
                        BaseResponse<OssUploadPolicyDTO> ossUploadPolicyDTOBaseResponse = resourceOssRemote.queryPolicy(ossQueryPolicyReqBaseRequest);
                        log.info("获取上传的policy返回参数{}",JSON.toJSONString(ossUploadPolicyDTOBaseResponse));*//*

                        HttpDownloadUtil.download(jdyUrl.getUrl(),jdyUrl.getName(),"/tmp/"+jdyUrl.getName());
                        //上传
                        OssUploadFileReq ossUploadFileReq = new OssUploadFileReq();
                        ossUploadFileReq.setFilePath("/tmp/"+jdyUrl.getName());
                        ossUploadFileReq.setFileName(jdyUrl.getName());
                        ossUploadFileReq.setSysCode("marketing");
                        log.info("oss上传请求参数{}",JSON.toJSONString(ossUploadFileReq));
                        BaseResponse<OssUploadDTO> upload = new ResourceUploadService(resourceOssRemote).upload(ossUploadFileReq);

                        if(upload.isSuccess()){

                            followFile = followFile+","+upload.getData().getOssKey();
                            FileUtils.deleteFile("/tmp/"+jdyUrl.getName());
                        }
                    }
                }
                diMarketingFollowVo.setFollowFile(followFile);
                log.info("跟进记录{}",JSON.toJSONString(diMarketingFollowVo));
                remoteDiService.addCustomerFollow(diMarketingFollowVo);
            }
        }*/
    }


    /**
     * 定时同步客户
     */
    public void jobSynCUstomer(List<JdyCustomerResponse.JdyCustomerData> dataList, String dataId, List<String> fields, String rel, List<JdyCustomerRequest.Field> cond, int limit){
        dataList.clear();
        jdyCommonService.queryJdyCustomer(dataList,dataId,fields,rel,cond,limit);

        //客户跟进记录返回数据
       /* List<JdyCustomerRecordResponse.FromData> dataListT = new ArrayList<>();
        JdyCommonDto.Field field = new JdyCommonDto.Field();
        field.setValue(dataList.stream().map(JdyCustomerResponse.JdyCustomerData::getAccount_no).collect(Collectors.toList()));
        field.setField("_widget_1652859202546");
        field.setMethod(JdyConstants.JDY_IN);
        jdyCommonService.queryCustomerRecord(dataListT,null,null,JdyConstants.AND,Arrays.asList(field),500);
        Map<String, List<JdyCustomerRecordResponse.FromData>> fromDataMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(dataListT)){
            fromDataMap = dataListT.stream().collect(Collectors.groupingBy(JdyCustomerRecordResponse.FromData::get_widget_1652859202546));
        }*/

        for(JdyCustomerResponse.JdyCustomerData jdyCustomerData:dataList){
            CrmCustomerPushDataReq.CrmCustomerPushData crmCustomerPushDataReq = materielConver.converToCrmCustomer(jdyCustomerData);
            crmCustomerPushDataReq.set_widget_1647244406261(jdyCustomerData.get_widget_1647244406261());
            //施柳彬的客户同步
            if(Objects.nonNull(crmCustomerPushDataReq.getCharger()) && crmCustomerPushDataReq.getCharger().getUsername().equals("01084852152425878614")) {
                pushCrmCustomerData(crmCustomerPushDataReq,null);
            }
        }
        /*dataListT.clear();
        fromDataMap.clear();*/
        if(dataList.size() == limit){
            jobSynCUstomer(dataList,dataList.get(dataList.size()-1).get_id(),fields,rel,cond,limit);
        }

    }

    /**
     * V2 销售市场中心月度绩效目标导入表（事业部维度）
     */
    public JdySaleMonthDeptCreatRequest.JdySaleMonthDeptCreat createMonthDept(String month,String param,String flag){
        String queryMoth = "";
        if(StringUtils.isNotEmpty(month)){
            String queryYear = month.split("-")[0];
            String queryMonth = month.split("-")[1];
            if(Integer.parseInt(queryMonth) < 10){
                queryMoth = queryYear+"-0"+queryMonth;
            }else{
                queryMoth = month;
            }

        }else{
            if(LocalDateTime.now().getMonthValue() == 1){
                queryMoth = (LocalDateTime.now().getYear()-1)+"-12";
            }else{
                queryMoth = LocalDateTime.now().getYear()+"-"+(LocalDateTime.now().getMonthValue()-1);
            }

        }


        JdySaleMonthDeptCreatRequest.JdySaleMonthDeptCreat jdySaleMonthDeptCreat = new JdySaleMonthDeptCreatRequest.JdySaleMonthDeptCreat();

        if("0".equals(flag)) {
            //查询当月
            R<List<DeptRealDateResponse>> deptRealData = remoteDiService.getDeptRealData(queryMoth, param);
            if (deptRealData.isSuccess() && CollectionUtil.isNotEmpty(deptRealData.getData())) {
                List<DeptRealDateResponse> deptRealDateResponses = deptRealData.getData();
                //业绩额
                List<DeptRealDateResponse> yejie = deptRealDateResponses.stream().filter(deptRealDateResponse -> "A.签约额".equals(deptRealDateResponse.getType01()) && "01.签约额(万元)".equals(deptRealDateResponse.getType02())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(yejie)) {
                    jdySaleMonthDeptCreat.set_widget_1732237918127(String.valueOf(yejie.get(0).getValue()));
                }else{
                    jdySaleMonthDeptCreat.set_widget_1732237918127("0");
                }
                //毛利额
                List<DeptRealDateResponse> mle = deptRealDateResponses.stream().filter(deptRealDateResponse -> "B.GP1毛利额".equals(deptRealDateResponse.getType01()) && "01.GP1毛利额(万元)".equals(deptRealDateResponse.getType02())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(mle)) {
                    jdySaleMonthDeptCreat.set_widget_1732237918128(String.valueOf(mle.get(0).getValue()));
                }else{
                    jdySaleMonthDeptCreat.set_widget_1732237918128("0");
                }

                //毛利率
                List<DeptRealDateResponse> mll = deptRealDateResponses.stream().filter(deptRealDateResponse -> "B.GP1毛利额".equals(deptRealDateResponse.getType01()) && "02.GP1毛利率(%)".equals(deptRealDateResponse.getType02())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(mll)) {
                    jdySaleMonthDeptCreat.set_widget_1732237918129(String.valueOf(mll.get(0).getValue()));
                }else{
                    jdySaleMonthDeptCreat.set_widget_1732237918129("0");
                }

                //新增客户数
                List<DeptRealDateResponse> cusCount = deptRealDateResponses.stream().filter(deptRealDateResponse -> "C.客户管理".equals(deptRealDateResponse.getType01()) && "01.新增客户(个)".equals(deptRealDateResponse.getType02())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(cusCount)) {
                    jdySaleMonthDeptCreat.set_widget_1732237918130(String.valueOf(cusCount.get(0).getValue()));
                }else{
                    jdySaleMonthDeptCreat.set_widget_1732237918130("0");
                }

                //商机数
                List<DeptRealDateResponse> nicheCount = deptRealDateResponses.stream().filter(deptRealDateResponse -> "C.客户管理".equals(deptRealDateResponse.getType01()) && "02.客户及商机跟进(条)".equals(deptRealDateResponse.getType02())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(nicheCount)) {
                    jdySaleMonthDeptCreat.set_widget_1732237918131(String.valueOf(nicheCount.get(0).getValue()));
                }else{
                    jdySaleMonthDeptCreat.set_widget_1732237918131("0");
                }

                //项目类
                List<DeptRealDateResponse> nicheProject = deptRealDateResponses.stream().filter(deptRealDateResponse -> "E.销售自拓线索输入数量".equals(deptRealDateResponse.getType01()) && "01.项目类(条)".equals(deptRealDateResponse.getType02())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(nicheProject)) {
                    jdySaleMonthDeptCreat.set_widget_1732237918132(String.valueOf(nicheProject.get(0).getValue()));
                }else{
                    jdySaleMonthDeptCreat.set_widget_1732237918132("0");
                }

                //贸易类
                List<DeptRealDateResponse> nicheMyl = deptRealDateResponses.stream().filter(deptRealDateResponse -> "E.销售自拓线索输入数量".equals(deptRealDateResponse.getType01()) && "02.贸易类(条)".equals(deptRealDateResponse.getType02())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(nicheMyl)) {
                    jdySaleMonthDeptCreat.set_widget_1732237918133(String.valueOf(nicheMyl.get(0).getValue()));
                }else{
                    jdySaleMonthDeptCreat.set_widget_1732237918133("0");
                }

                //备件
                List<DeptRealDateResponse> nicheBj = deptRealDateResponses.stream().filter(deptRealDateResponse -> "E.销售自拓线索输入数量".equals(deptRealDateResponse.getType01()) && "03.备件类(条)".equals(deptRealDateResponse.getType02())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(nicheBj)) {
                    jdySaleMonthDeptCreat.set_widget_1732237918134(String.valueOf(nicheBj.get(0).getValue()));
                }else{
                    jdySaleMonthDeptCreat.set_widget_1732237918134("0");
                }

                List<DeptRealDateResponse> nicheProjectAmount = deptRealDateResponses.stream().filter(deptRealDateResponse -> "F.每周/月新增商机额".equals(deptRealDateResponse.getType01()) && "01.项目类(万元)".equals(deptRealDateResponse.getType02())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(nicheProjectAmount)) {
                    jdySaleMonthDeptCreat.set_widget_1732237918135(String.valueOf(nicheProjectAmount.get(0).getValue()));
                }else{
                    jdySaleMonthDeptCreat.set_widget_1732237918135("0");

                }

                List<DeptRealDateResponse> nicheMyAmount = deptRealDateResponses.stream().filter(deptRealDateResponse -> "F.每周/月新增商机额".equals(deptRealDateResponse.getType01()) && "02.贸易类(万元)".equals(deptRealDateResponse.getType02())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(nicheMyAmount)) {
                    jdySaleMonthDeptCreat.set_widget_1732237918136(String.valueOf(nicheMyAmount.get(0).getValue()));
                }else{
                    jdySaleMonthDeptCreat.set_widget_1732237918136("0");
                }

                List<DeptRealDateResponse> nicheBjAmount = deptRealDateResponses.stream().filter(deptRealDateResponse -> "F.每周/月新增商机额".equals(deptRealDateResponse.getType01()) && "03.备件类(万元)".equals(deptRealDateResponse.getType02())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(nicheBjAmount)) {
                    jdySaleMonthDeptCreat.set_widget_1732237918137(String.valueOf(nicheBjAmount.get(0).getValue()));
                }else{
                    jdySaleMonthDeptCreat.set_widget_1732237918137("0");
                }

                List<DeptRealDateResponse> ysAmount = deptRealDateResponses.stream().filter(deptRealDateResponse -> "H.到期应收".equals(deptRealDateResponse.getType01()) && "01.本期实收(万元-除预付发货)".equals(deptRealDateResponse.getType02())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(ysAmount)) {
                    jdySaleMonthDeptCreat.set_widget_1732237918139(String.valueOf(ysAmount.get(0).getValue()));
                }else{
                    jdySaleMonthDeptCreat.set_widget_1732237918139("0");
                }

                List<DeptRealDateResponse> gp1 = deptRealDateResponses.stream().filter(deptRealDateResponse -> "B.GP1毛利额".equals(deptRealDateResponse.getType01()) && "01.GP1毛利额(万元)".equals(deptRealDateResponse.getType02())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(gp1)) {
                    jdySaleMonthDeptCreat.set_widget_1736486741291(String.valueOf(gp1.get(0).getValue()));
                }else{
                    jdySaleMonthDeptCreat.set_widget_1736486741291("0");
                }

                List<DeptRealDateResponse> gp3 = deptRealDateResponses.stream().filter(deptRealDateResponse -> "B.GP3费用".equals(deptRealDateResponse.getType01()) && "01.GP3-1费用(万元)".equals(deptRealDateResponse.getType02())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(gp3)) {
                    jdySaleMonthDeptCreat.set_widget_1736486741292(String.valueOf(gp3.get(0).getValue()));
                }else{
                    jdySaleMonthDeptCreat.set_widget_1736486741292("0");
                }

                //实际出货额
                List<DeptRealDateResponse> sendAmounts = deptRealDateResponses.stream().filter(deptRealDateResponse -> "D.发货额".equals(deptRealDateResponse.getType01()) && "01.发货额(万元)".equals(deptRealDateResponse.getType02())).collect(Collectors.toList());
                if(CollectionUtil.isNotEmpty(sendAmounts)){
                    jdySaleMonthDeptCreat.set_widget_1748228535666(String.valueOf(sendAmounts.get(0).getValue()));
                }else{
                    jdySaleMonthDeptCreat.set_widget_1748228535666("0");
                }

                if(Objects.nonNull(deptRealDateResponses.get(0).getBusiToOrderRate())) {
                    jdySaleMonthDeptCreat.set_widget_1732237918138(String.valueOf(deptRealDateResponses.get(0).getBusiToOrderRate().multiply(new BigDecimal("100"))));
                }else{
                    jdySaleMonthDeptCreat.set_widget_1732237918138("0");
                }



                /*jdySaleMonthDeptCreat.set_widget_1732237918138(ValueString.builder().value(String.valueOf(deptRealDateResponses.get(0).getBusiToOrderRate())).build());
                jdySaleMonthDeptCreatRequest.setData(jdySaleMonthDeptCreat);
                jdySaleMonthDeptCreatRequest.setApp_id("604877a3d4f7d50007d855fd");
                jdySaleMonthDeptCreatRequest.setEntry_id("666805c356112136f1f2f3bb");

                JdySaleDeptRequest jdySaleDeptRequest = new JdySaleDeptRequest();
                jdySaleDeptRequest.setApp_id("604877a3d4f7d50007d855fd");
                jdySaleDeptRequest.setEntry_id("666805c356112136f1f2f3bb");
                jdySaleDeptRequest.setUrlName(JdyConstants.MORE_QUERY_URL);

                JdySaleDeptRequest.Field field = new JdySaleDeptRequest.Field();
                field.setField("_widget_1718093251976");
                field.setMethod(JDY_EQ);
                field.setValue(Arrays.asList(key));

                JdySaleDeptRequest.Field fieldT = new JdySaleDeptRequest.Field();
                fieldT.setField("_widget_1718093251977");
                fieldT.setMethod(JDY_EQ);
                fieldT.setValue(Arrays.asList(Integer.parseInt(year)));

                JdySaleDeptRequest.Field fieldTT = new JdySaleDeptRequest.Field();
                fieldTT.setField("_widget_1718093251978");
                fieldTT.setMethod(JDY_EQ);
                fieldTT.setValue(Arrays.asList(Integer.parseInt(monthR)));

                JdySaleDeptRequest.Filter filterT = new JdySaleDeptRequest.Filter();
                filterT.setRel(JdyConstants.AND);
                filterT.setCond(Arrays.asList(field, fieldT, fieldTT));
                jdySaleDeptRequest.setFilter(filterT);
                jdySaleDeptRequest.setLimit(10);

                JdySaleDeptResponse jdySaleDeptResponse = jdyClient.jdyCallV5(jdySaleDeptRequest);
                if (Objects.nonNull(jdySaleDeptResponse) && CollectionUtil.isNotEmpty(jdySaleDeptResponse.getData())) {
                    for (JdySaleDeptResponse.JdyProductReportData jdyProductReportData : jdySaleDeptResponse.getData()) {
                        jdySaleMonthDeptCreatRequest.setUrlName(JdyConstants.ONE_UPDATE_URL);
                        jdySaleMonthDeptCreatRequest.setData_id(jdyProductReportData.get_id());
                        jdyClient.jdyCallV5(jdySaleMonthDeptCreatRequest);
                    }
                } else {
                    jdySaleMonthDeptCreatRequest.setUrlName(JdyConstants.ONE_CREATE_URL);
                    jdyClient.jdyCallV5(jdySaleMonthDeptCreatRequest);
                }*/
            }
        }


        if("1".equals(flag)) {
            //销售员业绩
            R<List<DeptRealDateResponse>> salerMonthDept = remoteDiService.getSalerMonthDept(queryMoth,param);
            if (salerMonthDept.isSuccess() && CollectionUtil.isNotEmpty(salerMonthDept.getData())) {
                List<DeptRealDateResponse> deptRealDateResponses = salerMonthDept.getData();
                //业绩额
                List<DeptRealDateResponse> yejie = deptRealDateResponses.stream().filter(deptRealDateResponse -> "A.签约额".equals(deptRealDateResponse.getType01()) && "01.签约额(万元)".equals(deptRealDateResponse.getType02())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(yejie)) {
                    jdySaleMonthDeptCreat.set_widget_1732151176578(String.valueOf(yejie.get(0).getValue()));
                }else{
                    jdySaleMonthDeptCreat.set_widget_1732151176578("0");
                }

                //毛利额
                List<DeptRealDateResponse> mle = deptRealDateResponses.stream().filter(deptRealDateResponse -> "B.GP1毛利额".equals(deptRealDateResponse.getType01()) && "01.GP1毛利额(万元)".equals(deptRealDateResponse.getType02())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(mle)) {
                    jdySaleMonthDeptCreat.set_widget_1732151176585(String.valueOf(mle.get(0).getValue()));
                }else{
                    jdySaleMonthDeptCreat.set_widget_1732151176585("0");
                }

                //毛利率
                List<DeptRealDateResponse> mll = deptRealDateResponses.stream().filter(deptRealDateResponse -> "B.GP1毛利额".equals(deptRealDateResponse.getType01()) && "02.GP1毛利率(%)".equals(deptRealDateResponse.getType02())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(mll)) {
                    jdySaleMonthDeptCreat.set_widget_1732151176586(String.valueOf(mll.get(0).getValue()));
                }else{
                    jdySaleMonthDeptCreat.set_widget_1732151176586("0");
                }

                //新增客户数
                List<DeptRealDateResponse> cusCount = deptRealDateResponses.stream().filter(deptRealDateResponse -> "C.客户管理".equals(deptRealDateResponse.getType01()) && "01.新增客户(个)".equals(deptRealDateResponse.getType02())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(cusCount)) {
                    jdySaleMonthDeptCreat.set_widget_1732151176587(String.valueOf(cusCount.get(0).getValue()));
                }else{
                    jdySaleMonthDeptCreat.set_widget_1732151176587("0");
                }

                //商机数
                List<DeptRealDateResponse> nicheCount = deptRealDateResponses.stream().filter(deptRealDateResponse -> "C.客户管理".equals(deptRealDateResponse.getType01()) && "02.客户及商机跟进(条)".equals(deptRealDateResponse.getType02())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(nicheCount)) {
                    jdySaleMonthDeptCreat.set_widget_1732151176594(String.valueOf(nicheCount.get(0).getValue()));
                }else{
                    jdySaleMonthDeptCreat.set_widget_1732151176594("0");
                }

                //项目类
                List<DeptRealDateResponse> nicheProject = deptRealDateResponses.stream().filter(deptRealDateResponse -> "E.销售自拓线索输入数量".equals(deptRealDateResponse.getType01()) && "01.项目类(条)".equals(deptRealDateResponse.getType02())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(nicheProject)) {
                    jdySaleMonthDeptCreat.set_widget_1732151176588(String.valueOf(nicheProject.get(0).getValue()));
                }else{
                    jdySaleMonthDeptCreat.set_widget_1732151176588("0");
                }

                //贸易类
                List<DeptRealDateResponse> nicheMyl = deptRealDateResponses.stream().filter(deptRealDateResponse -> "E.销售自拓线索输入数量".equals(deptRealDateResponse.getType01()) && "02.贸易类(条)".equals(deptRealDateResponse.getType02())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(nicheMyl)) {
                    jdySaleMonthDeptCreat.set_widget_1732151176595(String.valueOf(nicheMyl.get(0).getValue()));
                }else{
                    jdySaleMonthDeptCreat.set_widget_1732151176595("0");
                }

                //备件
                List<DeptRealDateResponse> nicheBj = deptRealDateResponses.stream().filter(deptRealDateResponse -> "E.销售自拓线索输入数量".equals(deptRealDateResponse.getType01()) && "03.备件类(条)".equals(deptRealDateResponse.getType02())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(nicheBj)) {
                    jdySaleMonthDeptCreat.set_widget_1732151176589(String.valueOf(nicheBj.get(0).getValue()));
                }else{
                    jdySaleMonthDeptCreat.set_widget_1732151176589("0");
                }

                List<DeptRealDateResponse> nicheProjectAmount = deptRealDateResponses.stream().filter(deptRealDateResponse -> "F.每周/月新增商机额".equals(deptRealDateResponse.getType01()) && "01.项目类(万元)".equals(deptRealDateResponse.getType02())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(nicheProjectAmount)) {
                    jdySaleMonthDeptCreat.set_widget_1732151176593(String.valueOf(nicheProjectAmount.get(0).getValue()));
                }else{
                    jdySaleMonthDeptCreat.set_widget_1732151176593("0");
                }

                List<DeptRealDateResponse> nicheMyAmount = deptRealDateResponses.stream().filter(deptRealDateResponse -> "F.每周/月新增商机额".equals(deptRealDateResponse.getType01()) && "02.贸易类(万元)".equals(deptRealDateResponse.getType02())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(nicheMyAmount)) {
                    jdySaleMonthDeptCreat.set_widget_1732151176596(String.valueOf(nicheMyAmount.get(0).getValue()));
                }else{
                    jdySaleMonthDeptCreat.set_widget_1732151176596("0");
                }

                List<DeptRealDateResponse> nicheBjAmount = deptRealDateResponses.stream().filter(deptRealDateResponse -> "F.每周/月新增商机额".equals(deptRealDateResponse.getType01()) && "03.备件类(万元)".equals(deptRealDateResponse.getType02())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(nicheBjAmount)) {
                    jdySaleMonthDeptCreat.set_widget_1732151176597(String.valueOf(nicheBjAmount.get(0).getValue()));
                }else{
                    jdySaleMonthDeptCreat.set_widget_1732151176597("0");
                }

                List<DeptRealDateResponse> ysAmount = deptRealDateResponses.stream().filter(deptRealDateResponse -> "H.到期应收".equals(deptRealDateResponse.getType01()) && "01.本期实收(万元-除预付发货)".equals(deptRealDateResponse.getType02())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(ysAmount)) {
                    jdySaleMonthDeptCreat.set_widget_1732151176599(String.valueOf(ysAmount.get(0).getValue()));
                }else{
                    jdySaleMonthDeptCreat.set_widget_1732151176599("0");
                }

                List<DeptRealDateResponse> gp1 = deptRealDateResponses.stream().filter(deptRealDateResponse -> "B.GP1毛利额".equals(deptRealDateResponse.getType01()) && "01.GP1毛利额(万元)".equals(deptRealDateResponse.getType02())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(gp1)) {
                    jdySaleMonthDeptCreat.set_widget_1736486880393(String.valueOf(gp1.get(0).getValue()));
                }else{
                    jdySaleMonthDeptCreat.set_widget_1736486880393("0");
                }

                List<DeptRealDateResponse> gp3 = deptRealDateResponses.stream().filter(deptRealDateResponse -> "B.GP3费用".equals(deptRealDateResponse.getType01()) && "01.GP3-1费用(万元)".equals(deptRealDateResponse.getType02())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(gp3)) {
                    jdySaleMonthDeptCreat.set_widget_1736486880394(String.valueOf(gp3.get(0).getValue()));
                }else{
                    jdySaleMonthDeptCreat.set_widget_1736486880394("0");
                }

                if(Objects.nonNull(deptRealDateResponses.get(0).getBusiToOrderRate())) {
                    jdySaleMonthDeptCreat.set_widget_1732151176598(String.valueOf(deptRealDateResponses.get(0).getBusiToOrderRate().multiply(new BigDecimal("100"))));
                }else{
                    jdySaleMonthDeptCreat.set_widget_1732151176598("0");

                }

                //实际出货额
                List<DeptRealDateResponse> sendAmounts = deptRealDateResponses.stream().filter(deptRealDateResponse -> "D.发货额".equals(deptRealDateResponse.getType01()) && "01.发货额(万元)".equals(deptRealDateResponse.getType02())).collect(Collectors.toList());
                if(CollectionUtil.isNotEmpty(sendAmounts)){
                    jdySaleMonthDeptCreat.set_widget_1748325430540(String.valueOf(sendAmounts.get(0).getValue()));
                }else{
                    jdySaleMonthDeptCreat.set_widget_1748325430540("0");
                }
            }
        }

        return jdySaleMonthDeptCreat;

    }

    /**
     * 收货清单
     * @param maxId
     */
    public void synReceive(Long maxId){

        //收货单
        List<PurchaseResponse> purchaseResponses = erpU9Mapper.selectPurchase(maxId);
        if(CollectionUtil.isNotEmpty(purchaseResponses)){
            List<Object> docNos = purchaseResponses.stream().map(PurchaseResponse::getDocNo).collect(Collectors.toList());
            List<ReceivingListResponse.ReceivingListData> dataList = new ArrayList<>();
            ReceivingListRequest.Field field = new ReceivingListRequest.Field();
            field.setField("_widget_1618539611161");
            field.setMethod(JdyConstants.JDY_IN);
            field.setValue(docNos);
            jdyCommonService.queryReceiving(dataList,null,null,JdyConstants.AND,Arrays.asList(field),3000);
            Map<String, List<ReceivingListResponse.ReceivingListData>> receMap = new HashMap<>();
            if(CollectionUtil.isNotEmpty(dataList)){
                receMap = dataList.stream().collect(Collectors.groupingBy(ReceivingListResponse.ReceivingListData::get_widget_1618539611161));
            }

            Map<String, List<PurchaseResponse>> purchaseMap = purchaseResponses.stream().collect(Collectors.groupingBy(PurchaseResponse::getDocNo));
            for(String key:purchaseMap.keySet()) {
                if(CollectionUtil.isNotEmpty(receMap.get(key))){
                    continue;
                }
                Map<String, List<PurchaseResponse>> itemMap = purchaseMap.get(key).stream().collect(Collectors.groupingBy(PurchaseResponse::getItemCode));
                for (String itemCode:itemMap.keySet()) {
                    //点收
                    BigDecimal eyeballingQtyTU = itemMap.get(itemCode).stream().map(PurchaseResponse::getEyeballingQtyTU).reduce(BigDecimal.ZERO, BigDecimal::add);

                    //实收
                    BigDecimal arriveAty = itemMap.get(itemCode).stream().map(PurchaseResponse::getArriveAty).reduce(BigDecimal.ZERO, BigDecimal::add);

                    PurchaseResponse purchaseResponse = itemMap.get(itemCode).get(0);

                    ReceivingCreateRequest receivingCreateRequest = new ReceivingCreateRequest();
                    ReceivingCreateRequest.UpdateData updateData = new ReceivingCreateRequest.UpdateData();
                    updateData.set_widget_1618539611161(ValueString.builder().value(purchaseResponse.getDocNo()).build());
                    updateData.set_widget_1618539611148(ValueString.builder().value(purchaseResponse.getCreateTime()).build());
                    updateData.set_widget_1618539611352(ValueString.builder().value(purchaseResponse.getItemCode()).build());
                    updateData.set_widget_1618539611370(ValueString.builder().value(purchaseResponse.getItemName()).build());
                    updateData.set_widget_1618539611388(ValueString.builder().value(purchaseResponse.getSpecs()).build());
                    updateData.set_widget_1618539611406(ValueString.builder().value(purchaseResponse.getSupplierName()).build());
                    updateData.set_widget_1618539611498(ValueString.builder().value(String.valueOf(eyeballingQtyTU)).build());
                    updateData.set_widget_1618539611197(ValueString.builder().value(RcvSrcDocTypeEnum.getDescT(purchaseResponse.getDocType())).build());
                    updateData.set_widget_1618539611214(ValueString.builder().value(purchaseResponse.getPoDocNo()).build());
                    updateData.set_widget_1618539611114(ValueString.builder().value(purchaseResponse.getStatus()).build());
                    updateData.set_widget_1618539611096(ValueString.builder().value(purchaseResponse.getId()).build());
                    updateData.set_widget_1618539611251(ValueString.builder().value(purchaseResponse.getWhName()).build());
                    updateData.set_widget_1618539611424(ValueString.builder().value(purchaseResponse.getSupplierCode()).build());
                    updateData.set_widget_1618539611442(ValueString.builder().value(purchaseResponse.getUomName()).build());
                    updateData.set_widget_1618539611517(ValueString.builder().value(String.valueOf(arriveAty)).build());
                    updateData.set_widget_1618539611179(ValueString.builder().value(purchaseResponse.getProjectCode()).build());

                    //标准采购
                    String id = erpU9Mapper.selectPurchaseData(purchaseResponse.getPoDocNo());
                    if (StringUtils.isNotEmpty(id)) {
                        updateData.set_widget_1618539611318(ValueString.builder().value("标准采购").build());
                    } else if ("8".equals(purchaseResponse.getDocType())) {
                        updateData.set_widget_1618539611318(ValueString.builder().value("普通销售").build());
                    } else {
                        updateData.set_widget_1618539611318(ValueString.builder().value("全程委外采购").build());
                    }
                    updateData.set_widget_1618539611284(ValueString.builder().value(purchaseResponse.getCreateBy()).build());
                    updateData.set_widget_1618539611233(ValueString.builder().value(purchaseResponse.getPurchaseBy()).build());
                    receivingCreateRequest.setData(updateData);
                    receivingCreateRequest.setApp_id("5feae86f5b9a360006d6ffe2");
                    receivingCreateRequest.setEntry_id("60b43c56bdda4b000732c195");
                    receivingCreateRequest.setUrlName(JdyConstants.ONE_CREATE_URL);
                    jdyClient.jdyCallV5(receivingCreateRequest);
                }
            }

            dataList.clear();
            purchaseMap.clear();
            if(purchaseResponses.size() == 500){
                long id = Long.parseLong(purchaseResponses.stream().sorted(Comparator.comparing(PurchaseResponse::getId).reversed()).collect(Collectors.toList()).get(0).getId());
                purchaseResponses.clear();
                synReceive(id);
            }

        }


    }

    /**
     * 线索同步到简道云
     */
    public void synClueToJdy(){
        R<List<ClueToJdyResponse>> cluesR = remoteDiService.synClueToJdy();
        if(cluesR.isSuccess() && CollectionUtil.isNotEmpty(cluesR.getData())){
            for(ClueToJdyResponse clueToJdyResponse:cluesR.getData()){

                JdyCommonDto.Field field = new JdyCommonDto.Field();
                field.setField("_widget_1651801063158");
                field.setValue(Arrays.asList(clueToJdyResponse.getClueNo()));
                field.setMethod(JDY_EQ);
                List<ClueDataResponse.FromData> dataList = new ArrayList<>();
                jdyCommonService.queryClue(dataList,null,null,JdyConstants.AND,Arrays.asList(field),10);
                if(CollectionUtil.isNotEmpty(dataList)){
                    continue;
                }


                ClueUpdateRequest clueUpdateRequest = new ClueUpdateRequest();
                clueUpdateRequest.setApp_id("6166529614a97a0008504397");
                clueUpdateRequest.setEntry_id("62747be68a9dce0007f657b3");
                clueUpdateRequest.setUrlName(JdyConstants.ONE_CREATE_URL);

                ClueUpdateRequest.ClueUpdateData clueUpdateData = new ClueUpdateRequest.ClueUpdateData();
                clueUpdateData.set_widget_1652942994028(ValueString.builder().value(DateUtils.LocalDateTimeToUtc(clueToJdyResponse.getCreateTime())).build());
                clueUpdateData.set_widget_1651801063158(ValueString.builder().value(clueToJdyResponse.getClueNo()).build());
                clueUpdateData.set_widget_1651801063679(ValueString.builder().value(clueToJdyResponse.getCompanyName()).build());
                clueUpdateData.set_widget_1651801063733(ValueString.builder().value(clueToJdyResponse.getContactsName()).build());
                clueUpdateData.set_widget_1651801063517(ValueString.builder().value(clueToJdyResponse.getClueOwner()).build());
                clueUpdateData.set_widget_1652148010740(ValueString.builder().value(clueToJdyResponse.getClueSource()).build());
                clueUpdateData.set_widget_1652147819696(ValueString.builder().value(clueToJdyResponse.getSearchTerms()).build());
                clueUpdateData.set_widget_1700538130030(ValueString.builder().value(clueToJdyResponse.getKeyword()).build());
                clueUpdateRequest.setData(clueUpdateData);
                jdyClient.jdyCallV5(clueUpdateRequest);
            }
        }
    }

    /**
     * 创建Md1.2机械外形图
     */
    public void createMD1point2(){

        //查询Di数据
        R<List<PreSaleAppearanceResponse>> appearanceR = remoteDiService.queryProjectPreSale();
        if(!appearanceR.isSuccess() || CollectionUtil.isEmpty(appearanceR.getData())){
            return;
        }

        JdyMd1point2CreateRequest jdyMd1point2CreateRequest = new JdyMd1point2CreateRequest();
        jdyMd1point2CreateRequest.setApp_id("5fcdbd9db0390800077ca015");
        jdyMd1point2CreateRequest.setEntry_id("5ff6fbdad9f0b60006858592");

        for(PreSaleAppearanceResponse preSaleAppearanceResponse:appearanceR.getData()) {

            if(preSaleAppearanceResponse.getProjectCode().startsWith("YXX")){
                continue;
            }

            //根据项目号查询MD1.2
            List<JdyMD1point2QueryResponse.JdyMD1point2Query> dataList = new ArrayList<>();
            JdyCommonDto.Field field = new JdyCommonDto.Field();
            field.setField("_widget_1617774553241");
            field.setValue(Arrays.asList(preSaleAppearanceResponse.getProjectCode()));
            field.setMethod(JDY_EQ);
            jdyCommonService.jdyMD1point2Query(dataList,null,null,JdyConstants.AND,Arrays.asList(field),10);


            for(String materialCode:preSaleAppearanceResponse.getMaterialCodes()) {
                JdyMd1point2CreateRequest.JdyData jdyData = new JdyMd1point2CreateRequest.JdyData();
                //项目号
                jdyData.set_widget_1617774553241(ValueString.builder().value(preSaleAppearanceResponse.getProjectCode()).build());

                //商机编号
                jdyData.set_widget_1617774553243(ValueString.builder().value(preSaleAppearanceResponse.getNicheCode()).build());

                //客户名称
                jdyData.set_widget_1617774553245(ValueString.builder().value(preSaleAppearanceResponse.getCompanyName()).build());

                //机械设计工程师
                jdyData.set_widget_1743472055916(ValueString.builder().value(preSaleAppearanceResponse.getMechineDesignUser()).build());


                List<String> appearanceUrls = new ArrayList<>();
                for (OssPreSale ossPreSale : preSaleAppearanceResponse.getPreSaleAppearanceUrls()) {
                    Map<String, String> stringStringMap = JdyFileUtils.uploadFile("5fcdbd9db0390800077ca015", "5ff6fbdad9f0b60006858592", ossPreSale.getShowUrl(), ossPreSale.getMimeName());
                    if (MapUtils.isNotEmpty(stringStringMap) && StringUtils.isNotEmpty(stringStringMap.get("up_key"))) {
                        appearanceUrls.add(stringStringMap.get("up_key"));
                        jdyMd1point2CreateRequest.setTransaction_id(stringStringMap.get("transaction_id"));
                    }
                }
                //系统外形图（PDF+DWG）
                jdyData.set_widget_1621492191516(ValueT.<List<String>>builder().value(appearanceUrls).build());

                if (CollectionUtil.isNotEmpty(dataList)) {
                    jdyMd1point2CreateRequest.setData_id(dataList.get(0).get_id());
                    jdyMd1point2CreateRequest.setUrlName(JdyConstants.ONE_UPDATE_URL);
                } else {
                    //系统物料号
                    jdyData.set_widget_1621492191429(ValueString.builder().value(materialCode).build());
                    jdyMd1point2CreateRequest.setUrlName(JdyConstants.ONE_CREATE_URL);
                }
                jdyMd1point2CreateRequest.setData(jdyData);
                jdyClient.jdyCallV5(jdyMd1point2CreateRequest);
            }
        }
    }

}
