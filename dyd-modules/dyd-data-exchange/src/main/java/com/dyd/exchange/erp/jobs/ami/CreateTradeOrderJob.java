package com.dyd.exchange.erp.jobs.ami;

import com.dyd.ami.api.RemoteAmiService;
import com.dyd.common.core.utils.StringUtils;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * 同步贸易类订单清单
 */
@Component
public class CreateTradeOrderJob extends IJobHandler {

    @Autowired
    private RemoteAmiService remoteAmiService;


    @Async
    @XxlJob("CreateTradeOrderJob")
    @Override
    public void execute() throws Exception {
        String jobParam = XxlJobHelper.getJobParam();
        if(StringUtils.isNotEmpty(jobParam)){
            for(String str:jobParam.split(",")){
                remoteAmiService.createTradeOrder(str);
            }
        }else{
            remoteAmiService.createTradeOrder(null);
        }

    }
}
